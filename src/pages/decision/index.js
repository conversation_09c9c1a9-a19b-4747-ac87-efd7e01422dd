import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI-Powered Decision Analysis | FunBlocks AI Tools</title>
        <meta name="description" content="Make better decisions with FunBlocks AI's decision analysis assistant. Get comprehensive, data-driven insights for business, personal, and team choices. Reduce bias and save time." />
        <meta name="keywords" content="AI decision analysis, decision making, strategic planning, risk assessment, outcome analysis, business decisions, personal choices, team decisions, rational conclusions, cognitive bias, data-driven insights, systematic evaluation" />
      </Head>
      <MainFrame app={APP_TYPE.decision} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['decision', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
