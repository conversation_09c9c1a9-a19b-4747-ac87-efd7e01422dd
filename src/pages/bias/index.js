import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI LogicLens: Critical Thinking & Bias Detection | FunBlocks AI Tools</title>
        <meta name="description" content="Analyze arguments, detect cognitive biases & logical fallacies with AI LogicLens. Improve critical thinking for academic papers, business reports, news & daily discussions." />
        <meta name="keywords" content="AI LogicLens, critical thinking, logical fallacy detection, cognitive bias detection, argumentation analysis, bias analysis, logic analysis, academic research, business analysis, news analysis, decision making, FunBlocks AI" />
      </Head>
      <MainFrame app={APP_TYPE.bias} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['logic', 'common'])),
    },
  }
}

// export async function getServerSideProps({ params, locale }) {
//   const initial_items = await fetchShowcases(APP_TYPE.mindmap, 'book', 20);

//   return {
//     props: {
//       ...(await serverSideTranslations(locale, ['common'])),
//       initial_showcases: initial_items
//     }
//   }
// }
