import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';
import { useTranslation } from 'next-i18next';

export default function Home({ initial_showcases }) {
  const { basePath } = getConfig().publicRuntimeConfig;
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.funblocks.net';
  const canonicalUrl = `${siteUrl}${basePath}/mindmap`;
  const { t } = useTranslation('mindmap');

  // Schema.org structured data for rich results
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "FunBlocks AI Mindmap",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "AI-powered mind mapping tool that transforms complex information into clear, visual mind maps with one click.",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "625"
    }
  };

  // FAQ structured data for rich results
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": t('mindmap_faq_1_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('mindmap_faq_1_a')
        }
      },
      {
        "@type": "Question",
        "name": t('mindmap_faq_2_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('mindmap_faq_2_a')
        }
      },
      {
        "@type": "Question",
        "name": t('mindmap_faq_3_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('mindmap_faq_3_a')
        }
      },
      {
        "@type": "Question",
        "name": t('mindmap_faq_4_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('mindmap_faq_4_a')
        }
      },
      {
        "@type": "Question",
        "name": t('mindmap_faq_5_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('mindmap_faq_5_a')
        }
      }
    ]
  };

  return (
    <>
      <Head>
        <title>FunBlocks AI Mindmap: AI-Powered Mind Mapping Tool | Create Visual Mind Maps in One Click</title>
        <meta name="description" content="FunBlocks AI Mindmap transforms complex information into clear, visual mind maps with one click. Perfect for students, professionals, researchers, and educators. Boost creativity, enhance learning, and organize knowledge effortlessly with AI-powered mind mapping." />
        <meta name="keywords" content="AI mind map, mind mapping tool, AI-powered mind map, visual thinking, knowledge management, brainstorming, project planning, study research, ChatGPT mind map alternative, AI assistant, FunBlocks AI, one-click mind map, mind map generator, visual learning, concept mapping, knowledge organization, educational tool, productivity tool, information visualization, hierarchical organization, cognitive mapping, learning enhancement, creative thinking, idea organization, visual notes" />
        <link rel="canonical" href={canonicalUrl} />
        <meta property="og:title" content="FunBlocks AI Mindmap: Create Visual Mind Maps in One Click | AI-Powered Mind Mapping Tool" />
        <meta property="og:description" content="Transform complex information into clear, visual mind maps with AI assistance. Perfect for learning, brainstorming, project planning, and knowledge management. Try our free AI mind mapping tool today!" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={canonicalUrl} />
        <meta property="og:image" content={`${siteUrl}/img/portfolio/thumbnails/aitools_mindmap_book_generated.png`} />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="FunBlocks AI Mindmap: AI-Powered Mind Mapping Tool" />
        <meta name="twitter:description" content="Create comprehensive mind maps from any content with one click using AI. Transform books, videos, websites, and more into visual knowledge maps." />
        <meta name="twitter:image" content={`${siteUrl}/img/portfolio/thumbnails/aitools_mindmap_book_generated.png`} />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
        {/* FAQ structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqData) }}
        />
      </Head>
      <MainFrame app={APP_TYPE.mindmap} showcases={initial_showcases} />
    </>
  )
}



export async function getServerSideProps({ locale }) {
  const initial_items = await fetchShowcases(APP_TYPE.mindmap, 'book', 20);

  return {
    props: {
      ...(await serverSideTranslations(locale, ['mindmap', 'common'])),
      initial_showcases: initial_items || []
    }
  }
}

