import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI MarzanoBrain - Educational Course Designer Based on Mar<PERSON>'s Taxonomy | FunBlocks AI</title>
        <meta name="description" content="AI MarzanoBrain helps educators and learners create comprehensive educational plans using <PERSON><PERSON>'s Taxonomy. Generate structured mind maps that transform any topic into a comprehensive learning journey." />
        <meta name="keywords" content="Marzano Taxonomy, educational tool, learning objectives, cognitive levels, AI education, AI tools, AI mind map, AI Brainstorming, teaching strategies, curriculum planning, instructional design, self-system, metacognitive system, cognitive system" />
      </Head>
      <MainFrame app={APP_TYPE.marzano} />
    </>
  )
}

export async function getStaticProps({ locale }) {

  return {
    props: {
      ...(await serverSideTranslations(locale, ['marzano', 'common']))
    }
  }
} 