import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI-Powered Critical Thinking & Question Optimization | FunBlocks AI Tools</title>
        <meta name="description" content="Refine questions, enhance critical thinking, and generate mind maps for research, academic inquiry, and professional problem-solving. Unlock deeper insights with FunBlocks AI." />
        <meta name="keywords" content="AI question optimization, critical thinking, mind mapping, research questions, academic inquiry, problem-solving, strategic planning, FunBlocks AI, question analysis, alternative formulations, inquiry, researchers, students, professionals, educators" />
      </Head>
      <MainFrame app={APP_TYPE.refineQuestion} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['question', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
