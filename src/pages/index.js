import Head from 'next/head'
import { Box } from '@chakra-ui/react'
import MainFrame from '../components/Home/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import SEO from '../components/common/SEO';
import SEOStructuredData from '../components/common/SEOStructuredData';
import Footer from '../components/common/Footer'
import { useTranslation } from 'next-i18next';

export default function Home() {
  const { basePath } = getConfig().publicRuntimeConfig;
  const { t } = useTranslation('common');

  // FAQ data for structured data
  const faqs = [
    {
      question: t('platform_faq_1_q'),
      answer: t('platform_faq_1_a')
    },
    {
      question: t('platform_faq_2_q'),
      answer: t('platform_faq_2_a')
    },
    {
      question: t('platform_faq_3_q'),
      answer: t('platform_faq_3_a')
    },
    {
      question: t('platform_faq_4_q'),
      answer: t('platform_faq_4_a')
    },
    {
      question: t('platform_faq_5_q'),
      answer: t('platform_faq_5_a')
    },
    {
      question: t('platform_faq_6_q'),
      answer: t('platform_faq_6_a')
    },
    {
      question: t('platform_faq_7_q'),
      answer: t('platform_faq_7_a')
    },
    {
      question: t('platform_faq_8_q'),
      answer: t('platform_faq_8_a')
    },
    {
      question: t('platform_faq_9_q'),
      answer: t('platform_faq_9_a')
    },
    {
      question: t('platform_faq_10_q'),
      answer: t('platform_faq_10_a')
    },
    {
      question: t('platform_faq_11_q'),
      answer: t('platform_faq_11_a')
    },
    {
      question: t('platform_faq_12_q'),
      answer: t('platform_faq_12_a')
    },
    {
      question: t('platform_faq_13_q'),
      answer: t('platform_faq_13_a')
    },
    {
      question: t('platform_faq_14_q'),
      answer: t('platform_faq_14_a')
    },
    {
      question: t('platform_faq_15_q'),
      answer: t('platform_faq_15_a')
    }
  ];

  const siteUrl = `https://www.funblocks.net${basePath}`;

  return (
    <>
      <SEO
        title="FunBlocks AI - Visual AI Tools for Learning, Productivity & Creativity | Mind Maps, Infographics & More"
        description="Transform your thinking with FunBlocks AI's visual tools suite: AI Mindmap, MindLadder, Infographic Generator, Slides Creator, Brainstorming Assistant, and more. Our research-backed tools help students, professionals, and creatives visualize complex concepts, enhance learning, and boost productivity through AI-powered visual thinking."
        keywords="AI visual tools, AI mindmap generator, AI infographic maker, AI presentation creator, visual learning tools, educational AI tools, AI brainstorming, mental models, cognitive learning tools, productivity AI, creative thinking tools, AI slides maker, visual thinking, concept visualization, learning frameworks, Bloom's taxonomy, Marzano framework, SOLO taxonomy, AI education tools, professional visualization tools"
        image="/og-image.png"
        url={siteUrl}
        canonical={siteUrl}
      />
      <SEOStructuredData faqs={faqs} />
      <Box>
        <MainFrame />
      </Box>
      {/* <Footer /> */}
    </>
  )
}

// Using getServerSideProps instead of getStaticProps to ensure the latest data is fetched on each request
export async function getServerSideProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
      metaTitle: "FunBlocks AI - Visual AI Tools for Learning, Productivity & Creativity | Mind Maps, Infographics & More",
      metaDescription: "Transform your thinking with FunBlocks AI's visual tools suite: AI Mindmap, MindLadder, Infographic Generator, Slides Creator, Brainstorming Assistant, and more. Our research-backed tools help students, professionals, and creatives visualize complex concepts, enhance learning, and boost productivity through AI-powered visual thinking.",
      metaKeywords: "AI visual tools, AI mindmap generator, AI infographic maker, AI presentation creator, visual learning tools, educational AI tools, AI brainstorming, mental models, cognitive learning tools, productivity AI, creative thinking tools, AI slides maker, visual thinking, concept visualization, learning frameworks, Bloom's taxonomy, Marzano framework, SOLO taxonomy, AI education tools, professional visualization tools",
      metaImage: "/og-image.png",
      metaUrl: `https://www.funblocks.net${getConfig().publicRuntimeConfig.basePath}`
    }
  };
}
