import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI DOKBrain - Educational Course Designer Based on <PERSON>'s Depth of Knowledge (DOK) framework | FunBlocks AI</title>
        <meta name="description" content="AI DOKBrain helps educators and learners create comprehensive educational plans using <PERSON>'s Depth of Knowledge (DOK) framework. Generate structured mind maps that break down complex subjects into progressive learning journeys." />
        <meta name="keywords" content="DOK, Webb DOK, Depth of Knowledge framework, SOLO Taxonomy, educational tool, learning objectives, cognitive levels, AI education, AI tools, AI mind map, AI Brainstorming, teaching strategies, curriculum planning, instructional design, Structure of Observed Learning Outcomes" />
      </Head>
      <MainFrame app={APP_TYPE.dok} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['dok', 'common'])),
    },
  }
} 