import { VStack } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import SEO from '../../components/common/SEO'
import SEOStructuredData from '../../components/common/SEOStructuredData'
import { useTranslation } from 'next-i18next'

export default function Home() {
  const { basePath } = getConfig().publicRuntimeConfig;
  const { t } = useTranslation('graphics');

  // Enhanced FAQ data for structured data
  const faqs = [
    {
      question: t('graphics_faq_1_q', 'What is FunBlocks AI Graphics?'),
      answer: t('graphics_faq_1_a', 'FunBlocks AI Graphics is a tool that uses large language models (LLM) technology to generate interesting and insightful SVG infographic cards. It can help users create shareable content suitable for social media, presentations, or personal notes.')
    },
    {
      question: t('graphics_faq_2_q', 'What can FunBlocks AI Graphics do?'),
      answer: t('graphics_faq_2_a', 'FunBlocks AI Graphics can generate interesting and insightful SVG infographic cards suitable for social media, presentations, personal notes, or just for fun.')
    },
    {
      question: t('graphics_faq_3_q', 'Do I need design skills to use this tool?'),
      answer: t('graphics_faq_3_a', 'Not at all! Our AI handles the design part. You just describe what you want, and the AI will generate professional graphics for you.')
    },
    {
      question: t('graphics_faq_4_q', 'What types of graphics can I create?'),
      answer: t('graphics_faq_4_a', 'You can create various types of visual content, including infographics, flowcharts, process diagrams, data charts, timelines, comparison tables, and more.')
    },
    {
      question: 'How does the AI Infographic Generator work?',
      answer: 'Our AI Infographic Generator analyzes your input text or data and automatically creates visually appealing infographics that effectively communicate your information. It handles layout, color schemes, typography, and visual elements to produce professional-quality results in seconds.'
    },
    {
      question: 'Can I create flowcharts with this AI tool?',
      answer: 'Yes, our AI Flowchart Generator is specifically designed to create clear, professional flowcharts from your process descriptions. Simply describe the process or workflow, and the AI will generate a structured flowchart with proper connections, decision points, and visual hierarchy.'
    },
    {
      question: 'What types of data charts can I create?',
      answer: 'You can create a wide variety of data charts including bar charts, pie charts, line graphs, area charts, scatter plots, bubble charts, radar charts, and more. Our AI analyzes your data and recommends the most effective chart type for your specific information.'
    },
    {
      question: 'Does the tool generate SVG code that I can edit?',
      answer: 'Yes, our AI SVG Code Generator creates editable SVG code that you can further customize in any vector graphics editor. This gives you the flexibility to use our AI-generated graphics as a starting point and then fine-tune them to your exact specifications.'
    },
    {
      question: 'How can AI-generated graphics improve my presentations?',
      answer: 'AI-generated graphics can transform complex data and concepts into clear, engaging visuals that capture attention and improve understanding. Studies show that presentations with quality visuals are 43% more persuasive and information retention increases by up to 65% when paired with relevant images.'
    },
    {
      question: 'Can I use these graphics for commercial purposes?',
      answer: 'Yes, all graphics generated by our AI tool can be used for both personal and commercial purposes. You retain full ownership of the content you create with our tool, making it perfect for business presentations, marketing materials, educational content, and more.'
    },
    {
      question: 'How does the AI ensure my graphics are visually appealing?',
      answer: 'Our AI is trained on design principles including color theory, typography, visual hierarchy, and composition. It automatically applies these principles to create balanced, professional graphics that effectively communicate your information while maintaining visual appeal.'
    },
    {
      question: 'What makes FunBlocks AI Graphics different from other AI design tools?',
      answer: 'FunBlocks AI Graphics specializes in information-rich visual content like infographics, flowcharts, and data visualizations, unlike general AI image generators. Our tool understands the principles of data visualization and information design, creating graphics that are not just visually appealing but also communicate complex information effectively.'
    }
  ];

  // Enhanced Schema.org structured data for rich results
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "FunBlocks AI Graphics Generator",
    "applicationCategory": "DesignApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "Create stunning infographics, flowcharts, data charts, and SVG code in seconds with our AI-powered graphics generator. Transform complex information into engaging visuals without design skills.",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1250"
    },
    "featureList": [
      "AI Infographic Generator",
      "AI Flowchart Generator",
      "Data Chart Creator",
      "SVG Code Generator",
      "Visual Content Creation",
      "Process Diagram Designer",
      "Timeline Generator",
      "Comparison Table Maker",
      "Concept Map Builder",
      "Data Visualization Tool"
    ],
    "applicationSubCategory": "Data Visualization",
    "releaseNotes": "Latest version includes enhanced flowchart capabilities and improved data visualization options",
    "screenshot": "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_swot.png",
    "softwareVersion": "2.0"
  };

  return (
    <>
      <SEO
        title="AI Graphics Generator: Create Infographics, Flowcharts & Data Visualizations | FunBlocks AI"
        description="Transform complex information into stunning infographics, flowcharts, and data visualizations in seconds with our AI Graphics Generator. No design skills needed - perfect for presentations, social media, and reports."
        keywords="AI Infographic generator, AI flowchart generator, data chart creator, AI SVG Code generator, data visualization, visual content, infographic maker, flowchart maker, chart generator, SVG creator, AI graphics tool, process diagram, timeline generator, concept map, comparison table, FunBlocks AI"
        image="/og-image.png"
        url={basePath + "/graphics"}
        canonical={`https://www.funblocks.net${basePath}/graphics`}
      />
      <SEOStructuredData faqs={faqs} />

      {/* Schema.org structured data - JSON-LD format */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
      />

      <MainFrame app={APP_TYPE.graphics} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'graphics'])),
    },
  }
}
