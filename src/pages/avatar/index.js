import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI Avatar Studio - Instantly Create Stunning AI-Generated Avatars | FunBlocks AI Tools</title>
        <meta name="description" content="AI Avatar Generator lets you transform your photos into unique digital art with AI. Choose from anime, cyberpunk, fantasy, comic book, and many more styles. One-click avatar generation for social media, gaming, and more!" />
        <meta name="keywords" content="AI avatar, AI-generated portraits, anime avatar, cyberpunk art, fantasy character, cartoon avatar, digital art, avatar generator, AI profile picture, futuristic design, comic book style, pixel art, chibi avatar, steampunk avatar" />
      </Head>
      <MainFrame app={APP_TYPE.avatar} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['avatar', 'common'])),
    },
  }
} 