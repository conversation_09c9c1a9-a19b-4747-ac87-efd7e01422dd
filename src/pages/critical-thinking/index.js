import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI-Powered Critical Thinking & Decision Making | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock deep analysis and clear decision paths with FunBlocks AI. Master systematic thinking, break down complex problems, and avoid cognitive biases with our AI-powered tools for research, business, and personal development." />
        <meta name="keywords" content="AI critical thinking, decision making, analytical framework, systematic analysis, problem-solving, mind mapping, bias reduction, actionable insights, strategic planning, risk assessment, thesis analysis, research methodology, career planning, life decisions" />
      </Head>
      <MainFrame app={APP_TYPE.criticalThinking} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['criticalthinking', 'common'])),
    },
  }
}

// export async function getServerSideProps({ params, locale }) {
//   const initial_items = await fetchShowcases(APP_TYPE.mindmap, 'book', 20);

//   return {
//     props: {
//       ...(await serverSideTranslations(locale, ['common'])),
//       initial_showcases: initial_items
//     }
//   }
// }
