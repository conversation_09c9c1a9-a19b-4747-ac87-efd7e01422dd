import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title> AI Youtube Video Summarizer | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock knowledge from YouTube videos instantly with FunBlocks AI. Transform lengthy videos into interactive summaries, knowledge maps, and AI-driven insights for proactive learning and time-saving exploration." />
        <meta name="keywords" content="YouTube summary, AI video analysis, knowledge map, interactive learning, AI insights, video summarization tool, YouTube education, active learning, content extraction, key points, main ideas, FunBlocks AI" />
      </Head>
      <MainFrame app={APP_TYPE.youtube} />
      </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['summarizer', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
