import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI Erase: Remove Watermarks Instantly | FunBlocks AI Tools</title>
        <meta name="description" content="Remove watermarks from images instantly with AI Erase. Preserve image quality and restore your photos to their original state with our advanced AI technology. Perfect for designers, marketers, and personal use." />
        <meta name="keywords" content="AI watermark removal, watermark remover, image processing, AI image editing, remove watermarks, photo restoration, clean images, design resources, social media content, marketing materials, FunBlocks AI Tools" />
      </Head>
      <MainFrame app={APP_TYPE.erase} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['erase', 'common'])),
    },
  }
} 