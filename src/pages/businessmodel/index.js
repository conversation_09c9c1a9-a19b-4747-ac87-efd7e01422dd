import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>FunBlocks AI Business Model Analyzer: AI-Powered Strategic Analysis | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock strategic insights with FunBlocks AI Business Model Analyzer. Get rapid, comprehensive analysis and data-driven recommendations to optimize your business model and drive innovation." />
        <meta name="keywords" content="AI business model analysis, strategic analysis tool, business model innovation, competitive advantage, market positioning, revenue stream diversification, operational efficiency, risk mitigation, value proposition, customer segments, revenue model, cost structure, key resources, key activities, channel strategy, strategic partnerships, innovation potential" />
      </Head>
      <MainFrame app={APP_TYPE.businessmodel} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['bma', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
