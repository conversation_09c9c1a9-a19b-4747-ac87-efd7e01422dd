import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI BloomBrain - Educational Tool Based on Bloom's Taxonomy | FunBlocks AI</title>
        <meta name="description" content="AI BloomBrain helps educators and learners create comprehensive learning journeys using <PERSON>'s Taxonomy. Generate structured mind maps, learning objectives, and assessments for any topic." />
        <meta name="keywords" content="Bloom's Taxonomy, educational tool, learning objectives, cognitive levels, AI education, teaching strategies, assessment generator, curriculum planning, AI tools, AI mind map, AI Brainstorming" />
      </Head>
      <MainFrame app={APP_TYPE.bloom} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['bloom', 'common'])),
    },
  }
} 