import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home({ initial_showcases }) {
  return (
    <>
      <Head>
        <title>AI CineMap: AI-Powered Film Analysis & Mind Maps | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock deeper insights into your favorite films with AI CineMap. Generate interactive mind maps analyzing narrative structure, cinematography, themes, and cultural context. Perfect for film enthusiasts, students, and content creators." />
        <meta name="keywords" content="AI film analysis, film mind map, cinema analysis, movie analysis, film studies, film education, narrative structure, cinematography, thematic analysis, cultural context, AI tools, film enthusiasts, content creators, interactive visualization, FunBlocks AI" />
      </Head>
      <MainFrame app={APP_TYPE.movie} showcases={initial_showcases} />
    </>
  )
}

// export async function getStaticProps({ locale }) {
//   return {
//     props: {
//       ...(await serverSideTranslations(locale, ['common'])),
//     },
//   }
// }

export async function getServerSideProps({ params, locale }) {
  const initial_items = await fetchShowcases(APP_TYPE.movie, 'movie', 20);

  return {
    props: {
      ...(await serverSideTranslations(locale, ['cinemap', 'common'])),
      initial_showcases: initial_items || []
    }
  }
}
