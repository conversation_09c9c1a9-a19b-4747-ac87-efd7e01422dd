import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>FunBlocks AI Photo Coach: AI-Powered Photography Feedback & Analysis | FunBlocks AI Tools</title>
        <meta name="description" content="Transform your photography with FunBlocks AI Photo Coach. Get instant, professional-grade feedback on composition, exposure, and more to improve your skills and artistic vision." />
        <meta name="keywords" content="AI photo coach, photography feedback, photography analysis, composition analysis, exposure evaluation, focus assessment, photography learning, photography skills, amateur photography, photography enthusiasts, photography students, instant feedback, actionable learning points" />
      </Head>
      <MainFrame app={APP_TYPE.photo} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['photo', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
