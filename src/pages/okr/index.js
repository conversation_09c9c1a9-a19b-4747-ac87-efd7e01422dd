import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>FunBlocks AI OKR Assistant: AI-Powered Goal Management & Strategic Planning | FunBlocks AI Tools</title>
        <meta name="description" content="Transform your aspirations into achievements with FunBlocks AI OKR Assistant. Define, track, and achieve strategic objectives with AI-powered goal transformation, mind map generation, and intelligent assessment." />
        <meta name="keywords" content="AI OKR assistant, goal management, strategic planning, OKR methodology, objective key results, goal setting, performance management, enterprise strategy, personal development, project management, mind map, AI tools, productivity, career development" />
      </Head>
      <MainFrame app={APP_TYPE.okr} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['okr', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
