import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { Box, Button, Container, Heading, Input, Text, VStack } from '@chakra-ui/react';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Link from 'next/link';

export default function TestMentalModel() {
  const router = useRouter();
  const [mentalModel, setMentalModel] = useState('');
  const [currentUrl, setCurrentUrl] = useState('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setCurrentUrl(window.location.origin + router.pathname);
    }
  }, [router.pathname]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (mentalModel) {
      router.push(`/mindkit?mental_model=${encodeURIComponent(mentalModel)}`);
    }
  };

  const handleSubmitMindSnap = (e) => {
    e.preventDefault();
    if (mentalModel) {
      router.push(`/mindsnap?mental_model=${encodeURIComponent(mentalModel)}`);
    }
  };

  return (
    <Container maxW="container.md" py={10}>
      <VStack spacing={8} align="stretch">
        <Heading as="h1" size="xl" textAlign="center">
          Test Mental Model URL Parameter
        </Heading>
        
        <Box p={6} borderWidth={1} borderRadius="lg">
          <form onSubmit={handleSubmit}>
            <VStack spacing={4}>
              <Text>Enter a mental model name to test with MindKit:</Text>
              <Input 
                value={mentalModel}
                onChange={(e) => setMentalModel(e.target.value)}
                placeholder="e.g., swot_analysis, first_principle, etc."
              />
              <Button type="submit" colorScheme="blue" isDisabled={!mentalModel}>
                Test with MindKit
              </Button>
            </VStack>
          </form>
        </Box>

        <Box p={6} borderWidth={1} borderRadius="lg">
          <form onSubmit={handleSubmitMindSnap}>
            <VStack spacing={4}>
              <Text>Enter a mental model name to test with MindSnap:</Text>
              <Input 
                value={mentalModel}
                onChange={(e) => setMentalModel(e.target.value)}
                placeholder="e.g., swot_analysis, first_principle, etc."
              />
              <Button type="submit" colorScheme="purple" isDisabled={!mentalModel}>
                Test with MindSnap
              </Button>
            </VStack>
          </form>
        </Box>

        <Box p={6} borderWidth={1} borderRadius="lg">
          <VStack spacing={4} align="stretch">
            <Text fontWeight="bold">Example URLs:</Text>
            <Link href="/mindkit?mental_model=swot_analysis" passHref>
              <Text as="a" color="blue.500" textDecoration="underline">
                {currentUrl.replace('/test-mental-model', '/mindkit')}?mental_model=swot_analysis
              </Text>
            </Link>
            <Link href="/mindkit?mental_model=first_principle" passHref>
              <Text as="a" color="blue.500" textDecoration="underline">
                {currentUrl.replace('/test-mental-model', '/mindkit')}?mental_model=first_principle
              </Text>
            </Link>
            <Link href="/mindsnap?mental_model=six_thinking_hats" passHref>
              <Text as="a" color="blue.500" textDecoration="underline">
                {currentUrl.replace('/test-mental-model', '/mindsnap')}?mental_model=six_thinking_hats
              </Text>
            </Link>
            <Link href="/mindsnap?mental_model=custom_model" passHref>
              <Text as="a" color="blue.500" textDecoration="underline">
                {currentUrl.replace('/test-mental-model', '/mindsnap')}?mental_model=custom_model (will use "Other")
              </Text>
            </Link>
          </VStack>
        </Box>
      </VStack>
    </Container>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
