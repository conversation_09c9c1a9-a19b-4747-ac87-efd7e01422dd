import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import SEO from '../../components/common/SEO'
import { fetchShowcases } from '../../utils/data';

const SEO_Data = {
    graphics: {
        title: "AI Graphics Generator: Infographics, Flowcharts, Data Charts & SVG Code | FunBlocks AI",
        description: "Create professional infographics, flowcharts, data charts, and SVG code with our AI Graphics Generator. Transform complex information into stunning visuals in seconds - no design skills needed.",
        keywords: "AI Infographic generator, AI flowchart generator, data chart creator, AI SVG Code generator, data visualization, visual content, infographic maker, flowchart maker, chart generator, SVG creator, AI graphics tool, FunBlocks AI"
    },
    mindmap: {
        title: "AI Reading Map: Mind Map Guided Reading | FunBlocks AI Tools",
        description: "Unlock deeper insights from your reading with AI Reading Map, a FunBlocks AI tool that creates structured reading guides and mind maps to enhance comprehension and knowledge retention.",
        keywords: "AI reading assistant, reading comprehension, mind mapping, knowledge discovery, book analysis, reading efficiency, intelligent reading, AI learning, educational tools, knowledge management, reading guide, FunBlocks AI"
    },
    reading: {
        title: "AI Reading Map: Mind Map Guided Reading | FunBlocks AI Tools",
        description: "Unlock deeper insights from your reading with AI Reading Map, a FunBlocks AI tool that creates structured reading guides and mind maps to enhance comprehension and knowledge retention.",
        keywords: "AI reading assistant, reading comprehension, mind mapping, knowledge discovery, book analysis, reading efficiency, intelligent reading, AI learning, educational tools, knowledge management, reading guide, FunBlocks AI"
    },
    movie: {
        title: "AI CineMap: AI-Powered Film Analysis & Mind Maps | FunBlocks AI Tools",
        description: "Unlock deeper insights into your favorite films with AI CineMap. Generate interactive mind maps analyzing narrative structure, cinematography, themes, and cultural context. Perfect for film enthusiasts, students, and content creators.",
        keywords: "AI film analysis, film mind map, cinema analysis, movie analysis, film studies, film education, narrative structure, cinematography, thematic analysis, cultural context, AI tools, film enthusiasts, content creators, interactive visualization, FunBlocks AI"
    }
};

export default function Home({ app, initial_items }) {
    const { basePath } = getConfig().publicRuntimeConfig;

    let appPath = APP_TYPE[app?.toLowerCase()];
    if (!appPath) {
        appPath = APP_TYPE.mindmap;
    }

    const seo_data = SEO_Data[appPath.toLowerCase()];

    return (
        <>
            {
                seo_data &&
                <SEO
                    title={seo_data.title}
                    description={seo_data.description}
                    keywords={seo_data.keywords}
                    image="/og-image.png"
                    url={basePath + "/" + appPath.toLowerCase()}
                />
            }

            <MainFrame app={appPath} isCollection={true} initial_showcases={initial_items}/>
        </>
    )
}

// export async function getStaticProps({ params, locale }) {
//     console.log('params...........', params, locale)
//     return {
//         props: {
//             ...(await serverSideTranslations(locale, ['common'])),
//         },
//     }
// }

export async function getServerSideProps({ params, locale }) {
    const mode = params?.slug[0] === APP_TYPE.movie && 'movie' || 'book';
    const initial_items = await fetchShowcases(APP_TYPE.mindmap, mode);

    return {
        props: {
            ...(await serverSideTranslations(locale, ['common'])),
            app: params?.slug[0] || 'mindmap',
            initial_items
        }
    }
}

// export async function getStaticPaths({ locales }) {
//     // 原始的静态路径
//     const slugs = [
//       ["graphics"],
//       ["mindmap"],
//       ["brainstorming"],
//       ["decision"],
//     ];

//     // 为每种语言生成路径
//     const paths = slugs.flatMap((slug) =>
//       locales.map((locale) => ({
//         params: { slug }, // 保持动态路由参数
//         locale,           // 指定语言环境
//       }))
//     );

//     console.log(JSON.stringify(paths, null, 4));

//     return {
//       paths,
//       fallback: false, // 未列出的路径返回 404
//     };
//   }

//   export async function getStaticProps({ params, locale }) {
//     const { slug } = params;

//     return {
//       props: {
//         ...(await serverSideTranslations(locale, ['common'])), // 加载语言翻译
//         app: slug[0] || '', // 动态参数
//       },
//     };
//   }