import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>FunBlocks AI Art Insight: AI-Powered Art Appreciation & Analysis | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock deeper understanding of art with FunBlocks AI Art Insight. Get instant, AI-powered mind maps analyzing artworks, photos, & cultural significance. Perfect for educators, enthusiasts, & researchers." />
        <meta name="keywords" content="AI art analysis, art appreciation, art history, AI art tool, mind map, art education, art research, professional art analysis, art critique, cultural significance, composition analysis, color analysis, technique analysis, photography feedback" />
      </Head>
      <MainFrame app={APP_TYPE.art} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['artinsight', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
