// pages/aitools/mindsnap/index.js
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import Head from 'next/head'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';

export default function MindSnapIndex() {
  const router = useRouter();
  const { mental_model } = router.query;
  const { basePath } = getConfig().publicRuntimeConfig;
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.funblocks.net';
  const canonicalUrl = `${siteUrl}${basePath}/mindsnap`;
  const currentDate = new Date().toISOString().split('T')[0]; // Get current date for SEO

  useEffect(() => {
    // 如果有mental_model查询参数，重定向到动态路由
    if (mental_model && typeof mental_model === 'string') {
      router.replace(`/aitools/mindsnap/${encodeURIComponent(mental_model)}`);
    }
  }, [mental_model, router]);

  // Schema.org structured data for rich results
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "MindSnap - AI Mental Model Analysis & Visualization",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "Transform any topic into visual insights using AI-powered mental model analysis. Create beautiful infographics that enhance understanding and decision-making.",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.7",
      "ratingCount": "112"
    },
    "featureList": [
      "Mental model analysis",
      "AI-powered deep insights",
      "Beautiful infographic generation",
      "SVG export format",
      "SWOT analysis framework",
      "First principles thinking",
      "Six thinking hats method",
      "Eisenhower matrix",
      "Second-order thinking",
      "Inversion thinking"
    ],
    "applicationSubCategory": "Visualization Tool",
    "keywords": "mental models, AI infographics, visual thinking, decision making, SWOT analysis, first principles thinking",
    "datePublished": "2024-05-01",
    "dateModified": currentDate
  };

  // FAQ structured data for rich results
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "What is MindSnap and how does it work?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "MindSnap is an AI-powered tool that transforms topics into visual infographics using mental models. You provide a topic, and our AI analyzes it through the lens of classical mental frameworks, generating a visually appealing infographic that highlights key insights and relationships."
        }
      },
      {
        "@type": "Question",
        "name": "What are mental models and why are they useful?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Mental models are frameworks that help structure thinking and analysis. They're useful because they provide proven approaches to understanding complex topics, making decisions, and solving problems. By applying different mental models, you can gain new perspectives and insights that might not be apparent through conventional thinking."
        }
      },
      {
        "@type": "Question",
        "name": "Do I need to understand mental models to use MindSnap?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Not at all! MindSnap is designed to make mental models accessible to everyone. You can either select a specific model if you're familiar with it, or let our AI choose the most appropriate one for your topic. The generated infographic will explain how the mental model applies to your specific topic."
        }
      },
      {
        "@type": "Question",
        "name": "What types of topics can I analyze with MindSnap?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "MindSnap can analyze virtually any topic - from business challenges and market trends to personal decisions, educational concepts, and creative projects. The AI adapts the analysis based on the nature of your topic and the selected mental model."
        }
      }
    ]
  };

  return (
    <>
      <Head>
        <title>MindSnap - AI Mental Model Analysis & Visualization | FunBlocks AI Tools</title>
        <meta name="description" content="Transform any topic into visual insights with MindSnap. Our AI analyzes your ideas through proven mental models, creating beautiful infographics that enhance understanding and decision-making." />
        <meta name="keywords" content="mental models, AI infographics, visual thinking, decision making, SWOT analysis, first principles thinking, six thinking hats, eisenhower matrix, second-order thinking, inversion, business analysis, educational tools, visual learning, critical thinking, cognitive frameworks, strategic planning, problem solving, business strategy visualization, concept mapping, visual brainstorming, decision framework, mental model analysis, AI visualization tool" />

        {/* Mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
        <meta name="theme-color" content="#4299E1" />

        {/* Canonical URL */}
        <link rel="canonical" href={canonicalUrl} />

        {/* Open Graph metadata (for social media sharing) */}
        <meta property="og:title" content="MindSnap - AI Mental Model Analysis & Visualization" />
        <meta property="og:description" content="Transform any topic into visual insights with AI-powered mental model analysis. Create beautiful infographics instantly." />
        <meta property="og:url" content={canonicalUrl} />
        <meta property="og:type" content="website" />
        <meta property="og:site_name" content="FunBlocks AI Tools" />
        <meta property="og:image" content="https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_swot.png" />
        <meta property="og:image:alt" content="MindSnap AI Mental Model Analysis Example" />

        {/* Twitter Card metadata */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="MindSnap - AI Mental Model Analysis & Visualization" />
        <meta name="twitter:description" content="Transform any topic into visual insights with AI-powered mental model analysis. Create beautiful infographics instantly." />
        <meta name="twitter:image" content="https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_swot.png" />
        <meta name="twitter:image:alt" content="MindSnap AI Mental Model Analysis Example" />

        {/* Schema.org structured data - JSON-LD format */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />

        {/* FAQ structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqData) }}
        />
      </Head>
      <MainFrame app={APP_TYPE.mindsnap} mental_model={mental_model} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['mindsnap', 'common'])),
    },
  }
}