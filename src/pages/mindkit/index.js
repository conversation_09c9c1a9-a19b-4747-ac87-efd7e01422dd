// pages/aitools/mindkit/index.js
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import Head from 'next/head'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { APP_TYPE } from '../../utils/constants';

export default function MindkitIndex() {
  const router = useRouter();
  const { mental_model } = router.query;

  useEffect(() => {
    // 如果有mental_model查询参数，重定向到动态路由
    if (mental_model && typeof mental_model === 'string') {
      router.replace(`/aitools/mindkit/${encodeURIComponent(mental_model)}`);
    }
  }, [mental_model, router]);

  return (
    <>
      <Head>
        <title>FunBlocks AI Brainstorming with Classic Mental Models | FunBlocks AI Tools</title>
        <meta name="description" content="FunBlocks AI provides AI-driven tools for whiteboarding, mind mapping, presentations, and infographics. Leverage mental models and AI for enhanced decision-making, problem-solving, and knowledge integration." />
        <meta name="keywords" content="AI tools, mental models, mind mapping, whiteboarding, presentations, infographics, problem solving, decision making, knowledge management, strategic planning, risk assessment, market analysis, AI-powered productivity, visual communication" />
      </Head>
      <MainFrame app={APP_TYPE.mindkit} mental_model={mental_model} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['mindkit', 'common'])),
    },
  }
}