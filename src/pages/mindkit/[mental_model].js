// pages/aitools/mindkit/[mental_model].js
import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { useRouter } from 'next/router';

export default function Home() {
  const router = useRouter();
  const { mental_model } = router.query;

  return (
    <>
      <Head>
        <title>FunBlocks AI Brainstorming with Classic Mental Models | FunBlocks AI Tools</title>
        <meta name="description" content="FunBlocks AI provides AI-driven tools for whiteboarding, mind mapping, presentations, and infographics. Leverage mental models and AI for enhanced decision-making, problem-solving, and knowledge integration." />
        <meta name="keywords" content="AI tools, mental models, mind mapping, whiteboarding, presentations, infographics, problem solving, decision making, knowledge management, strategic planning, risk assessment, market analysis, AI-powered productivity, visual communication" />
      </Head>
      <MainFrame app={APP_TYPE.mindkit} mental_model={mental_model?.replace('-', ' ')} />
    </>
  )
}

// 使用 SSR 而不是 SSG
export async function getServerSideProps({ locale, params }) {
  const { mental_model } = params;
  
  return {
    props: {
      ...(await serverSideTranslations(locale, ['mindkit', 'common'])),
      mental_model,
    },
  }
}

// 不需要 getStaticPaths 和 getStaticProps