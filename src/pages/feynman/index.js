import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI Feynman Tutor: Master Complex Topics with AI | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock deeper understanding with FunBlocks AI Feynman Tutor. Break down complex subjects using AI-powered Feynman learning techniques, interactive teaching, and visual mind maps. Perfect for students, professionals, and curious minds." />
        <meta name="keywords" content="AI Feynman Tutor, Feynman Learning, AI Learning, Mind Mapping, Concept Breakdown, Visual Learning, Personalized Learning, Academic Learning, Professional Development, Knowledge Integration, FunBlocks AI, AI Education, Interactive Learning" />
      </Head>
      <MainFrame app={APP_TYPE.feynman} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['feynman', 'common'])),
    },
  }
}

// export async function getServerSideProps({ params, locale }) {
//   const initial_items = await fetchShowcases(APP_TYPE.mindmap, 'book', 20);

//   return {
//     props: {
//       ...(await serverSideTranslations(locale, ['common'])),
//       initial_showcases: initial_items
//     }
//   }
// }
