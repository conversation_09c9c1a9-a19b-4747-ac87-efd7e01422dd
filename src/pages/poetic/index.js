import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI-Powered Cultural Poetry Generator | FunBlocks AI Tools</title>
        <meta name="description" content="Transform images into culturally rich poetic commentaries with FunBlocks AI. Generate witty and insightful poems in multiple languages, reflecting diverse literary traditions. Perfect for content creators and culture enthusiasts." />
        <meta name="keywords" content="AI poetry generator, cultural poetry, multilingual poetry, image to poetry, AI poetic lens, cultural insights, literary traditions, content creation, social media captions, cross-cultural communication, Chinese poetry, Japanese poetry, French poetry, English poetry" />
      </Head>
      <MainFrame app={APP_TYPE.poetic} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['poetic', 'common'])),
    },
  }
}

// export async function getServerSideProps({ params, locale }) {
//   const initial_items = await fetchShowcases(APP_TYPE.mindmap, 'book', 20);

//   return {
//     props: {
//       ...(await serverSideTranslations(locale, ['common'])),
//       initial_showcases: initial_items
//     }
//   }
// }
