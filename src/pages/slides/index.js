import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>FunBlocks AI: AI-Powered Presentation Tool & More | FunBlocks AI Tools</title>
        <meta name="description" content="FunBlocks AI provides AI-powered tools for creating presentations, whiteboards, and more. Generate professional presentation slides instantly from any topic with AI. Enhance productivity and streamline your workflow." />
        <meta name="keywords" content="AI presentation tool, AI slide generator, presentation maker, AI PPT, markdown presentation, AI slides editor, speech notes, one-click presentation, AI-powered presentations, presentation software, online collaboration, productivity tools, FunBlocks AI" />
      </Head>
      <MainFrame app={APP_TYPE.slides} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['slides', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
