import { ChakraProvider } from '@chakra-ui/react'
import { AuthProvider } from '../contexts/AuthContext'
import '../styles/globals.css'
import '../styles/markdown-styles.css'
import '../components/CardGenerator/InputForm.css'
import '../styles/node.css'
import '../styles/simple-floatingedges.css'
import { appWithTranslation, useTranslation } from 'next-i18next'
import Head from 'next/head'
import getConfig from 'next/config';
import { useEffect } from 'react'
import { useRouter } from 'next/router'
import Script from 'next/script'
import * as gtag from '../lib/gtag'

function MyApp({ Component, pageProps }) {
  const { t } = useTranslation('common');
  const { basePath } = getConfig().publicRuntimeConfig;
  const router = useRouter()

  // 使用 pageProps 中的自定义 meta 数据，如果没有则使用默认值
  const metaTitle = pageProps.metaTitle || t('meta.title');
  const metaDescription = pageProps.metaDescription || t('meta.description');
  const metaImage = pageProps.metaImage || `https://www.funblocks.net/assets/img/portfolio/fullsize/ai_insights.png`;
  const metaUrl = pageProps.metaUrl || `https://www.funblocks.net${basePath}`;

  useEffect(() => {
    // 监听路由变化，发送页面访问数据
    const handleRouteChange = (url) => {
      gtag.pageview(url)
    }
    router.events.on('routeChangeComplete', handleRouteChange)
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange)
    }
  }, [router.events])

  return (
    <>
      {/* Global Site Tag (gtag.js) - Google Analytics */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${gtag.GA_MEASUREMENT_ID}`}
      />
      <Script
        id="gtag-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${gtag.GA_MEASUREMENT_ID}', {
              page_path: window.location.pathname,
            });
          `,
        }}
      />
      <Head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href={`${basePath}/icon.png`} />
        <meta name="keywords" content={t('meta.keywords')} />
        
        <title key="title">{metaTitle}</title>
        <meta name="description" content={metaDescription} key="description" />
        <meta property="og:title" content={metaTitle} key="og:title" />
        <meta property="og:description" content={metaDescription} key="og:description" />
        <meta property="og:image" content={metaImage} key="og:image" />
        <meta property="og:url" content={metaUrl} key="og:url" />
        <meta property="og:type" content="website" />
        
        <meta name="twitter:card" content="summary_large_image" key="twitter:card" />
        <meta name="twitter:title" content={metaTitle} key="twitter:title" />
        <meta name="twitter:description" content={metaDescription} key="twitter:description" />
        <meta name="twitter:image" content={metaImage} key="twitter:image" />
        
        <link rel="canonical" href={metaUrl} key="canonical" />
      </Head>
      <ChakraProvider>
        <AuthProvider>
          <Component {...pageProps} />
        </AuthProvider>
      </ChakraProvider>
    </>
  )
}

// 添加 getInitialProps 以支持服务端渲染
// MyApp.getInitialProps = async ({ Component, ctx }) => {
//   let pageProps = {}
  
//   if (Component.getInitialProps) {
//     pageProps = await Component.getInitialProps(ctx)
//   }
  
//   return { pageProps }
// }

export default appWithTranslation(MyApp)
