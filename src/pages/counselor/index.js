import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI Counseling Assistant - Personalized Psychological Support | FunBlocks AI Tools</title>
        <meta name="description" content="Experience empathetic and structured psychological support with our AI Counseling Assistant. Get personalized guidance, insights, and coping strategies for personal growth and well-being." />
        <meta name="keywords" content="AI counseling, AI therapy, mental health, psychological support, CBT, humanistic psychology, solution-focused therapy, personal growth, self-help, emotional analysis, therapeutic methods, online counseling, wellness, AI tools" />
      </Head>
      <MainFrame app={APP_TYPE.counselor} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['counselor', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
