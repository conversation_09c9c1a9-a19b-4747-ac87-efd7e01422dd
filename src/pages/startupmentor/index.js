import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI Mentor for Startups | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock your startup's potential with our AI-powered mentor. Get instant expert analysis, strategic guidance, and risk assessment to accelerate your business success. Ideal for early-stage entrepreneurs and innovation managers." />
        <meta name="keywords" content="AI startup mentor, startup guidance, business analysis, strategic planning, risk management, market validation, venture capital, innovation, business model optimization, entrepreneurship, fundraising, AI tools, FunBlocks AI" />
      </Head>
      <MainFrame app={APP_TYPE.startupmentor} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['startup', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
