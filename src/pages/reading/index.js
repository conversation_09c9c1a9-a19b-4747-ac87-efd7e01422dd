import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home({ initial_showcases }) {
  return (
    <>
      <Head>
        <title>AI Reading Map: Mind Map Guided Reading | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock deeper insights from your reading with AI Reading Map, a FunBlocks AI tool that creates structured reading guides and mind maps to enhance comprehension and knowledge retention." />
        <meta name="keywords" content="AI reading assistant, reading comprehension, mind mapping, knowledge discovery, book analysis, reading efficiency, intelligent reading, AI learning, educational tools, knowledge management, reading guide, FunBlocks AI" />
      </Head>
      <MainFrame app={APP_TYPE.reading} showcases={initial_showcases} />
    </>
  )
}

export async function getServerSideProps({ params, locale }) {
  const initial_items = await fetchShowcases(APP_TYPE.mindmap, 'book', 20);

  return {
    props: {
      ...(await serverSideTranslations(locale, ['reading', 'common'])),
      initial_showcases: initial_items || []
    }
  }
}
