import Head from 'next/head'
import { Box, Container, Heading, Link, VStack } from '@chakra-ui/react'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import Header from '../../components/Header';
import Artifacts from '../../components/CardGenerator/Artifacts';
import { ShareModalProvider } from '../../contexts/ShareModalContext';
import { QRCodeCanvas } from 'qrcode.react';
import ShareModal from '../../components/CardGenerator/ShareModal';
const { basePath } = getConfig().publicRuntimeConfig;

export default function Home() {
  return (
    <ShareModalProvider>
      <VStack width={'100%'} position="relative" overflowY="hidden" gap={'4px'}>
          <Header 
            leftLink={<Link href={basePath} fontSize="2xl" fontWeight="bold" color="blue.500" isExternal>
              FunBlocks AI Tools
            </Link>}
          />
          <ShareModal />
          <div style={{ display: 'none' }}>
              <QRCodeCanvas
                  value={`https://www.funblocks.net${basePath}`}
                  size={100}

                  id="qrcode-canvas"
              />
          </div>
          <VStack overflowY="auto" height={`calc(100vh - 76px)`} alignItems={'center'} width="100%" py={10}>
            <Artifacts collection={'my'} app={Object.values(APP_TYPE)}/>
          </VStack>
      </VStack>
    </ShareModalProvider>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
