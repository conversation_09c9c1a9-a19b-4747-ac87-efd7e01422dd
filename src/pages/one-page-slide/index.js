import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>FunBlocks AI Slide Generator: AI-Powered Productivity & Creativity Tools | FunBlocks AI Tools</title>
        <meta name="description" content="Create stunning presentations, mind maps, infographics, and more with our AI-powered tools. Transform text into visuals in seconds. Boost your productivity and unleash your creativity with FunBlocks AI." />
        <meta name="keywords" content="AI presentation maker, slide generator, infographic creator, mind map tool, whiteboard, AI tools, business presentations, educational content, professional reports, data visualization, smart content analysis, professional design, time-saving, productivity, creativity" />
      </Head>
      <MainFrame app={APP_TYPE.onePageSlides} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['oneslide', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
