import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "FunBlocks AI Brainstorming",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "AI-powered brainstorming and mind mapping tool that helps generate innovative ideas using advanced language models and classic thinking frameworks.",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "156"
    },
    "featureList": [
      "AI Ideation with multiple thinking models",
      "Interactive AI Mind Mapping",
      "Creative AI Thinking frameworks",
      "Collaborative brainstorming",
      "Exportable mind maps and ideas"
    ],
    "applicationSubCategory": "AI Creativity Tools"
  }
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "What is AI Brainstorming and how does it work?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "AI Brainstorming is a creative process that uses artificial intelligence to generate ideas and solutions. FunBlocks AI Brainstorming combines advanced language models with classic thinking frameworks to help you generate innovative ideas from multiple perspectives, breaking through mental limitations."
        }
      },
      {
        "@type": "Question",
        "name": "How does AI Mindmap generation improve creative thinking?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "AI Mindmap generation automatically creates structured visual representations of ideas, making them more intuitive and easier to understand. This visual approach helps users see connections between concepts, identify patterns, and develop ideas further. FunBlocks AI creates mind maps that can be customized, expanded, and shared with team members."
        }
      },
      {
        "@type": "Question",
        "name": "What thinking models does your AI Ideation tool use?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Our AI Ideation tool incorporates several classic thinking models including Six Thinking Hats by Edward de Bono, SCAMPER technique, Mind Mapping, First Principles Thinking, and more. These frameworks help structure the ideation process and ensure comprehensive exploration of topics from multiple perspectives."
        }
      },
      {
        "@type": "Question",
        "name": "How is AI thinking different from traditional brainstorming methods?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "AI thinking enhances traditional brainstorming by leveraging vast knowledge bases and pattern recognition capabilities. It can generate ideas without human biases, explore unusual connections, and systematically apply thinking frameworks. Unlike traditional methods that rely solely on participants' knowledge, AI can introduce novel perspectives and information from diverse domains."
        }
      }
    ]
  }


  return (
    <>
      <Head>
        <title>AI Brainstorm & AI Mindmap: Intelligent Ideation Tools | FunBlocks AI Brainstorming</title>
        <meta name="description" content="Transform your creative process with AI-powered brainstorming and mind mapping. Our AI ideation tools leverage advanced thinking models to generate innovative ideas, build interactive mind maps, and enhance creative thinking. Perfect for business strategy, content creation, and problem-solving." />
        <meta name="keywords" content="AI brainstorm, AI ideation, AI mindmap, AI thinking, idea generation, mind mapping, creative thinking, LLM, thinking models, innovation, productivity, business solutions, personal development, problem-solving, FunBlocks AI, collaboration, visual tools, six thinking hats, SCAMPER, creative AI, brainstorm assistant, digital brainstorming" />

        {/* Open Graph tags for social sharing */}
        <meta property="og:title" content="AI Brainstorm & AI Mindmap: Intelligent Ideation Tools | FunBlocks AI" />
        <meta property="og:description" content="Generate innovative ideas, build interactive mind maps, and enhance creative thinking with our AI-powered brainstorming and ideation tools. Leverage advanced AI thinking models to overcome creative blocks." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://www.funblocks.net/aitools/brainstorming" />
        {/* <meta property="og:image" content="https://www.funblocks.net/images/brainstorming-preview.jpg" /> */}

        {/* Schema.org structured data */}
        <script type="application/ld+json">
          {JSON.stringify(schemaData)}
        </script>
        {/* FAQ Schema for SEO */}
        <script type="application/ld+json">
          {JSON.stringify(faqData)}
        </script>

        {/* Canonical URL */}
        <link rel="canonical" href="https://www.funblocks.net/aitools/brainstorming" />
      </Head>
      <MainFrame app={APP_TYPE.brainstorming} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['brainstorm', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');

//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
