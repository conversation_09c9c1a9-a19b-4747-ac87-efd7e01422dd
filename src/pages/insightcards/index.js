import { VStack } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import SEO from '../../components/common/SEO'
import SEOStructuredData from '../../components/common/SEOStructuredData'
import { useTranslation } from 'next-i18next'

export default function Home() {
  const { basePath } = getConfig().publicRuntimeConfig;
  const { t } = useTranslation('insightcards');

  // FAQ data for SEO
  const faqs = [
    {
      question: 'What are InsightCards?',
      answer: 'InsightCards are AI-generated visual cards that provide unique insights on any topic. They transform complex concepts into visually appealing SVG cards with different perspectives like Aha Insights, Paradoxical Interpretations, Multi-perspectives, Cross-disciplinary analysis, and more.'
    },
    {
      question: 'How do InsightCards work?',
      answer: 'Simply enter a topic and select a card type (like Aha Insight, Philosophical Analysis, etc.). Our AI analyzes your topic and generates a visually appealing insight card that reveals hidden connections, contradictions, or perspectives you might not have considered.'
    },
    {
      question: 'What types of InsightCards can I create?',
      answer: 'You can create various types including Aha Insight (revealing hidden logic), Paradoxical Interpretation (explaining through counterintuitive reasoning), Multi-perspectives (understanding from different viewpoints), Cross-disciplinary analysis, Philosophical Analysis, Incisive Critique, Brutally Honest responses, and Layered Understanding cards.'
    },
    {
      question: 'Who can benefit from using InsightCards?',
      answer: 'InsightCards are valuable for students, educators, researchers, content creators, business professionals, and anyone looking to gain deeper understanding of concepts, generate creative ideas, or create engaging visual content for presentations and social media.'
    },
    {
      question: 'Can I customize the InsightCards?',
      answer: 'While you cannot directly edit the generated cards, you can influence the output by selecting different card types and refining your topic description. Each card type offers a unique perspective on your topic.'
    },
    {
      question: 'Can I share the InsightCards I create?',
      answer: 'Yes! All generated InsightCards can be easily downloaded and shared on social media, included in presentations, or used in educational materials. They\'re designed to be visually appealing and perfect for sharing insights.'
    },
    {
      question: 'How are InsightCards different from regular infographics?',
      answer: 'Unlike traditional infographics that primarily visualize data, InsightCards focus on transforming abstract concepts and ideas into visual representations that highlight connections, contradictions, and multiple perspectives. They\'re designed to provoke deeper thinking rather than just presenting information.'
    },
    {
      question: 'Are InsightCards based on scientific research?',
      answer: 'Yes, InsightCards are built on established cognitive science principles including Dual Coding Theory, perspective-taking research, insight formation studies, and visual learning research. These principles enhance learning, retention, and creative thinking.'
    },
    {
      question: 'How can I use InsightCards for teaching or education?',
      answer: 'Educators can use InsightCards to help students understand complex topics from multiple perspectives, develop critical thinking skills, and visualize abstract concepts. They\'re particularly effective for introducing new topics, facilitating discussions, and helping students make connections between different ideas.'
    },
    {
      question: 'Can InsightCards help with creative problem-solving?',
      answer: 'Absolutely! InsightCards are designed to break conventional thinking patterns by presenting ideas from unexpected angles. The Cross-disciplinary Analysis and Paradoxical Interpretation cards are especially useful for overcoming creative blocks and generating innovative solutions.'
    }
  ];

  // Enhanced Schema.org structured data for rich results
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "FunBlocks InsightCards Generator",
    "applicationCategory": "DesignApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "Create visually appealing insight cards that reveal hidden connections, contradictions, and perspectives on any topic. Generate Aha Insights, Paradoxical Interpretations, Multi-perspectives, and more with AI.",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "950"
    }
  };

  return (
    <>
      <SEO
        title="AI InsightCards Generator: Create Visual Insight Cards | FunBlocks AI"
        description="Generate visually appealing insight cards that reveal hidden connections, contradictions, and perspectives on any topic. Create Aha Insights, Paradoxical Interpretations, Multi-perspectives, and more with our AI InsightCards Generator."
        keywords="AI insight cards, visual insights, aha moments, paradoxical thinking, multi-perspective analysis, cross-disciplinary insights, philosophical analysis, critical thinking cards, visual learning, concept visualization, insight generator, FunBlocks AI"
        image="/og-image.png"
        url={basePath + "/insightcards"}
        canonical={`https://www.funblocks.net${basePath}/insightcards`}
      />
      <SEOStructuredData faqs={faqs} />

      {/* Schema.org structured data - JSON-LD format */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
      />

      <MainFrame app={APP_TYPE.insightcards} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'insightcards'])),
    },
  }
}
