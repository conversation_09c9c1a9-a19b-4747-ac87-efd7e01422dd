import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI-Powered Reflection Coach for Critical Thinking & Self-Awareness | FunBlocks AI Tools</title>
        <meta name="description" content="Develop stronger reasoning and deeper self-awareness with FunBlocks AI's ReflectAI. An AI coach that challenges your thinking, analyzes beliefs, and provides multi-perspective insights for personal and professional growth." />
        <meta name="keywords" content="AI reflection coach, critical thinking, self-awareness, personal growth, professional development, decision analysis, belief examination, career development, cognitive biases, mind mapping, multi-perspective analysis, AI tools, emotional intelligence, leadership potential" />
      </Head>
      <MainFrame app={APP_TYPE.reflection} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['reflection', 'common'])),
    },
  }
}

// export async function getServerSideProps({ params, locale }) {
//   const initial_items = await fetchShowcases(APP_TYPE.mindmap, 'book', 20);

//   return {
//     props: {
//       ...(await serverSideTranslations(locale, ['common'])),
//       initial_showcases: initial_items
//     }
//   }
// }
