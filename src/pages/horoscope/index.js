import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
    <Head>
      <title>AI Fortune Analysis: Personalized Astrological Insights | FunBlocks AI Tools</title>
      <meta name="description" content="Unlock your destiny with AI-powered astrological insights. Combining Eastern and Western astrology, get personalized analysis and actionable suggestions for career, wealth, relationships, and health." />
      <meta name="keywords" content="AI astrology, fortune telling, personalized astrology, Eastern astrology, Western astrology, career planning, wealth management, relationship advice, health analysis, life planning, decision making, astrological insights, AI-powered analysis, zodiac signs, Chinese zodiac, fortune analysis" />
    </Head>
    <MainFrame app={APP_TYPE.horoscope} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['horoscope', 'common'])),
    },
  }
}

// export async function getServerSideProps({ params, locale }) {
//   const initial_items = await fetchShowcases(APP_TYPE.mindmap, 'book', 20);

//   return {
//     props: {
//       ...(await serverSideTranslations(locale, ['common'])),
//       initial_showcases: initial_items
//     }
//   }
// }
