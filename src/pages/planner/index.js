import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI Task Planner: AI-Powered Productivity & Task Management | FunBlocks AI Tools</title>
        <meta name="description" content="Transform complex tasks into actionable plans with FunBlocks AI Task Planner. Break down projects, boost productivity, and ensure successful completion with AI-powered analysis, smart task breakdown, and intuitive Kanban task management." />
        <meta name="keywords" content="AI task planner, task management, project management, AI task breakdown, Kanban, mind mapping, brainstorming, productivity, time efficiency, project risk reduction, goal setting, team collaboration, business applications, personal use, FunBlocks AI" />
      </Head>
      <MainFrame app={APP_TYPE.planner} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['planner', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
