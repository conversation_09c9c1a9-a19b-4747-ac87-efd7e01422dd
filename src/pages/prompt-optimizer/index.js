import Head from 'next/head'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { APP_TYPE } from '../../utils/constants'
import { useTranslation } from 'next-i18next'

export default function Home() {
  const { t } = useTranslation('prompt-optimizer')
  
  // Structured data for Google Rich Results
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "AI Prompt Optimizer",
    "applicationCategory": "Productivity Software",
    "description": t('prompt_intro_description'),
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "256"
    }
  }

  return (
    <>
      <Head>
        <title>{t('prompt_intro_title')}</title>
        <meta name="description" content={t('prompt_intro_description')} />
        <meta name="keywords" content="AI prompt optimization, prompt engineering, AI writing, ChatGPT prompts, AI interaction" />
        
        {/* Open Graph tags for social sharing */}
        <meta property="og:title" content={t('prompt_intro_title')} />
        <meta property="og:description" content={t('prompt_intro_description')} />
        <meta property="og:type" content="website" />
        <meta property="og:image" content="https://www.funblocks.net/img/portfolio/fullsize/prompt_optimizer_hero.png" />
        
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={t('prompt_intro_title')} />
        <meta name="twitter:description" content={t('prompt_intro_description')} />
        
        {/* Structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </Head>
      <MainFrame app={APP_TYPE.promptOptimizer} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['prompt-optimizer', 'common'])),
    },
  }
}
