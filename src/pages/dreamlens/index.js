import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>DreamLens: AI-Powered Dream Analysis & Interpretation | FunBlocks AI Tools</title>
        <meta name="description" content="Unlock your subconscious with DreamL<PERSON>, an AI-powered dream analysis tool. Get personalized insights into your dreams using psychology and neuroscience perspectives." />
        <meta name="keywords" content="dream analysis, dream interpretation, AI dream analysis, subconscious, psychology, Freud, Jung, cognitive neuroscience, dream symbols, personal growth, mental health, self-understanding, insight, visualization, mind map, DreamLens" />
      </Head>
      <MainFrame app={APP_TYPE.dreamlens} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['dreamlens', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');
  
//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
