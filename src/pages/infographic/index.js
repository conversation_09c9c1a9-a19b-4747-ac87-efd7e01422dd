import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  const currentDate = new Date().toISOString().split('T')[0]; // 获取当前日期，用于SEO

  return (
    <>
      <Head>
        <title>AI-Powered Infographic Generator | Create Professional Data Visualizations Instantly | FunBlocks AI</title>
        <meta name="description" content="Transform text into stunning infographics in seconds with our AI-powered tool. Create data visualizations, timelines, charts, comparison graphics and more with no design skills required. Perfect for marketing, education, and business presentations." />
        <meta name="keywords" content="AI infographic generator, infographic maker, data visualization tool, business presentations, educational content, marketing infographics, research reports, AI visualization, design software, visual content, timeline creator, flowchart generator, mind map, SVG infographic creator, automated chart maker, statistical visualization, process flowcharts, comparison graphics" />

        {/* 添加规范链接 */}
        <link rel="canonical" href="https://www.funblocks.net/aitools/infographic" />

        {/* 添加更多SEO元数据 */}
        <meta name="author" content="FunBlocks AI" />
        <meta name="robots" content="index, follow" />
        <meta name="revisit-after" content="7 days" />
        <meta name="language" content="English" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="last-modified" content={currentDate} />
        <meta name="category" content="AI Tools, Data Visualization" />
        <meta name="rating" content="General" />

        {/* Open Graph 元数据 (对社交媒体分享优化) */}
        <meta property="og:title" content="AI-Powered Infographic Generator | Create Professional Visuals | FunBlocks AI" />
        <meta property="og:description" content="Transform text into stunning infographics in seconds with our AI-powered tool. Create data visualizations, timelines, and charts with no design skills required. Perfect for marketing, education, and business." />
        {/* <meta property="og:image" content="https://www.funblocks.net/images/infographic-preview.jpg" /> */}
        <meta property="og:url" content="https://www.funblocks.net/aitools/infographic" />
        <meta property="og:type" content="website" />
        <meta property="og:site_name" content="FunBlocks AI Tools" />
        <meta property="og:locale" content="en_US" />
        <meta property="og:locale:alternate" content="zh_CN" />

        {/* Twitter Card 元数据 */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="AI-Powered Infographic Generator | Create Professional Visuals | FunBlocks AI" />
        <meta name="twitter:description" content="Transform text into stunning infographics in seconds with our AI-powered tool. Create data visualizations, timelines, and charts with no design skills required." />
        {/* <meta name="twitter:image" content="https://www.funblocks.net/images/infographic-preview.jpg" /> */}
        <meta name="twitter:site" content="@FunBlocksAI" />

        {/* Schema.org 结构化数据 - JSON-LD格式 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "FunBlocks AI Infographic Generator",
              "applicationCategory": "DesignApplication",
              "applicationSubCategory": "DataVisualization",
              "operatingSystem": "Web",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD",
                "priceValidUntil": new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
                "availability": "https://schema.org/InStock"
              },
              "description": "AI-powered tool that transforms text into stunning infographics in seconds. Create data visualizations, timelines, charts, comparison graphics and more with no design skills required. Perfect for marketing, education, and business presentations.",
              // "screenshot": "https://www.funblocks.net/images/infographic-preview.jpg",
              "featureList": [
                "One-click infographic generation",
                "Smart content analysis",
                "Professional design principles",
                "Flexible SVG output format",
                "Multiple visualization types",
                "Data-driven chart creation",
                "Timeline visualization",
                "Process flow diagrams",
                "Comparison graphics"
              ],
              "keywords": "infographic generator, data visualization, AI charts, timeline creator, process flowcharts",
              "datePublished": "2023-01-01",
              "dateModified": currentDate,
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "256",
                "bestRating": "5",
                "worstRating": "1"
              },
              "audience": {
                "@type": "Audience",
                "audienceType": "Business professionals, Educators, Content creators, Researchers"
              },
              "author": {
                "@type": "Organization",
                "name": "FunBlocks AI",
                "url": "https://www.funblocks.net"
              }
            })
          }}
        />

        {/* FAQ Schema.org 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "FAQPage",
              "mainEntity": [
                {
                  "@type": "Question",
                  "name": "How does the AI infographic generator work?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Our AI analyzes your input text, identifies key information and relationships, then automatically selects the most appropriate visualization type and applies professional design principles to create the infographic."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Do I need design experience to use it?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "No design experience is needed. Our AI handles all the design decisions while still giving you the flexibility to make adjustments if desired."
                  }
                },
                {
                  "@type": "Question",
                  "name": "What types of content can I create infographics from?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "You can create infographics from various types of content, including statistics, processes, timelines, comparisons, hierarchies, and more."
                  }
                },
                {
                  "@type": "Question",
                  "name": "Can I edit the generated infographics?",
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes, all infographics are generated in SVG format, which can be easily edited using vector graphics software."
                  }
                }
              ]
            })
          }}
        />
      </Head>
      <MainFrame app={APP_TYPE.infographic} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['infographic', 'common'])),
    },
  }
}

// export async function getServerSideProps(context) {
//   // 获取初始数据
//   const data = await fetchShowcases(APP_TYPE.mindmap, 'book');

//   return {
//     props: {
//       initial_showcases: data,
//     },
//   };
// }
