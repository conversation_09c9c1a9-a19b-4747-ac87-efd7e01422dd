const prodCardTypes = {
  graphChat: [{
    value: 'chart_infographics', label: 'Graph Chat', desc: 'Responding to any query or instruction by generating a SVG infographics'
  }],

  makeGraph: [
    { value: 'chart_infographics', label: 'Infographics', desc: 'Create an infographic from the provided text or webpage' },
    { value: "chart_slide", label: "One-page slide", desc: 'Create a one-page slide from the provided text or webpage' },
    { value: "chart_cornell_notes", label: "Cornell Note", desc: 'Create a Cornell-style note from the provided text or webpage' },
    { value: "chart_flowchart", label: "Flow<PERSON>hart", desc: 'Create a Flowchart from the provided text or webpage' },
    { value: "chart_sequencediagram", label: "Sequence Diagram", desc: 'Create a Sequence Diagram from the provided text or webpage' },
    { value: "chart_quadrant", label: "Quadrant Chart", desc: 'Create a Quadrant Chart from the provided text or webpage' },
    { value: "chart_timeline", label: "Timeline", desc: 'Create a Timeline from the provided text or webpage' },
  ],
  imageInsights: [
    { value: 'witty_insights', label: 'WonderLens', desc: 'See the world through a special perspective' },
  ],
  cardGuru: [
    // { value: '66f81b742bb3331b1722b401', label: 'Concept-Dissector', desc: 'Break down a concept in detail' },
    // { value: "670d61391a63db7d490a0eac", label: "Multi-perspective", desc: 'Understand a concept from multiple perspectives' },
    // { value: "670d7a791a63db7d490a1117", label: "Red Pill and Blue Pill", desc: 'Neo, the hacker who took the red pill, ' },
    // { value: "670d95e4ed4ce9a88342acb6", label: "Impossible triangle", desc: 'Show the impossible triangle for any topic' },
    // { value: "670dd637ed4ce9a88342adf7", label: "Occam's Razor", desc: 'Explain a topic using the simplest explanation' },
    // { value: "670da193ed4ce9a88342acd7", label: "Simplified Concept", desc: 'Make a concept easy to understand' },
    // { value: "67106d6d8db5bf226e26e25f", label: "McKinsey method", desc: 'Analyze a problem using the McKinsey method' },
    // { value: "6710ad3b8db5bf226e26e358", label: "Business model canvas", desc: 'Create a business model canvas for a given business' },
    // { value: "671077898db5bf226e26e266", label: "Six Thinking Hats", desc: 'Analyze a problem using the Six Thinking Hats method' },
    { value: 'infographics_aha_insight', label: 'Aha Insight', desc: 'Reveal the hidden logic behind a concept for a sudden moment of clarity' },
    { value: 'infographics_paradoxical_interpretation', label: 'Paradoxical Interpretation', desc: 'Explain a concept through surprising or counterintuitive reasoning' },
    { value: "infographics_multi_perspectives", label: "Multi-perspectives", desc: 'Understand a concept from multiple perspectives' },
    { value: "infographics_cross_disciplinary", label: "Cross-disciplinary", desc: 'Performs cross-disciplinary analysis of the given topic' },
    { value: "infographics_philosophical_analysis", label: "Philosophical Analysis", desc: 'Analyze the given topic from the perspective of an appropriate philosopher' },
    { value: "infographics_incisive_critique", label: "Incisive Critique", desc: 'Create a sharp, incisive critique to provided statement' },
    { value: "infographics_brutally_honest", label: "Brutally Honest", desc: 'Create a brutally honest response to provided statement' },
    { value: "infographics_leveled_understanding", label: "Layered Understanding", desc: 'Create a hierarchical explanation of the provided topic that progressively deepens understanding' },
  ]// 可以根据需要添加更多开发环境的卡片类型
}

const devCardTypes = {
  graphChat: [{
    value: 'chart_infographics', desc: '根据产品描述生成关键词', label: '根据产品描述生成关键词'
  }],
  makeGraph: [
    { value: 'chart_infographics', label: 'Infographics', desc: 'Create an infographic from the provided text or webpage' },
    { value: "chart_slide", label: "One-page slide", desc: 'Create a one-page slide from the provided text or webpage' },
    { value: "chart_cornell_notes", label: "Cornell Note", desc: 'Create a Cornell-style note from the provided text or webpage' },
    { value: "chart_flowchart", label: "FlowChart", desc: 'Create a Flowchart from the provided text or webpage' },
    { value: "chart_sequencediagram", label: "Sequence Diagram", desc: 'Create a Sequence Diagram from the provided text or webpage' },
    { value: "chart_quadrant", label: "Quadrant Chart", desc: 'Create a Quadrant Chart from the provided text or webpage' },
    { value: "chart_timeline", label: "Timeline", desc: 'Create a Timeline from the provided text or webpage' },
  ],
  cardGuru: [
    { value: 'infographics_aha_insight', label: 'Aha Insight', desc: 'Reveal the hidden logic behind a concept for a sudden moment of clarity' },
    { value: 'infographics_paradoxical_interpretation', label: 'Paradoxical Interpretation', desc: 'Explain a concept through surprising or counterintuitive reasoning' },
    { value: "infographics_multi_perspectives", label: "Multi-perspectives", desc: 'Understand a concept from multiple perspectives' },
    { value: "infographics_cross_disciplinary", label: "Cross-disciplinary", desc: 'Performs cross-disciplinary analysis of the given topic' },
    { value: "infographics_philosophical_analysis", label: "Philosophical Analysis", desc: 'Analyze the given topic from the perspective of an appropriate philosopher' },
    { value: "infographics_incisive_critique", label: "Incisive Critique", desc: 'Create a sharp, incisive critique to provided statement' },
    { value: "infographics_brutally_honest", label: "Brutally Honest", desc: 'Create a brutally honest response to provided statement' },
    { value: "infographics_leveled_understanding", label: "Layered Understanding", desc: 'Create a hierarchical explanation of the provided topic that progressively deepens understanding' },
  ],
  imageInsights: [
    { value: 'witty_insights', label: 'WonderLens', desc: 'See the world through a special perspective' },
  ],
  // 可以根据需要添加更多开发环境的卡片类型
}

export const cardTypes = process.env.NODE_ENV === 'production' ? prodCardTypes : devCardTypes;

// export const onePageSlidesPromptId = process.env.NODE_ENV === 'production' ? '670db2b7ed4ce9a88342ace1' : '66ce76f4cd47d61b1b7f2353';
// export const infographicPromptId = process.env.NODE_ENV === 'production' ? '6713971f94befc9c46774f56' : '66ce76f4cd47d61b1b7f2353';
export const onePageSlidesPromptId =  'chart_slide'
export const infographicPromptId = 'chart_infographics'
export const cornellNotePromptId = 'chart_cornell_notes'