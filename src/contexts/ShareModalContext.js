import React, { createContext, useState, useContext } from 'react';
import getConfig from 'next/config';
import { useBreakpointValue, useMediaQuery } from '@chakra-ui/react';
const { basePath } = getConfig().publicRuntimeConfig;
const ShareModalContext = createContext();

export const ShareModalProvider = ({ children }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [artifactId, setArtifactId] = useState(null);
    const [imageDataUrl, setImageDataUrl] = useState(null);
    const [title, setTitle] = useState(null);
    const isMobile = useBreakpointValue({ base: true, md: false });
    const [app, setApp] = useState();

    const shareOnMobile = async (app, title, artifactId, imageDataUrl) => {
        if (navigator.share) {
            try {
                // Convert the data URL to a Blob directly
                const blob = await (await fetch(imageDataUrl)).blob();
                const file = new File([blob], 'card.png', { type: 'image/png' });

                await navigator.share({
                    title: title,
                    text: title,
                    url: `${window.location.origin}${basePath}/share/${app}/${artifactId}`,
                    files: [file]
                });
                return true; // Share successful
            } catch (error) {
                console.error('Error sharing:', error);
                return false; // Share failed
            }
        }
        return false; // Web Share API not supported
    };

    const openShareModal = async (app, artifactId, imgDataUrl, title) => {
        if (isMobile) {
            const shared = await shareOnMobile(app, title, artifactId, imgDataUrl);
            if (shared) return; // 如果成功分享，就不打开模态框
        }

        setApp(app);
        setArtifactId(artifactId);
        setImageDataUrl(imgDataUrl);
        setTitle(title);
        setIsOpen(true);
    };

    const closeShareModal = () => {
        setApp(null);
        setIsOpen(false);
        setArtifactId(null);
        setImageDataUrl(null);
        setTitle(null);
    };

    return (
        <ShareModalContext.Provider value={{ tool: app, isOpen, artifactId, imageDataUrl, title, openShareModal, closeShareModal }}>
            {children}
        </ShareModalContext.Provider>
    );
};

export const useShareModal = () => useContext(ShareModalContext);
