// Zodiac sign date ranges
const ZODIAC_DATES = [
  { name: '<PERSON><PERSON><PERSON>', startMonth: 12, startDay: 22, endMonth: 1, endDay: 19 },
  { name: 'Aquarius', startMonth: 1, startDay: 20, endMonth: 2, endDay: 18 },
  { name: '<PERSON><PERSON><PERSON>', startMonth: 2, startDay: 19, endMonth: 3, endDay: 20 },
  { name: '<PERSON><PERSON>', startMonth: 3, startDay: 21, endMonth: 4, endDay: 19 },
  { name: '<PERSON><PERSON>', startMonth: 4, startDay: 20, endMonth: 5, endDay: 20 },
  { name: '<PERSON>', startMonth: 5, startDay: 21, endMonth: 6, endDay: 21 },
  { name: '<PERSON>', startMonth: 6, startDay: 22, endMonth: 7, endDay: 22 },
  { name: '<PERSON>', startMonth: 7, startDay: 23, endMonth: 8, endDay: 22 },
  { name: '<PERSON><PERSON><PERSON>', startMonth: 8, startDay: 23, endMonth: 9, endDay: 22 },
  { name: '<PERSON><PERSON>', startMonth: 9, startDay: 23, endMonth: 10, endDay: 23 },
  { name: '<PERSON><PERSON><PERSON>', startMonth: 10, startDay: 24, endMonth: 11, endDay: 22 },
  { name: 'Sagittarius', startMonth: 11, startDay: 23, endMonth: 12, endDay: 21 }
];

// Chinese zodiac animals
const CHINESE_ZODIAC = [
  'Rat', 'Ox', 'Tiger', 'Rabbit', 'Dragon', 'Snake',
  'Horse', 'Goat', 'Monkey', 'Rooster', 'Dog', 'Pig'
];

// Five elements mapping
const FIVE_ELEMENTS = {
  'Capricorn': 'Earth',
  'Aquarius': 'Air',
  'Pisces': 'Water',
  'Aries': 'Fire',
  'Taurus': 'Earth',
  'Gemini': 'Air',
  'Cancer': 'Water',
  'Leo': 'Fire',
  'Virgo': 'Earth',
  'Libra': 'Air',
  'Scorpio': 'Water',
  'Sagittarius': 'Fire'
};

class HoroscopeCalculator {
  constructor() {
    this.baseFactors = {
      career: {
        Aries: 0.75, Taurus: 0.85, Gemini: 0.45, Cancer: 0.35,
        Leo: 0.95, Virgo: 0.55, Libra: 0.5, Scorpio: 0.8,
        Sagittarius: 0.7, Capricorn: 0.9, Aquarius: 0.6, Pisces: 0.4
      },
      wealth: {
        Aries: 0.45, Taurus: 0.95, Gemini: 0.7, Cancer: 0.4,
        Leo: 0.85, Virgo: 0.55, Libra: 0.75, Scorpio: 0.8,
        Sagittarius: 0.5, Capricorn: 0.9, Aquarius: 0.65, Pisces: 0.45
      },
      love: {
        Aries: 0.55, Taurus: 0.5, Gemini: 0.8, Cancer: 0.95,
        Leo: 0.7, Virgo: 0.45, Libra: 0.9, Scorpio: 0.75,
        Sagittarius: 0.65, Capricorn: 0.4, Aquarius: 0.85, Pisces: 0.95
      },
      health: {
        Aries: 0.85, Taurus: 0.75, Gemini: 0.5, Cancer: 0.65,
        Leo: 0.7, Virgo: 0.9, Libra: 0.6, Scorpio: 0.45,
        Sagittarius: 0.8, Capricorn: 0.7, Aquarius: 0.55, Pisces: 0.65
      }
    };

    // Elements compatibility matrix
    this.elementCompatibility = {
      Fire: { Fire: 1.0, Earth: 0.8, Metal: 0.9, Water: 0.7, Wood: 1.1 },
      Earth: { Fire: 0.8, Earth: 1.0, Metal: 1.1, Water: 0.8, Wood: 0.7 },
      Metal: { Fire: 0.7, Earth: 1.1, Metal: 1.0, Water: 0.9, Wood: 0.8 },
      Water: { Fire: 0.7, Earth: 0.8, Metal: 1.1, Water: 1.0, Wood: 1.1 },
      Wood: { Fire: 1.1, Earth: 0.7, Metal: 0.8, Water: 1.1, Wood: 1.0 }
    };

    // Define zodiac sign characteristics that influence different aspects
    this.zodiacTraits = {
      Aries: {
        leadership: 0.9,  energy: 0.85, stability: 0.3,  innovation: 0.4
      },
      Taurus: {
        stability: 0.9,   materialism: 0.8, energy: 0.4,  innovation: 0.3
      },
      Gemini: {
        adaptability: 0.8, communication: 0.9, stability: 0.3, focus: 0.4
      },
      Cancer: {
        emotion: 0.9,     security: 0.8, leadership: 0.4, adaptability: 0.5
      },
      Leo: {
        leadership: 0.9,  creativity: 0.8, stability: 0.4, patience: 0.3
      },
      Virgo: {
        analytical: 0.9,  precision: 0.85, creativity: 0.4, spontaneity: 0.3
      },
      Libra: {
        balance: 0.8,     harmony: 0.9, decisiveness: 0.3, intensity: 0.4
      },
      Scorpio: {
        intensity: 0.9,   power: 0.85, patience: 0.4, lightness: 0.3
      },
      Sagittarius: {
        adventure: 0.9,   optimism: 0.8, routine: 0.3, caution: 0.4
      },
      Capricorn: {
        ambition: 0.9,    discipline: 0.85, spontaneity: 0.3, flexibility: 0.4
      },
      Aquarius: {
        innovation: 0.9,  independence: 0.85, tradition: 0.3, routine: 0.4
      },
      Pisces: {
        intuition: 0.9,   empathy: 0.85, practicality: 0.3, structure: 0.4
      }
    };

    // More divergent category trait weights
    this.categoryTraits = {
      career: {
        leadership: 0.4,    analytical: 0.3,  stability: 0.2,   energy: -0.2
      },
      wealth: {
        materialism: 0.4,   discipline: 0.3,  adventure: -0.2,  caution: 0.2
      },
      love: {
        emotion: 0.4,       harmony: 0.3,     independence: -0.2, empathy: 0.2
      },
      health: {
        energy: 0.4,        stability: 0.3,   intensity: -0.2,   balance: 0.2
      }
    };

    // Amplified period weights
    this.periodWeights = {
      daily: {
        baseScore: 0.35,   // 降低基础分数权重
        seasonal: 0.2,
        personal: 0.2,
        cyclic: 0.15,
        random: 0.1
      },
      weekly: {
        baseScore: 0.4,
        seasonal: 0.2,
        personal: 0.15,
        cyclic: 0.15,
        random: 0.1
      },
      monthly: {
        baseScore: 0.45,
        seasonal: 0.25,
        personal: 0.15,
        cyclic: 0.1,
        random: 0.05
      },
      yearly: {
        baseScore: 0.5,
        seasonal: 0.3,
        personal: 0.1,
        cyclic: 0.05,
        random: 0.05
      }
    };

    // Calculate baseFactors using zodiac traits
    this.baseFactors = {
      career: {},
      wealth: {},
      love: {},
      health: {}
    };

    // Define trait weights for each category
    const categoryTraitWeights = {
      career: {
        leadership: 0.3,
        ambition: 0.25,
        stability: 0.15,
        analytical: 0.15,
        communication: 0.15
      },
      wealth: {
        materialism: 0.25,
        discipline: 0.2,
        perception: 0.2,
        stability: 0.2,
        impulse: -0.15     // Negative impact
      },
      love: {
        emotion: 0.25,
        empathy: 0.2,
        communication: 0.2,
        balance: 0.2,
        independence: 0.15
      },
      health: {
        health: 0.3,
        energy: 0.25,
        stability: 0.25,
        discipline: 0.2
      }
    };

    // Calculate base factors for each zodiac and category
    Object.keys(this.zodiacTraits).forEach(zodiac => {
      Object.keys(this.baseFactors).forEach(category => {
        let score = 0.5; // Start with a neutral base
        
        // Add weighted trait influences
        Object.entries(categoryTraitWeights[category]).forEach(([trait, weight]) => {
          if (this.zodiacTraits[zodiac][trait]) {
            score += this.zodiacTraits[zodiac][trait] * weight;
          }
        });

        // Normalize score to 0.6-1.0 range to maintain positivity while allowing difference
        this.baseFactors[category][zodiac] = 0.6 + (score * 0.4);
      });
    });

    // Add seasonal affinities
    this.seasonalAffinities = {
      spring: ['Aries', 'Taurus', 'Gemini'],
      summer: ['Cancer', 'Leo', 'Virgo'],
      autumn: ['Libra', 'Scorpio', 'Sagittarius'],
      winter: ['Capricorn', 'Aquarius', 'Pisces']
    };

    // Cycle frequencies for different periods
    this.cycleFrequencies = {
      daily: 1,
      weekly: 1/7,
      monthly: 1/30,
      yearly: 1/365
    };

    // Phase shifts for different categories to ensure non-correlation
    this.categoryPhaseShifts = {
      career: 0,
      wealth: Math.PI / 2,
      love: Math.PI,
      health: (3 * Math.PI) / 2
    };
  }


  // Calculate zodiac sign
  getZodiacSign(month, day) {
    for (const zodiac of ZODIAC_DATES) {
      if (
        (month === zodiac.startMonth && day >= zodiac.startDay) ||
        (month === zodiac.endMonth && day <= zodiac.endDay)
      ) {
        return zodiac.name;
      }
    }
    return ZODIAC_DATES[0].name; // Default to Capricorn
  }

  // Calculate Chinese zodiac
  getChineseZodiac(year) {
    return CHINESE_ZODIAC[(year - 4) % 12];
  }

  // Calculate lucky number based on birthday
  calculateLuckyNumber(birthday) {
    const dateSum = birthday.split('').reduce((sum, digit) => sum + parseInt(digit), 0);
    return dateSum % 9 + 1;
  }

  getCurrentSeason() {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'autumn';
    return 'winter';
  }

  calculateFortuneScore(birthday, gender, category, zodiacSign) {
    const date = new Date();
    const dayOfYear = Math.floor((date - new Date(date.getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
    
    // Get base score from refined baseFactors
    const baseScore = this.baseFactors[category][zodiacSign];
    
    // Seasonal bonus (5-10% boost if zodiac matches current season)
    const currentSeason = this.getCurrentSeason();
    const seasonalBonus = this.seasonalAffinities[currentSeason].includes(zodiacSign) ? 
      1 + (Math.sin(dayOfYear * 0.017) * 0.05 + 0.05) : 1;
    
    // Calculate cyclic influence (creates varying patterns through the year)
    const yearPhase = (2 * Math.PI * dayOfYear) / 365;
    const categoryPhase = {
      career: yearPhase,
      wealth: yearPhase + (Math.PI / 2),    // Quarter year offset
      love: yearPhase + Math.PI,            // Half year offset
      health: yearPhase + (3 * Math.PI / 2) // Three quarter year offset
    };
    
    const cyclicInfluence = (Math.sin(categoryPhase[category]) + 1) / 2 * 0.2 + 0.9; // 0.9-1.1 range

    // Personal number influence (based on birthday)
    const personalNumber = this.calculatePersonalDayNumber(birthday, date);
    const personalInfluence = 0.9 + (personalNumber * 0.2); // 0.9-1.1 range

    // Combine all factors
    let finalScore = baseScore * seasonalBonus * cyclicInfluence * personalInfluence;

    // Add controlled randomness (±5%)
    const randomFactor = 0.95 + (this.generateStableRandom(birthday + date.toISOString() + category) * 0.1);
    finalScore *= randomFactor;

    // Scale to 1-5 range
    finalScore = (finalScore * 4) + 1;

    // Ensure final score is between 1 and 5
    return Math.min(5, Math.max(1, finalScore));
  }

  calculatePersonalDayNumber(birthday, currentDate) {
    const birthdaySum = birthday.split('-').reduce((sum, num) => sum + parseInt(num), 0);
    const dateSum = currentDate.getDate() + (currentDate.getMonth() + 1) + currentDate.getFullYear();
    return ((birthdaySum + dateSum) % 9) / 9; // Normalize to 0-1
  }

  calculateGenderInfluence(gender, category, dayOfYear) {
    // Gender influence varies by category and day
    const baseInfluence = Math.sin(2 * Math.PI * dayOfYear / 365);
    
    // Different categories have different gender biases on different days
    const categoryFactors = {
      career: Math.sin(dayOfYear * 0.017), // Varies over roughly 3-week periods
      wealth: Math.cos(dayOfYear * 0.023), // Different phase than career
      love: Math.sin(dayOfYear * 0.019),   // Another distinct cycle
      health: Math.cos(dayOfYear * 0.021)  // Yet another cycle
    };

    const genderMod = gender === 'female' ? 1 : -1;
    return (baseInfluence * categoryFactors[category] * genderMod + 1) / 2; // Normalize to 0-1
  }

  generateStableRandom(seed) {
    // Simple but stable pseudo-random generator
    const hash = seed.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    // Convert hash to 0-1 range
    return (Math.abs(hash) % 1000) / 1000;
  }

  calculateBaseScore(zodiacSign, category) {
    let score = 0.5;
    const traits = this.zodiacTraits[zodiacSign];
    const categoryWeights = this.categoryTraits[category];
    
    Object.entries(categoryWeights).forEach(([trait, weight]) => {
      if (traits[trait]) {
        // Apply non-linear transformation for more extreme results
        const traitEffect = Math.pow(traits[trait], 1.5) * weight;
        score += traitEffect;
      }
    });

    // Apply sigmoid function to create more extreme distributions
    return this.sigmoid(score, 0.3, 0.7); // Steeper curve for more spread
  }

  sigmoid(x, min, max) {
    // Custom sigmoid function to create more extreme distributions
    const scaled = (x - 0.5) * 8; // Increase steepness
    const sig = 1 / (1 + Math.exp(-scaled));
    return min + (max - min) * sig;
  }

  calculateCyclicScore(category, dayOfYear, period) {
    const frequency = {
      daily: 1,
      weekly: 1/7,
      monthly: 1/30,
      yearly: 1/365
    }[period];

    const phaseShift = {
      career: 0,
      wealth: Math.PI / 2,
      love: Math.PI,
      health: 3 * Math.PI / 2
    }[category];

    // Combine multiple frequencies for more varied patterns
    const primaryCycle = Math.sin(2 * Math.PI * dayOfYear * frequency + phaseShift);
    const secondaryCycle = Math.sin(3 * Math.PI * dayOfYear * frequency);
    const tertiaryCycle = Math.sin(5 * Math.PI * dayOfYear * frequency);

    // Combine cycles with different weights
    const combinedCycle = (
      primaryCycle * 0.5 +
      secondaryCycle * 0.3 +
      tertiaryCycle * 0.2
    );

    // Apply exponential transformation for more extreme values
    return 0.5 + (Math.sign(combinedCycle) * Math.pow(Math.abs(combinedCycle), 0.7) * 0.5);
  }

  calculateRandomScore(birthday, date, category, period) {
    const seed = `${birthday}-${date.toISOString().slice(0, 10)}-${category}-${period}`;
    const baseRandom = this.generateStableRandom(seed);
    
    // Apply power transformation for more extreme distribution
    return Math.pow(baseRandom, 0.7);
  }

  calculateMultiPeriodScore(birthday, gender, category, zodiacSign, period) {
    const date = new Date();
    const dayOfYear = Math.floor((date - new Date(date.getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
    const weights = this.periodWeights[period];

    // 基础分数计算（使用更极端的分布）
    const baseScore = Math.pow(this.baseFactors[category][zodiacSign], 1.3);
    
    // 增加性别影响范围（-0.15 到 0.15）
    const genderFactor = this.calculateGenderInfluence(gender, category, dayOfYear) * 0.3 - 0.15;
    
    const cyclicScore = this.calculateEnhancedCyclicScore(category, dayOfYear, period);
    const personalScore = this.calculatePersonalScore(birthday, date, period);
    const seasonalScore = this.calculateSeasonalScore(zodiacSign, dayOfYear, period);
    const randomScore = this.calculateStableRandomScore(birthday, date, category, period);

    // 组合所有因素
    let finalScore = (
      baseScore * weights.baseScore +
      seasonalScore * weights.seasonal +
      personalScore * weights.personal +
      cyclicScore * weights.cyclic +
      randomScore * weights.random
    );

    // 应用性别影响
    finalScore = finalScore * (1 + genderFactor);

    // 使用改进的sigmoid函数
    finalScore = this.enhancedSigmoid(finalScore);
    
    // 改进的分数映射方式，使用非线性映射
    return this.mapToFinalScore(finalScore);
  }

  calculateSeasonalScore(zodiacSign, dayOfYear, period) {
    const currentSeason = this.getCurrentSeason();
    // 降低季节基础分数的差异
    const seasonalBase = this.seasonalAffinities[currentSeason].includes(zodiacSign) ? 0.7 : 0.5;
    
    const seasonalPhase = (2 * Math.PI * dayOfYear) / 365;
    const seasonalVariation = Math.sin(seasonalPhase * this.cycleFrequencies[period]);
    
    // 增加变化幅度
    return seasonalBase + (seasonalVariation * 0.3);
  }

  calculatePersonalScore(birthday, currentDate, period) {
    const personalDayNumber = this.calculatePersonalDayNumber(birthday, currentDate);
    const periodFactor = this.cycleFrequencies[period];
    
    const personalPhase = 2 * Math.PI * personalDayNumber * periodFactor;
    // 增加变化幅度
    return 0.5 + (Math.sin(personalPhase) * 0.5);
  }

  calculateCyclicScore(category, dayOfYear, period) {
    const basePhase = (2 * Math.PI * dayOfYear) * this.cycleFrequencies[period];
    const categoryShift = this.categoryPhaseShifts[category];
    
    // Combine multiple frequencies for more natural variation
    const primaryCycle = Math.sin(basePhase + categoryShift);
    const secondaryCycle = Math.sin(basePhase * 2.1); // Slightly offset frequency
    
    return 0.6 + ((primaryCycle * 0.7 + secondaryCycle * 0.3) * 0.4);
  }

  calculateRandomScore(birthday, date, category, period) {
    const seed = `${birthday}-${date.toISOString().slice(0, 10)}-${category}-${period}`;
    const baseRandom = this.generateStableRandom(seed);
    
    // Adjust random range based on period
    const randomRange = {
      daily: 0.4,
      weekly: 0.3,
      monthly: 0.2,
      yearly: 0.1
    }[period];

    return 0.6 + (baseRandom * randomRange);
  }

  normalizeScore(score) {
    // Ensure score follows a reasonable distribution between 1-5
    const normalizedScore = 1 + (score * 4);
    return Math.min(5, Math.max(1, normalizedScore));
  }

  generateFortuneReport(birthDate, gender) {
    const [year, month, day] = birthDate.split('-').map(Number);
    const zodiacSign = this.getZodiacSign(month, day);
    const chineseZodiac = this.getChineseZodiac(year);
    
    const periods = ['daily', 'weekly', 'monthly', 'yearly'];
    const categories = ['career', 'wealth', 'love', 'health'];
    
    const scores = {};
    periods.forEach(period => {
      scores[period] = {};
      categories.forEach(category => {
        scores[period][category] = this.calculateMultiPeriodScore(
          birthDate,
          gender,
          category,
          zodiacSign,
          period
        );
      });
    });

    return {
      basicInfo: {
        zodiacSign,
        chineseZodiac,
        element: this.zodiacTraits[zodiacSign].element
      },
      scores,
      timestamp: new Date().toISOString()
    };
  }

  // 改进的sigmoid函数，产生更分散的分布
  enhancedSigmoid(x) {
    // 增加斜率并移动中心点，使分布更分散
    const shifted = (x - 0.6) * 12;
    return 1 / (1 + Math.exp(-shifted));
  }

  // 新增：非线性分数映射函数
  mapToFinalScore(score) {
    // 使用幂函数进行映射，使分数分布更均匀
    const mapped = Math.pow(score, 1.2);
    // 扩展到1-5范围，并确保分布更均匀
    const finalScore = 1 + 4 * mapped;
    // 确保分数在1-5之间
    return Math.min(5, Math.max(1, finalScore));
  }

  // 增强的周期性分数计算
  calculateEnhancedCyclicScore(category, dayOfYear, period) {
    const frequency = this.cycleFrequencies[period];
    const phase = 2 * Math.PI * dayOfYear * frequency;
    
    // 使用多个频率的叠加创造更自然的变化
    const primary = Math.sin(phase + this.categoryPhaseShifts[category]);
    const secondary = Math.sin(phase * 1.5) * 0.5;
    const tertiary = Math.sin(phase * 2.1) * 0.3;
    
    return (primary + secondary + tertiary) / 1.8;
  }

  // 更稳定的随机分数生成
  calculateStableRandomScore(birthday, date, category, period) {
    const seed = `${birthday}-${date.toISOString().slice(0, 10)}-${category}-${period}`;
    const baseRandom = this.generateStableRandom(seed);
    
    // 使用Beta分布模拟更自然的随机分布
    return Math.pow(baseRandom, 0.7) * (1 - Math.pow(1 - baseRandom, 0.7));
  }
}

// Export calculator instance for React component use
export const horoscopeCalculator = new HoroscopeCalculator();