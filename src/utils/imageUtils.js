import { toPng } from 'html-to-image';

export const handleShare = async (app, id, title, svgRef, onShare, toast) => {
  try {
    if (id) {
      const dataUrl = await getImageData(svgRef);
      onShare(app, id, dataUrl, title);
    } else {
      toast({
        description: "Have nothing to share",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  } catch (error) {
    toast({
      title: "Failed to share",
      description: "Please try again later",
      status: "error",
      duration: 3000,
      isClosable: true,
    });
    console.error('分享图片时出错:', error);
  }
};

const getImageData = (svgRef) => {
  return new Promise((resolve, reject) => {
    const element = svgRef.current

    toPng(element)
      .then((dataUrl) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
          const padding = 30;
          const qrSize = 80;
          const textHeight = 20;
          canvas.width = img.width + padding * 2;
          canvas.height = img.height + padding * 2 + qrSize + textHeight;

          // Set white background
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Draw shadow
          ctx.shadowColor = 'rgba(0, 0, 0, 0.15)';
          ctx.shadowBlur = 10;
          ctx.shadowOffsetX = 4;
          ctx.shadowOffsetY = 6;

          // Draw rounded rectangle for card effect
          ctx.beginPath();
          ctx.roundRect(padding, padding, img.width, img.height, 10);
          ctx.fillStyle = 'white';
          ctx.fill();

          // Reset shadow
          ctx.shadowColor = 'transparent';
          ctx.shadowBlur = 0;
          ctx.shadowOffsetX = 0;
          ctx.shadowOffsetY = 0;

          // Draw the image
          ctx.drawImage(img, padding, padding);

          const qrCodeDataUrl = document.getElementById('qrcode-canvas').toDataURL('image/png');

          if (qrCodeDataUrl) {
            const qrImg = new Image();
            qrImg.onload = () => {
              // Draw QR code with slight shadow
              ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
              ctx.shadowBlur = 5;
              ctx.shadowOffsetX = 2;
              ctx.shadowOffsetY = 2;
              ctx.drawImage(qrImg, canvas.width - qrSize - padding, canvas.height - qrSize - padding, qrSize, qrSize);

              // Reset shadow
              ctx.shadowColor = 'transparent';
              ctx.shadowBlur = 0;
              ctx.shadowOffsetX = 0;
              ctx.shadowOffsetY = 0;

              // Draw "Generated with" text
              ctx.font = '15px Arial';
              ctx.fillStyle = '#666666';
              ctx.textAlign = 'right';
              ctx.fillText('Generated with', canvas.width - padding - qrSize - 18, canvas.height - padding - qrSize + 15);

              // Draw FunBlocks AI Graphics with improved gradient
              const textX = canvas.width - padding - qrSize - 18;
              const gradientWidth = 200; // Fixed width for gradient
              const gradient = ctx.createLinearGradient(
                textX - gradientWidth,
                0,
                textX,
                0
              );
              gradient.addColorStop(0, '#4361ee');
              gradient.addColorStop(1, '#7209b7');

              ctx.font = 'bold 24px Arial';
              ctx.fillStyle = gradient;
              ctx.textAlign = 'right';
              ctx.fillText('FunBlocks AI Graphics', textX, canvas.height - padding - qrSize / 2 + 5);

              // Draw slogan
              ctx.font = '20px Arial';
              ctx.fillStyle = '#444444';
              ctx.textAlign = 'right';
              ctx.fillText('Where wit meets wisdom', textX, canvas.height - padding - 5);

              resolve(canvas.toDataURL('image/png'));
            };
            qrImg.src = qrCodeDataUrl;
          } else {
            resolve(canvas.toDataURL('image/png'));
          }
        };

        img.src = dataUrl;
      })
      .catch((error) => {
        console.error('Error getting image data:', error);
        reject(error);
      });
  });
};

export const handleDownload = async (svgRef, toast) => {
  try {
    const dataUrl = await getImageData(svgRef);
    const link = document.createElement('a');
    link.download = 'artifact.png';
    link.href = dataUrl;
    link.click();
  } catch (error) {
    toast({
      title: "下载图片时出错",
      description: "请稍后再试",
      status: "error",
      duration: 3000,
      isClosable: true,
    });
    console.error('下载图片时出错:', error);
  }
};

export const getProxiedImageUrl =  (url, server_host) => {
  if(url.includes(server_host) || url.includes('/imgproxy?') || url.startsWith('data:')) {
      return url;
  }

  let proxiedUrl = server_host + '/imgproxy?url=' + encodeURIComponent(url);

  return proxiedUrl;
}