import { apiUrl } from './apiUtils';
import { APP_TYPE } from './constants';

export const fetchShowcases = async (app, mode, init_size = 500) => {
    const service = app === APP_TYPE.graphics && 'aiinsights' || '';

    try {
        const response = await fetch(`${apiUrl}/ai/showcase_artifacts?app=${app}&mode=${mode || ''}&service=${service}&pageNum=0&pageSize=${init_size}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data?.data?.length) {
            return data.data;
        }
    } catch (error) {
        console.error('Fetch error:', error);
        throw error; // Rethrow the error after logging it
    }
}