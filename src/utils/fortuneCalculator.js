// 星座运势计算器
// 提供星座判断、运势计算等功能，支持日、周、月、年运势

// 星座日期范围定义
// Zodiac sign date ranges
const ZODIAC_DATES = [
  { name: 'Capric<PERSON>', startMonth: 12, startDay: 22, endMonth: 1, endDay: 19 },
  { name: 'Aquarius', startMonth: 1, startDay: 20, endMonth: 2, endDay: 18 },
  { name: '<PERSON><PERSON><PERSON>', startMonth: 2, startDay: 19, endMonth: 3, endDay: 20 },
  { name: '<PERSON><PERSON>', startMonth: 3, startDay: 21, endMonth: 4, endDay: 19 },
  { name: '<PERSON><PERSON>', startMonth: 4, startDay: 20, endMonth: 5, endDay: 20 },
  { name: 'Gemini', startMonth: 5, startDay: 21, endMonth: 6, endDay: 21 },
  { name: 'Cancer', startMonth: 6, startDay: 22, endMonth: 7, endDay: 22 },
  { name: '<PERSON>', startMonth: 7, startDay: 23, endMonth: 8, endDay: 22 },
  { name: '<PERSON><PERSON><PERSON>', startMonth: 8, startDay: 23, endMonth: 9, endDay: 22 },
  { name: 'Libra', startMonth: 9, startDay: 23, endMonth: 10, endDay: 23 },
  { name: 'Scorpio', startMonth: 10, startDay: 24, endMonth: 11, endDay: 22 },
  { name: 'Sagittarius', startMonth: 11, startDay: 23, endMonth: 12, endDay: 21 }
];

// Chinese zodiac animals
const CHINESE_ZODIAC = [
  'Rat', 'Ox', 'Tiger', 'Rabbit', 'Dragon', 'Snake',
  'Horse', 'Goat', 'Monkey', 'Rooster', 'Dog', 'Pig'
];

// Five elements mapping
const FIVE_ELEMENTS = {
  'Capricorn': 'Earth',
  'Aquarius': 'Air',
  'Pisces': 'Water',
  'Aries': 'Fire',
  'Taurus': 'Earth',
  'Gemini': 'Air',
  'Cancer': 'Water',
  'Leo': 'Fire',
  'Virgo': 'Earth',
  'Libra': 'Air',
  'Scorpio': 'Water',
  'Sagittarius': 'Fire'
};
// 运势类别
const FORTUNE_CATEGORIES = ['career', 'wealth', 'love', 'health'];

class FortuneCalculator {
  constructor() {
    this.seedrandom = require('seedrandom');
  }

  // Calculate zodiac sign
  getZodiacSign(month, day) {
    for (const zodiac of ZODIAC_DATES) {
      if (
        (month === zodiac.startMonth && day >= zodiac.startDay) ||
        (month === zodiac.endMonth && day <= zodiac.endDay)
      ) {
        return zodiac.name;
      }
    }
    return ZODIAC_DATES[0].name; // Default to Capricorn
  }

  // Calculate Chinese zodiac
  getChineseZodiac(year) {
    return CHINESE_ZODIAC[(year - 4) % 12];
  }

  // 计算基准运势值
  _calculateBaseScore(zodiacSign, timeUnit, timeValue, category, chineseZodiac = '') {
    // 结合时间单位、生肖等信息生成种子
    const seed = `${zodiacSign}-${timeUnit}-${timeValue}-${category}-${chineseZodiac}`;
    const rng = this.seedrandom(seed);

    // 生成基准值(2-4之间)
    return 2 + rng() * 2;
  }

  // 计算每日运势
  _calculateDailyFortune(zodiacSign, date, category, baseScore) {
    const rng = this.seedrandom(`${date.toISOString()}-${zodiacSign}-${category}`);
    const variation = (rng() - 0.5) * 0.5;
    return Math.max(0, Math.min(5, baseScore + variation));
  }

  // 计算周运势
  _calculateWeeklyFortune(zodiacSign, year, week, category, chineseZodiac) {
    const baseScore = this._calculateBaseScore(zodiacSign, 'week', week, category, chineseZodiac);
    const rng = this.seedrandom(`${year}-W${week}-${zodiacSign}-${category}`);
    const variation = (rng() - 0.5) * 0.3; // 周运势波动相对较小
    return Math.max(0, Math.min(5, baseScore + variation));
  }

  // 计算月运势
  _calculateMonthlyFortune(zodiacSign, year, month, category, chineseZodiac) {
    const baseScore = this._calculateBaseScore(zodiacSign, 'month', month, category, chineseZodiac);
    const rng = this.seedrandom(`${year}-M${month}-${zodiacSign}-${category}`);
    const variation = (rng() - 0.5) * 0.2; // 月运势波动更小
    return Math.max(0, Math.min(5, baseScore + variation));
  }

  // 计算年运势
  _calculateYearlyFortune(zodiacSign, year, category, chineseZodiac) {
    const baseScore = this._calculateBaseScore(zodiacSign, 'year', year, category, chineseZodiac);
    const rng = this.seedrandom(`${year}-${zodiacSign}-${category}-${chineseZodiac}`);
    const variation = (rng() - 0.5) * 0.1; // 年运势波动最小
    return Math.max(0, Math.min(5, baseScore + variation));
  }

  // 计算完整运势报告
  calculateFullFortune(horoscopeSettings, birthYear, birthMonth, birthDay, gender, date = new Date()) {
    const zodiacSign = this.getZodiacSign(birthMonth, birthDay);
    const chineseZodiac = this.getChineseZodiac(birthYear);

    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const week = this._getWeekNumber(date);

    // 计算各个时间维度的运势
    const fortunesByPeriod = {
      daily: {},
      weekly: {},
      monthly: {},
      yearly: {}
    };

    // 计算每个类别的运势
    FORTUNE_CATEGORIES.forEach(category => {
      // 计算年运势作为基础
      const yearlyScore = this._calculateYearlyFortune(zodiacSign, year, category, chineseZodiac);

      // 基于年运势计算月运势
      const monthlyScore = this._calculateMonthlyFortune(zodiacSign, year, month, category, chineseZodiac);

      // 基于月运势计算周运势
      const weeklyScore = this._calculateWeeklyFortune(zodiacSign, year, week, category, chineseZodiac);

      // 基于周运势计算日运势
      const dailyScore = this._calculateDailyFortune(zodiacSign, date, category, weeklyScore);

      // 存储各个时间维度的运势
      fortunesByPeriod.daily[category] = {
        score: Math.round(dailyScore * 100) / 100,
        level: this._getFortuneLevel(dailyScore),
        trend: this._getFortuneTrend(weeklyScore, dailyScore)
      };

      if (horoscopeSettings?.range != 'today') {
        fortunesByPeriod.weekly[category] = {
          score: Math.round(weeklyScore * 100) / 100,
          level: this._getFortuneLevel(weeklyScore),
          trend: this._getFortuneTrend(monthlyScore, weeklyScore)
        };

        fortunesByPeriod.monthly[category] = {
          score: Math.round(monthlyScore * 100) / 100,
          level: this._getFortuneLevel(monthlyScore),
          trend: this._getFortuneTrend(yearlyScore, monthlyScore)
        };

        fortunesByPeriod.yearly[category] = {
          score: Math.round(yearlyScore * 100) / 100,
          level: this._getFortuneLevel(yearlyScore),
          trend: 'Annual benchmark'
        };
      }
    });

    // 计算各时间维度的平均分
    const averageScores = {};
    Object.keys(fortunesByPeriod).forEach(period => {
      averageScores[period] = Math.round(
        Object.values(fortunesByPeriod[period])
          .reduce((sum, fortune) => sum + fortune.score, 0)
        / FORTUNE_CATEGORIES.length * 100
      ) / 100;
    });

    // 返回完整的运势数据
    return {
      basicInfo: {
        birthDate: `${birthYear}-${birthMonth}-${birthDay}`,
        gender,
        zodiacSign,
        chineseZodiac
      },
      date: date.toISOString().split('T')[0],
      week,
      month,
      year,
      fortunes: fortunesByPeriod,
      averageScores
    };
  }

  // 辅助方法
  _isDateInRange(date, start, end) {
    const [startMonth, startDay] = start.split('-').map(Number);
    const [endMonth, endDay] = end.split('-').map(Number);
    const [month, day] = date.split('-').map(Number);

    if (startMonth > endMonth) {
      return (month > startMonth || (month === startMonth && day >= startDay)) ||
        (month < endMonth || (month === endMonth && day <= endDay));
    } else {
      return (month > startMonth || (month === startMonth && day >= startDay)) &&
        (month < endMonth || (month === endMonth && day <= endDay));
    }
  }

  _getWeekNumber(date) {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDays = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDays + firstDayOfYear.getDay() + 1) / 7);
  }

  _getFortuneLevel(score) {
    if (score >= 4.5) return 'Excellent';
    if (score >= 4.0) return 'Very Good';
    if (score >= 3.5) return 'Good';
    if (score >= 3.0) return 'Fairly Good';
    if (score >= 2.5) return 'Average';
    if (score >= 2.0) return 'Below Average';
    if (score >= 1.5) return 'Poor';
    if (score >= 1.0) return 'Very Poor';
    return 'Extremely Poor';
  }

  _getFortuneTrend(baseScore, currentScore) {
    const diff = currentScore - baseScore;
    if (diff > 0.2) return 'Rising';
    if (diff < -0.2) return 'Falling';
    return 'Stable';
  }
}

const fortuneCalculator = new FortuneCalculator();
export default fortuneCalculator;