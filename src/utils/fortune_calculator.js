
class FortuneCalculator {
    // 星座运势计算系统
    static ZODIAC_DATES = [
        { name: 'Capricorn', startMonth: 12, startDay: 22, endMonth: 1, endDay: 19 },
        { name: 'Aquarius', startMonth: 1, startDay: 20, endMonth: 2, endDay: 18 },
        { name: '<PERSON><PERSON><PERSON>', startMonth: 2, startDay: 19, endMonth: 3, endDay: 20 },
        { name: '<PERSON><PERSON>', startMonth: 3, startDay: 21, endMonth: 4, endDay: 19 },
        { name: '<PERSON><PERSON>', startMonth: 4, startDay: 20, endMonth: 5, endDay: 20 },
        { name: '<PERSON>', startMonth: 5, startDay: 21, endMonth: 6, endDay: 21 },
        { name: '<PERSON>', startMonth: 6, startDay: 22, endMonth: 7, endDay: 22 },
        { name: '<PERSON>', startMonth: 7, startDay: 23, endMonth: 8, endDay: 22 },
        { name: '<PERSON><PERSON><PERSON>', startMonth: 8, startDay: 23, endMonth: 9, endDay: 22 },
        { name: '<PERSON><PERSON>', startMonth: 9, startDay: 23, endMonth: 10, endDay: 23 },
        { name: 'Scorpio', startMonth: 10, startDay: 24, endMonth: 11, endDay: 22 },
        { name: 'Sagittarius', startMonth: 11, startDay: 23, endMonth: 12, endDay: 21 }
    ];

    // Chinese zodiac animals
    static CHINESE_ZODIAC = [
        'Rat', 'Ox', 'Tiger', 'Rabbit', 'Dragon', 'Snake',
        'Horse', 'Goat', 'Monkey', 'Rooster', 'Dog', 'Pig'
    ];

    // Five elements mapping
    static FIVE_ELEMENTS = {
        'Capricorn': 'Earth',
        'Aquarius': 'Air',
        'Pisces': 'Water',
        'Aries': 'Fire',
        'Taurus': 'Earth',
        'Gemini': 'Air',
        'Cancer': 'Water',
        'Leo': 'Fire',
        'Virgo': 'Earth',
        'Libra': 'Air',
        'Scorpio': 'Water',
        'Sagittarius': 'Fire'
    };


    // 运势类别
    static FORTUNE_CATEGORIES = ['career', 'wealth', 'love', 'health'];

    constructor() {
        this.seedrandom = require('seedrandom');
    }

    getZodiacSign(month, day) {
        console.log('month, day.............', month, day)
        for (const zodiac of FortuneCalculator.ZODIAC_DATES) {
          if (
            (month === zodiac.startMonth && day >= zodiac.startDay) ||
            (month === zodiac.endMonth && day <= zodiac.endDay)
          ) {
            return zodiac.name;
          }
        }
        return FortuneCalculator.ZODIAC_DATES[0].name; // Default to Capricorn
      }

    // 获取生肖
    getChineseZodiac(year) {
        return FortuneCalculator.CHINESE_ZODIAC[(year - 4) % 12];
    }

    // 生成稳定的随机数
    generateStableRandom(seed, min = 0, max = 1) {
        const rng = this.seedrandom(seed);
        return min + rng() * (max - min);
    }

    // 计算基础运势值
    calculateBaseScore(birthData, date, category) {
        const { year, month, day, gender, zodiacSign, chineseZodiac } = birthData;

        // 使用多个因素作为种子
        const seed = `${zodiacSign}-${chineseZodiac}-${gender}-${date}-${category}`;
        const baseScore = this.generateStableRandom(seed, 1, 5);

        // 根据五行相生相克调整分数
        const elementAdjustment = this.generateStableRandom(`element-${seed}`, -0.5, 0.5);

        // 性别影响（微调）
        const genderAdjustment = this.generateStableRandom(`gender-${seed}`, -0.2, 0.2);

        return Math.max(0, Math.min(5, baseScore + elementAdjustment + genderAdjustment));
    }

    // 计算周期运势
    calculatePeriodScore(birthData, period, category) {
        const { startDate, endDate } = period;
        const seed = `${period.type}-${startDate}-${endDate}`;

        // 基础运势
        const baseScore = this.calculateBaseScore(birthData, startDate, category);

        // 周期调整
        const periodAdjustment = this.generateStableRandom(seed, -0.3, 0.3);

        return Math.max(0, Math.min(5, baseScore + periodAdjustment));
    }

    // 生成完整运势报告数据
    generateFortuneReport(birthYear, birthMonth, birthDay, gender) {
        const birthData = {
            year: birthYear,
            month: birthMonth,
            day: birthDay,
            gender: gender,
            zodiacSign: this.getZodiacSign(birthMonth, birthDay),
            chineseZodiac: this.getChineseZodiac(birthYear)
        };

        const today = new Date();
        const periods = {
            daily: {
                type: 'daily',
                startDate: today.toISOString().split('T')[0],
                endDate: today.toISOString().split('T')[0]
            },
            weekly: {
                type: 'weekly',
                startDate: new Date(today.setDate(today.getDate() - today.getDay())).toISOString().split('T')[0],
                endDate: new Date(today.setDate(today.getDate() + 6)).toISOString().split('T')[0]
            },
            monthly: {
                type: 'monthly',
                startDate: new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0],
                endDate: new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0]
            },
            yearly: {
                type: 'yearly',
                startDate: new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0],
                endDate: new Date(today.getFullYear(), 11, 31).toISOString().split('T')[0]
            }
        };

        const fortune = {
            basicInfo: {
                zodiacSign: birthData.zodiacSign,
                chineseZodiac: birthData.chineseZodiac,
                element: FortuneCalculator.FIVE_ELEMENTS[birthData.zodiacSign]
            },
            scores: {}
        };

        // 计算各个周期的运势分数
        Object.entries(periods).forEach(([periodKey, periodData]) => {
            fortune.scores[periodKey] = {};
            FortuneCalculator.FORTUNE_CATEGORIES.forEach(category => {
                fortune.scores[periodKey][category] =
                    this.calculatePeriodScore(birthData, periodData, category);
            });
        });

        // 添加运势解读提示信息
        fortune.interpretation = {
            zodiacCharacteristics: {
                personality: `${birthData.zodiacSign}的典型性格特征`,
                element: `五行属性：${FortuneCalculator.FIVE_ELEMENTS[birthData.zodiacSign]}`,
                compatibility: `与其他星座的互动关系`
            },
            periodHighlights: Object.entries(fortune.scores).map(([period, scores]) => ({
                period,
                highestCategory: Object.entries(scores).reduce((a, b) =>
                    scores[a] > scores[b] ? a : b)[0],
                lowestCategory: Object.entries(scores).reduce((a, b) =>
                    scores[a] < scores[b] ? a : b)[0]
            }))
        };

        return fortune;
    }
}

// 创建实例并导出
const fortuneCalculator = new FortuneCalculator();
export default fortuneCalculator;