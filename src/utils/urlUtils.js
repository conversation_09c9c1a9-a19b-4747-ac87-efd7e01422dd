import urlRegex from 'url-regex'

export const isValidURL = (string) => {
  return urlRegex().test(string)
}

export const fetchUrlContent = async (url) => {
  try {
    const apiUrl = 'https://r.jina.ai/';
    const response = await fetch(`${apiUrl}${url}`, {
      headers: {
        'Accept': 'application/json'
      }
    });

    // 检查特定的 HTTP 状态码
    if (response.status === 404) {
      console.error('Resource not found (404)');
      return { error: 'NOT_FOUND' };
    } else if (response.status === 403) {
      console.error('Access forbidden (403)');
      return { error: 'FORBIDDEN' };
    } else if (response.status === 500) {
      console.error('Internal server error (500)');
      return { error: 'SERVER_ERROR' };
    } else if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return { error: 'FAIL_TO_FETCH_URL', status: response.status };
    }

    const data = await response.json();

    // 验证返回的数据结构
    if (!data || typeof data !== 'object' || !('data' in data)) {
      console.error('Invalid response structure');
      return { error: 'INVALID_RESPONSE' };
    }

    // 检查 data 属性是否为空或无效
    if (!data.data || typeof data.data !== 'object') {
      console.error('Empty or invalid content in response');
      return { error: 'EMPTY_CONTENT' };
    }

    // console.log('data.data', data.data)

    return data.data;
  } catch (error) {
    console.error('Error fetching URL content:', error.message);
    return { error: 'FETCH_ERROR', message: error.message };
  }
};
