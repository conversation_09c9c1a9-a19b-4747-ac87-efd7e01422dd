import React from 'react';
import { VStack, Heading, Text, Box, SimpleGrid, Icon, Button, Image, Flex, List, ListItem, ListIcon, Accordion, AccordionItem, AccordionButton, AccordionPanel, Link, useBreakpointValue } from '@chakra-ui/react';
import { FaRobot, FaShareAlt, FaPencilAlt, FaLightbulb, FaArrowRight, FaCheckCircle, FaSmile, FaBrain, FaUsers, FaStethoscope, FaMicroscope } from 'react-icons/fa';
import { AddIcon } from '@chakra-ui/icons';
import { useTranslation } from 'next-i18next';
import getConfig from 'next/config';
const FeatureBox = ({ icon, title, description }) => (
    <Box borderWidth={1} borderRadius="lg" p={5} textAlign="center" boxShadow="md" transition="all 0.3s" _hover={{ transform: 'translateY(-5px)', boxShadow: 'lg' }}>
        <Icon as={icon} w={10} h={10} color="purple.500" mb={4} />
        <Heading as="h3" size="md" mb={2}>
            {title}
        </Heading>
        <Text>{description}</Text>
    </Box>
);

const ProductIntro = () => {
    const { t } = useTranslation('common');
    const isMobile = useBreakpointValue({ base: true, md: false })
    const { basePathGraphics } = getConfig().publicRuntimeConfig;
    const features = [
        {
            icon: FaRobot,
            title: t('ai_driven_insights'),
            description: t('ai_driven_insights_desc')
        },
        {
            icon: FaPencilAlt,
            title: t('creativity_booster'),
            description: t('creativity_booster_desc')
        },
        {
            icon: FaLightbulb,
            title: t('humor_and_wisdom'),
            description: t('humor_and_wisdom_desc')
        },
        {
            icon: FaShareAlt,
            title: t('easy_sharing'),
            description: t('easy_sharing_desc')
        }
    ];

    const contentFeatures = [
        t('deep_insights'),
        t('quality_content'),
        t('innovative_thinking'),
        t('humor_integration'),
    ];

    const funFeatures = [
        {
            icon: FaSmile,
            title: t('endless_fun'),
            description: t('endless_fun_desc')
        },
        {
            icon: FaPencilAlt,
            title: t('novel_format'),
            description: t('novel_format_desc')
        },
        {
            icon: FaBrain,
            title: t('stimulate_thinking'),
            description: t('stimulate_thinking_desc')
        },
        {
            icon: FaUsers,
            title: t('enhance_social_interaction'),
            description: t('enhance_social_interaction_desc')
        }
    ];

    const faqItems = [
        {
            question: t('what_is_ai_insights'),
            answer: t('what_is_ai_insights_answer')
        },
        {
            question: t('why_infographic_cards'),
            answer: t('why_infographic_cards_answer')
        },
        {
            question: t('what_can_ai_insights_do'),
            answer: t('what_can_ai_insights_do_answer')
        },
        {
            question: t('how_to_use_ai_insights'),
            answer: t('how_to_use_ai_insights_answer')
        },
        {
            question: t('difference_of_three_modes'),
            answer: t('difference_of_three_modes_answer')
        },
        {
            question: t('can_content_be_edited'),
            answer: t('can_content_be_edited_answer')
        },
        {
            question: t('can_i_share_cards'),
            answer: t('can_i_share_cards_answer')
        },
        {
            question: t('is_content_true'),
            answer: t('is_content_true_answer')
        },
        {
            question: t('is_content_original'),
            answer: t('is_content_original_answer')
        },
        {
            question: t('can_other_ai_generate_cards'),
            answer: t('can_other_ai_generate_cards_answer')
        },
        {
            question: t('difference_from_other_ai'),
            answer: t('difference_from_other_ai_answer')
        },
        {
            question: t('in_common_as_chatgpt'),
            answer: t('in_common_as_chatgpt_answer')
        },
        {
            question: t('is_ai_insights_free'),
            answer: t('is_ai_insights_free_answer')
        }
    ];

    return (
        <VStack spacing={12} align="stretch" py={8}>
            <Flex direction={{ base: 'column', md: 'row' }} align="center" justify="space-between">
                <Box flex={1} pr={{ base: 0, md: 8 }}>
                    <Heading as="h1" size={isMobile ? 'xl' : '2xl'} mb={4} color="purple.600">
                        FunBlocks AI Graphics
                    </Heading>
                    <Heading as="h2" size="lg" mb={6} fontWeight="normal">
                        {t('fun_and_informative')}
                    </Heading>
                    <Text fontSize="lg">
                        {t('explore_ai_thoughts_and_humor')}
                    </Text>
                    <Text fontSize="lg" mb={8}>
                        {t('generate_insightful_and_fun_cards')}
                    </Text>
                    <Button rightIcon={<FaArrowRight />} colorScheme="purple" size="lg" as={Link} href={basePathGraphics}>
                        {t('try_now')}
                    </Button>
                </Box>
                {/* <Box flex={1} mt={{ base: 8, md: 0 }}>
                    <Image src="/path/to/product-demo.gif" alt="FunBlocks AI Graphics 演示" borderRadius="lg" boxShadow="2xl" />
                </Box> */}
            </Flex>

            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={10}>
                {features.map((feature, index) => (
                    <FeatureBox key={index} {...feature} />
                ))}
            </SimpleGrid>

            {/* 新添加的 section */}
            <Box bg="purple.50" p={8} borderRadius="lg" mt={12}>
                <Heading as="h3" size="lg" mb={6} textAlign="center" color="purple.600">
                    {t('deep_informative_content')}
                </Heading>
                <Text fontSize="lg" mb={6} textAlign="center">
                    {t('funblocks_ai_insights_unique')}
                </Text>
                <List spacing={3}>
                    {contentFeatures.map((feature, index) => (
                        <ListItem key={index} display="flex" alignItems="center">
                            <ListIcon as={FaCheckCircle} color="purple.500" />
                            <Text fontSize="md">{feature}</Text>
                        </ListItem>
                    ))}
                </List>
            </Box>

            {/* New Section for Three Generation Modes */}
            <Box bg="green.50" p={8} borderRadius="lg" mt={12}>
                <Heading as="h3" size="lg" mb={6} textAlign="center" color="green.600">
                    {t('three_generation_modes')}
                </Heading>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    <Box bg="white" p={5} borderRadius="md" boxShadow="md">
                        <Flex align="center" mb={3}>
                            <Icon as={FaMicroscope} w={6} h={6} color="green.500" mr={3} />
                            <Heading as="h4" size="md">{t('image_insights')}</Heading>
                        </Flex>
                        <Text>{t('image_insights_description')}</Text>
                    </Box>
                    <Box bg="white" p={5} borderRadius="md" boxShadow="md">
                        <Flex align="center" mb={3}>
                            <Icon as={FaBrain} w={6} h={6} color="green.500" mr={3} />
                            <Heading as="h4" size="md">{t('graph_chat')}</Heading>
                        </Flex>
                        <Text>{t('graph_chat_description')}</Text>
                    </Box>
                    <Box bg="white" p={5} borderRadius="md" boxShadow="md">
                        <Flex align="center" mb={3}>
                            <Icon as={FaLightbulb} w={6} h={6} color="green.500" mr={3} />
                            <Heading as="h4" size="md">{t('card_guru')}</Heading>
                        </Flex>
                        <Text>{t('card_guru_description')}</Text>
                    </Box>
                    <Box bg="white" p={5} borderRadius="md" boxShadow="md">
                        <Flex align="center" mb={3}>
                            <Icon as={FaShareAlt} w={6} h={6} color="green.500" mr={3} />
                            <Heading as="h4" size="md">{t('make_graph')}</Heading>
                        </Flex>
                        <Text>{t('make_graph_description')}</Text>
                    </Box>
                </SimpleGrid>
            </Box>

            <Box bg="gray.50" p={8} borderRadius="lg" mt={12}>
                <Heading as="h3" size="lg" mb={4} textAlign="center">
                    {t('why_choose_funblocks_ai_insights')}
                </Heading>
                <Text fontSize="lg" textAlign="center">
                    {t('funblocks_ai_insights_combines_llm_and_card_features')}
                </Text>
            </Box>

            {/* 新添加的 section */}
            <Box bg="blue.50" p={8} borderRadius="lg" mt={12}>
                <Heading as="h3" size="lg" mb={6} textAlign="center" color="blue.600">
                    {t('fun_social_tool')}
                </Heading>
                <Text fontSize="lg" mb={8} textAlign="center">
                    {t('funblocks_ai_insights_thought_stimulator')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {funFeatures.map((feature, index) => (
                        <Box key={index} bg="white" p={5} borderRadius="md" boxShadow="md">
                            <Flex align="center" mb={3}>
                                <Icon as={feature.icon} w={6} h={6} color="blue.500" mr={3} />
                                <Heading as="h4" size="md">{feature.title}</Heading>
                            </Flex>
                            <Text>{feature.description}</Text>
                        </Box>
                    ))}
                </SimpleGrid>
            </Box>

            {/* New Call to Action Section */}
            <Box bg="purple.100" p={12} borderRadius="lg" mt={12} textAlign="center">
                <Heading as="h2" size="xl" mb={4} color="purple.700">
                    {t('one_click_joy')}
                </Heading>
                <Text fontSize="xl" mb={6}>
                    {t('experience_ai_insights_now')}
                </Text>
                <Button
                    rightIcon={<FaArrowRight />}
                    colorScheme="purple"
                    size="lg"
                    as={Link}
                    href={basePathGraphics}
                    px={8}
                    py={6}
                    fontSize="xl"
                >
                    {t('start_generating')}
                </Button>
            </Box>

            <Heading as="h2" size="xl" textAlign="center" pt={12} pb={4}>
                {t('faq')}
            </Heading>
            <Accordion allowMultiple>
                {faqItems.map((item, index) => (
                    <AccordionItem key={index}>
                        <h2>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="bold">{item.question}</Text>
                                </Box>
                                <AddIcon />
                            </AccordionButton>
                        </h2>
                        <AccordionPanel pb={4}>
                            <Text>{item.answer}</Text>
                        </AccordionPanel>
                    </AccordionItem>
                ))}
            </Accordion>

            {/* New Section for Other FunBlocks AI Products */}
            <Box bg="gray.100" p={8} borderRadius="lg" mt={12} textAlign="center">
                <Heading as="h3" size="lg" mb={4} color="gray.700">
                    {t('explore_other_products')}
                </Heading>
                <List spacing={3}>
                    <ListItem>
                        <Link href="https://www.funblocks.net/welcome_extension.html" color="blue.500" fontSize="lg">
                            FunBlocks AI Extension
                        </Link>
                        <Text fontSize="md" color="gray.600">
                            {t('boost_productivity')}
                        </Text>
                    </ListItem>
                    <ListItem>
                        <Link href="https://www.funblocks.net/aiflow.html" color="blue.500" fontSize="lg">
                            FunBlocks AI Flow
                        </Link>
                        <Text fontSize="md" color="gray.600">
                            {t('unleash_creativity')}
                        </Text>
                    </ListItem>
                    <ListItem>
                        <Link href="https://www.funblocks.net/slides.html" color="blue.500" fontSize="lg">
                            FunBlocks AI Slides
                        </Link>
                        <Text fontSize="md" color="gray.600">
                            {t('create_stunning_presentations')}
                        </Text>
                    </ListItem>
                    <ListItem>
                        <Link href="https://www.funblocks.net/" color="blue.500" fontSize="lg">
                            FunBlocks AI
                        </Link>
                        <Text fontSize="md" color="gray.600">
                            {t('transform_workflow')}
                        </Text>
                    </ListItem>
                </List>
            </Box>

            {/* Add Contact Us section */}
            <Box textAlign="center" marginTop={8}>
                <Button as={Link} href="https://discord.gg/XtdZFBy4uR" target="_blank" colorScheme="teal">
                    {t('contact_us')}
                </Button>
            </Box>
        </VStack>
    );
};

export default ProductIntro;
