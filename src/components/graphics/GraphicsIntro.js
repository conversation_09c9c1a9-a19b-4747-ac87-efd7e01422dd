import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon, Button, Image, Flex, Badge, HStack, Divider, Grid, GridItem } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaChartBar, FaMagic, FaLightbulb, FaClock, FaPalette, FaChartLine, FaSitemap, FaRegObjectGroup, FaDownload, FaShareAlt, FaArrowRight, FaCheck, FaRocket, FaBuffer, FaUserTie, FaSchool, FaNewspaper, FaFileInvoice, FaRobot, FaPencilAlt, FaCheckCircle, FaSmile, FaBrain, FaUsers, FaStethoscope, FaMicroscope, FaRegLightbulb, FaStar, FaGraduationCap, FaProjectDiagram, FaBullhorn, FaEye } from 'react-icons/fa'
import { MdCheckCircle, MdAutoAwesome, MdTimeline, MdCategory, MdInsights, MdBarChart, MdCompareArrows, MdAccessTime, MdOutlineDesignServices } from 'react-icons/md'
import { AddIcon } from '@chakra-ui/icons'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'
import Testimonials from '../common/Testimonials'
import CaseStudies from '../common/CaseStudies'
import ComparisonTable from '../common/ComparisonTable'
import ResearchBacked from '../common/ResearchBacked'

const appname = 'AI Graphics Generator'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={5}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={3}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

// Example graphics card component
const ExampleCard = ({ title, description, imageSrc }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })

    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            overflow="hidden"
            h="100%"
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
        >
            <Image
                src={imageSrc}
                alt={title}
                height={isMobile ? "240px" : "320px"}
                width="100%"
                objectFit="contain"
            />
            <Box p={5}>
                <Heading size="md" mb={2}>{title}</Heading>
                <Text color="gray.600">{description}</Text>
            </Box>
        </Box>
    )
}

// Comparison feature component
const ComparisonFeature = ({ feature, traditional, aiPowered, withSeperator = true }) => {
    return (
        <Box borderBottomWidth={withSeperator ? "1px" : "0px"} py={3}>
            <Grid templateColumns="repeat(3, 1fr)">
                <GridItem>
                    <Text fontWeight="medium">{feature}</Text>
                </GridItem>
                <GridItem>
                    <Flex align="center">
                        <Text color="gray.500">{traditional}</Text>
                    </Flex>
                </GridItem>
                <GridItem>
                    <Flex align="center">
                        <Text color="blue.500" fontWeight="bold">{aiPowered}</Text>
                    </Flex>
                </GridItem>
            </Grid>
        </Box>
    )
}

const GraphicsIntro = () => {
    const { t } = useTranslation('graphics')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaMagic,
            title: t('graphics_feature_1_title'),
            text: t('graphics_feature_1_text')
        },
        {
            icon: MdAutoAwesome,
            title: t('graphics_feature_2_title'),
            text: t('graphics_feature_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('graphics_feature_3_title'),
            text: t('graphics_feature_3_text')
        },
        {
            icon: FaShareAlt,
            title: t('graphics_feature_4_title'),
            text: t('graphics_feature_4_text')
        }
    ]

    const visualTypes = [
        {
            icon: FaSitemap,
            title: t('graphics_visual_1_title'),
            text: t('graphics_visual_1_text')
        },
        {
            icon: FaLightbulb,
            title: t('graphics_visual_2_title'),
            text: t('graphics_visual_2_text')
        },
        {
            icon: FaShareAlt,
            title: t('graphics_visual_3_title'),
            text: t('graphics_visual_3_text')
        }

    ]

    // Content features from ProductIntro
    const contentFeatures = [
        t('deep_insights'),
        t('quality_content'),
        t('innovative_thinking'),
        t('humor_integration'),
    ]

    // Fun features from ProductIntro
    const funFeatures = [
        {
            icon: FaSmile,
            title: t('endless_fun'),
            description: t('endless_fun_desc')
        },
        {
            icon: FaPencilAlt,
            title: t('novel_format'),
            description: t('novel_format_desc')
        },
        {
            icon: FaBrain,
            title: t('stimulate_thinking'),
            description: t('stimulate_thinking_desc')
        },
        {
            icon: FaUsers,
            title: t('enhance_social_interaction'),
            description: t('enhance_social_interaction_desc')
        }
    ]

    // FAQ items from ProductIntro
    const additionalFaqItems = [
        {
            question: t('why_infographic_cards'),
            answer: t('why_infographic_cards_answer')
        },
        {
            question: t('how_to_use_ai_insights'),
            answer: t('how_to_use_ai_insights_answer')
        },
        {
            question: t('difference_of_three_modes'),
            answer: t('difference_of_three_modes_answer')
        },
        {
            question: t('can_content_be_edited'),
            answer: t('can_content_be_edited_answer')
        },
        {
            question: t('can_i_share_cards'),
            answer: t('can_i_share_cards_answer')
        },
        {
            question: t('is_content_true'),
            answer: t('is_content_true_answer')
        },
        {
            question: t('is_content_original'),
            answer: t('is_content_original_answer')
        },
        {
            question: t('can_other_ai_generate_cards'),
            answer: t('can_other_ai_generate_cards_answer')
        },
        {
            question: t('difference_from_other_ai'),
            answer: t('difference_from_other_ai_answer')
        },
        {
            question: t('in_common_as_chatgpt'),
            answer: t('in_common_as_chatgpt_answer')
        },
        {
            question: t('is_ai_insights_free'),
            answer: t('is_ai_insights_free_answer')
        }
    ]

    // 示例数据
    const examples = [
        {
            title: t('graphics_example_1_title'),
            description: t('graphics_example_1_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_piechart.png"
        },
        {
            title: t('graphics_example_2_title'),
            description: t('graphics_example_2_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_process.png"
        },
        {
            title: t('graphics_example_3_title'),
            description: t('graphics_example_3_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_conceptchart.png"
        },
        {
            title: t('graphics_example_4_title'),
            description: t('graphics_example_4_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_insights.png"
        }
    ]

    // Comparison data
    const comparisonData = [
        {
            feature: t('comparison_time'),
            traditional: t('comparison_time_traditional'),
            aiPowered: t('comparison_time_ai')
        },
        {
            feature: t('comparison_skills'),
            traditional: t('comparison_skills_traditional'),
            aiPowered: t('comparison_skills_ai')
        },
        {
            feature: t('comparison_design'),
            traditional: t('comparison_design_traditional'),
            aiPowered: t('comparison_design_ai')
        },
        {
            feature: t('comparison_variety'),
            traditional: t('comparison_variety_traditional'),
            aiPowered: t('comparison_variety_ai')
        },
        {
            feature: t('comparison_iteration'),
            traditional: t('comparison_iteration_traditional'),
            aiPowered: t('comparison_iteration_ai')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={isMobile ? 6 : 16}>
            {/* Hero section */}
            <Box
                w="100%"
                bgGradient="linear(to-br, blue.50, purple.50)"
                py={isMobile ? 12 : 16}
                px={4}
                borderBottomWidth="1px"
                borderBottomColor="gray.200"
            >
                <VStack spacing={8} maxW="4xl" mx="auto" textAlign="center">
                    <Badge colorScheme="blue" fontSize={isMobile ? "sm" : "md"} px={3} py={1} borderRadius="full">
                        {t('hero_badge')}
                    </Badge>

                    <Heading
                        fontSize={isMobile ? "3xl" : "5xl"}
                        fontWeight="extrabold"
                        bgGradient="linear(to-r, blue.400, purple.500)"
                        bgClip="text"
                        lineHeight="1.2"
                    >
                        {t('hero_heading')}
                    </Heading>

                    <Text fontSize={isMobile ? "lg" : "xl"} color="gray.600" maxW="2xl">
                        {t('hero_description')}
                    </Text>

                    <HStack spacing={4}>
                        <Button
                            size="lg"
                            colorScheme="blue"
                            rightIcon={<FaArrowRight />}
                            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        >
                            {t('hero_start_button')}
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            colorScheme="purple"
                            onClick={() => document.querySelector('#examples').scrollIntoView({ behavior: 'smooth' })}
                        >
                            {t('hero_examples_button')}
                        </Button>
                    </HStack>

                    {/* Metrics */}
                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={isMobile ? 4 : 8} width="100%" pt={8}>
                        <VStack>
                            <Heading size="xl" color="blue.500">{t('hero_metric_1_number')}</Heading>
                            <Text color="gray.600">{t('hero_metric_1_text')}</Text>
                        </VStack>
                        <VStack>
                            <Heading size="xl" color="purple.500">{t('hero_metric_2_number')}</Heading>
                            <Text color="gray.600">{t('hero_metric_2_text')}</Text>
                        </VStack>
                        <VStack>
                            <Heading size="xl" color="green.500">{t('hero_metric_3_number')}</Heading>
                            <Text color="gray.600">{t('hero_metric_3_text')}</Text>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Box>

            <Section bg="gray.50" title={t('graphics_visual_types_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {visualTypes.map((type, index) => (
                        <Feature
                            key={index}
                            icon={type.icon}
                            title={type.title}
                            text={type.text}
                        />))
                    }
                </SimpleGrid>

                <Box
                    mt={12}
                    p={6}
                    bg="blue.50"
                    rounded="xl"
                    width="100%"
                    maxW="3xl"
                    borderWidth="1px"
                    borderColor="blue.200"
                >
                    <VStack align="start" spacing={4}>
                        <HStack>
                            <Icon as={FaRegLightbulb} color="blue.500" w={5} h={5} />
                            <Heading size="sm" color="blue.700">
                                {t('funblocks_ai_insights_unique')}
                            </Heading>
                        </HStack>
                        <List spacing={3}>
                            {contentFeatures.map((feature, index) => (
                                <ListItem key={index} display="flex" alignItems="center">
                                    <ListIcon as={FaCheckCircle} color="green.500" />
                                    <Text fontSize="md">{feature}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </Box>
            </Section>

            {/* Examples showcase */}
            <Section bg="white" id="examples">
                <Heading
                    size="lg"
                    mb={8}
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.400)"
                    bgClip="text"
                >
                    {t('graphics_examples_title')}
                </Heading>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    {examples.map((example, index) => (
                        <ExampleCard
                            key={index}
                            title={example.title}
                            description={example.description}
                            imageSrc={example.imageSrc}
                        />
                    ))}
                </SimpleGrid>

                <Box textAlign="center" mt={10}>
                    <Text fontSize="lg" fontWeight="medium" mb={4}>
                        {t('examples_subtitle')}
                    </Text>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('examples_button')}
                    </Button>
                </Box>
            </Section>

            {/* Features section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('graphics_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Comparison section */}
            <Section bg="white" title={t('comparison_title')}>
                <Box maxW="4xl" width="100%" borderWidth="1px" borderRadius="lg" overflow="hidden" bg="white">
                    <Grid templateColumns="repeat(3, 1fr)" bg="gray.50" p={4}>
                        <GridItem>
                            <Text fontWeight="bold">{t('comparison_header_feature')}</Text>
                        </GridItem>
                        <GridItem>
                            <Text fontWeight="bold">{t('comparison_header_traditional')}</Text>
                        </GridItem>
                        <GridItem>
                            <Text fontWeight="bold" color="blue.500">{t('comparison_header_ai')}</Text>
                        </GridItem>
                    </Grid>

                    <Box p={4}>
                        {comparisonData.map((item, index) => (
                            <ComparisonFeature
                                key={index}
                                feature={item.feature}
                                traditional={item.traditional}
                                aiPowered={item.aiPowered}
                                withSeperator={index !== comparisonData.length - 1}
                            />
                        ))}
                    </Box>
                </Box>
            </Section>

            {/* Benefits section */}
            <Section bg="gray.50" title={t('graphics_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('graphics_benefits_pro_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`graphics_benefit_pro_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('graphics_benefits_business_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`graphics_benefit_business_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Use cases section */}
            <Section bg="white">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('graphics_use_cases_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('graphics_use_cases_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('graphics_scenarios_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`graphics_scenario_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`graphics_scenario_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('graphics_users_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`graphics_user_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`graphics_user_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Adding Fun, Novel, Thought-Provoking Social Tool section */}
            <Section bg="purple.50">
                <Heading as="h3" size="lg" mb={6} textAlign="center" color="blue.600">
                    {t('fun_social_tool')}
                </Heading>
                <Text fontSize="lg" mb={8} textAlign="center">
                    {t('funblocks_ai_insights_thought_stimulator')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {funFeatures.map((feature, index) => (
                        <Box key={index} bg="white" p={5} borderRadius="md" boxShadow="md">
                            <Flex align="center" mb={3}>
                                <Icon as={feature.icon} w={6} h={6} color="purple.400" mr={3} />
                                <Heading as="h4" size="md">{feature.title}</Heading>
                            </Flex>
                            <Text>{feature.description}</Text>
                        </Box>
                    ))}
                </SimpleGrid>
            </Section>

            {/* <GraphicsCaseStudies /> */}
            <CaseStudies
                appname={appname}
                description={t('case_studies_description')}
                caseStudies={[
                    {
                        icon: FaChartLine,
                        title: t('case_study_1_title', 'Data Visualization for Executive Reports'),
                        industry: t('case_study_1_industry', 'Business Intelligence'),
                        challenge: t('case_study_1_challenge', 'A Fortune 500 company needed to transform complex quarterly performance data into easily digestible visuals for executive meetings, but their design team was overwhelmed with requests.'),
                        solution: t('case_study_1_solution', 'Using our AI Graphics Generator, they created professional data charts and infographics in minutes, highlighting key metrics and trends with minimal effort.'),
                        // results: t('case_study_1_results', 'Meeting preparation time reduced by 60%, executive comprehension improved by 45%, and design team freed up for strategic projects.'),
                        // imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_business.png"
                    },
                    {
                        icon: FaGraduationCap,
                        title: t('case_study_2_title', 'Educational Concept Visualization'),
                        industry: t('case_study_2_industry', 'Education'),
                        challenge: t('case_study_2_challenge', 'A university professor struggled to explain complex scientific processes to undergraduate students, finding traditional textbook explanations insufficient for visual learners.'),
                        solution: t('case_study_2_solution', 'Created a series of process-based infographics using our AI tool that visually broke down complex concepts into clear, sequential steps with visual cues.'),
                        // results: t('case_study_2_results', 'Student comprehension increased by 32%, test scores improved by 27%, and course satisfaction ratings rose from 3.2/5 to 4.7/5.'),
                        // imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_education.png"
                    },
                    {
                        icon: FaProjectDiagram,
                        title: t('case_study_3_title', 'Software Architecture Documentation'),
                        industry: t('case_study_3_industry', 'Software Development'),
                        challenge: t('case_study_3_challenge', 'A tech startup needed to document their complex microservices architecture for new developers but lacked dedicated technical writers or designers.'),
                        solution: t('case_study_3_solution', 'Used the AI Flowchart Generator to create comprehensive system diagrams showing service relationships, data flows, and integration points with minimal technical input.'),
                        // results: t('case_study_3_results', 'Onboarding time for new developers reduced by 40%, documentation maintenance simplified, and system understanding improved across non-technical teams.'),
                        // imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_flowchart.png"
                    },
                    {
                        icon: FaBullhorn,
                        title: t('case_study_4_title', 'Social Media Content Strategy'),
                        industry: t('case_study_4_industry', 'Digital Marketing'),
                        challenge: t('case_study_4_challenge', 'A digital marketing agency needed to create engaging, data-rich visuals for social media campaigns but was constrained by limited design resources and tight deadlines.'),
                        solution: t('case_study_4_solution', 'Implemented our AI Graphics Generator to rapidly produce customized infographics and data visualizations tailored to each client\'s brand and campaign goals.'),
                        // results: t('case_study_4_results', 'Content production time decreased by 75%, engagement rates increased by 38% compared to text-only posts, and client satisfaction scores improved significantly.'),
                        // imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_social.png"
                    }
                ]} />
            {/* Comparison Table Section */}
            <ComparisonTable
                title={t("graphics_comparison_title")}
                description={t("graphics_comparison_description")}
                bg="blue.50"
                productName={appname}
                columns={[
                    { key: 'funblocks', label: t('comparison_funblocks', 'FunBlocks AI Graphics'), highlight: true },
                    { key: 'traditional', label: t('comparison_traditional', 'Traditional Design Tools') },
                    { key: 'other_ai', label: t('comparison_other_ai', 'Other AI Tools') },
                    { key: 'templates', label: t('comparison_templates', 'Template Services') }
                ]}
                features={[
                    {
                        name: t('comparison_feature_1', 'No Design Skills Required'),
                        tooltip: t('comparison_feature_1_tooltip', 'Can create professional graphics without any design experience'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_2', 'One-Click Generation'),
                        tooltip: t('comparison_feature_2_tooltip', 'Creates complete graphics with a single text prompt'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_3', 'Customized to Content'),
                        tooltip: t('comparison_feature_3_tooltip', 'Automatically adapts design to match the specific content'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_4', 'Infographic Generation'),
                        tooltip: t('comparison_feature_4_tooltip', 'Creates data-rich visual stories from text descriptions'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_5', 'Flowchart Creation'),
                        tooltip: t('comparison_feature_5_tooltip', 'Generates process flows and diagrams from descriptions'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_6', 'Data Visualization'),
                        tooltip: t('comparison_feature_6_tooltip', 'Creates charts and graphs from data or descriptions'),
                        funblocks: true,
                        traditional: true,
                        other_ai: true,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_7', 'SVG Code Generation'),
                        tooltip: t('comparison_feature_7_tooltip', 'Produces editable SVG code for further customization'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_8', 'Learning Curve'),
                        tooltip: t('comparison_feature_8_tooltip', 'Easy to learn and use immediately'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_9', 'Speed of Creation'),
                        tooltip: t('comparison_feature_9_tooltip', 'Creates graphics in minutes rather than hours'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_10', 'Design Principles Applied'),
                        tooltip: t('comparison_feature_10_tooltip', 'Automatically applies professional design principles'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: true
                    },
                ]}
            />

            {/* Research Backed Section */}
            <ResearchBacked
                namespace="layered"
                title={t("research_title")}
                description={t("research_description")}
                bg="gray.50"
                productName={appname}
                buttons={[
                    {
                        label: t('research_button_science', 'Evidence-Based Design'),
                        icon: FaBrain,
                        colorScheme: 'blue'
                    },
                    {
                        label: t('research_button_data', 'Data Visualization Research'),
                        icon: FaChartLine,
                        colorScheme: 'green'
                    }
                ]}
                researchAreas={[
                    {
                        title: t('research_area_1_title', 'Visual Information Processing'),
                        description: t('research_area_1_description', 'Our AI Graphics Generator leverages research on how humans process visual information more efficiently than text. Studies show that visuals are processed 60,000 times faster than text and improve comprehension by up to 400%.'),
                        icon: FaEye,
                        color: 'blue',
                        citations: [
                            {
                                text: 'Mayer, R. E. (2020). Multimedia learning. Cambridge University Press.',
                                url: 'https://www.cambridge.org/highereducation/books/multimedia-learning/FB7E79A165D24D47CEACEB4D2C426ECD#overview'
                            },
                            {
                                text: 'Mayer, R. E., & Moreno, R. (2003). Nine ways to reduce cognitive load in multimedia learning. Educational Psychologist, 38(1), 43-52.',
                                url: 'https://www.uky.edu/~gmswan3/544/9_ways_to_reduce_CL.pdf'
                            }
                        ]
                    },
                    {
                        title: t('research_area_2_title', 'Data Visualization Principles'),
                        description: t('research_area_2_description', 'Our AI implements established data visualization principles from leading researchers like Edward Tufte and Stephen Few, ensuring that complex data is presented with clarity, precision, and minimal cognitive load.'),
                        icon: FaChartBar,
                        color: 'purple',
                        citations: [
                            {
                                text: 'Tufte, E. R. (2001). The Visual Display of Quantitative Information (2nd ed.). Graphics Press.',
                                url: 'https://www.edwardtufte.com/tufte/books_vdqi'
                            },
                            {
                                text: 'Cairo, A. (2012). The Functional Art: An introduction to information graphics and visualization. New Riders.',
                                url: 'https://www.amazon.com/Functional-Art-introduction-information-visualization/dp/0321834739'
                            }
                        ]
                    },
                    {
                        title: t('research_area_3_title', 'Cognitive Load Theory'),
                        description: t('research_area_3_description', 'Our AI Graphics Generator applies cognitive load theory to create visuals that reduce extraneous cognitive load, allowing users to focus on understanding the content rather than deciphering complex presentations.'),
                        icon: FaBrain,
                        color: 'green',
                        citations: [
                            {
                                text: 'Sweller, J. (2011). Cognitive load theory. Psychology of Learning and Motivation, 55, 37-76.',
                                url: 'https://www.researchgate.net/publication/279149314_Cognitive_Load_Theory'
                            },
                            {
                                text: 'Mayer, R. E., & Moreno, R. (2003). Nine ways to reduce cognitive load in multimedia learning. Educational Psychologist, 38(1), 43-52.',
                                url: 'https://www.uky.edu/~gmswan3/544/9_ways_to_reduce_CL.pdf'
                            }
                        ]
                    },
                    {
                        title: t('research_area_4_title', 'Information Design'),
                        description: t('research_area_4_description', 'Our system incorporates principles of information design and visual hierarchy to ensure that the most important information stands out and complex relationships are clearly communicated.'),
                        icon: FaProjectDiagram,
                        color: 'orange',
                        citations: [
                            {
                                text: 'Cairo, A. (2012). The Functional Art: An introduction to information graphics and visualization. New Riders.',
                                url: 'https://www.amazon.com/Functional-Art-introduction-information-visualization/dp/0321834739'
                            },
                            {
                                text: 'Krum, R. (2013). Cool Infographics: Effective Communication with Data Visualization and Design. Wiley.',
                                url: 'https://coolinfographics.com/book'
                            }
                        ]
                    }
                ]}
            />
            {/* <GraphicsTestimonials /> */}
            <Testimonials
                testimonials={[
                    {
                        content: t('testimonial_1_content', 'As a marketing manager with no design background, I was struggling to create professional infographics for our campaigns. The AI Graphics Generator changed everything! Now I can create stunning visuals in minutes that would have taken our design team days. Our engagement rates have increased by 45% since we started using these graphics.'),
                        author: 'Sarah Chen',
                        role: t('testimonial_1_role', 'Marketing Manager'),
                        organization: 'TechCorp Inc.',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_2_content', 'The flowchart generator is a game-changer for our development team. We used to spend hours creating system architecture diagrams, but now we can generate them in minutes. The AI somehow understands exactly what we need from just a text description. It\'s like having a professional designer on call 24/7.'),
                        author: 'David Martinez',
                        role: t('testimonial_2_role', 'Software Architect'),
                        organization: 'Innovate Solutions',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_3_content', 'As a data analyst, I need to present complex findings to non-technical stakeholders regularly. The AI Graphics Generator helps me transform boring spreadsheets into compelling data visualizations that tell a story. My presentations now generate much more engagement and understanding from executives.'),
                        author: 'Michael Rodriguez',
                        role: t('testimonial_3_role', 'Data Analyst'),
                        organization: 'Global Finance Group',
                        rating: 4.5,
                        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_4_content', 'I use the AI Graphics Generator for my classroom materials, and my students love it! The visuals help explain complex concepts in a way that text alone never could. The best part is how quickly I can create custom graphics for each lesson - what used to take hours now takes minutes.'),
                        author: 'Emily Johnson',
                        role: t('testimonial_4_role', 'High School Teacher'),
                        organization: 'Westlake High School',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_5_content', 'The SVG code generator is brilliant for our web development team. We can quickly create custom graphics that are lightweight and responsive. The code is clean and well-structured, making it easy to integrate into our projects. This tool has significantly reduced our dependency on stock graphics.'),
                        author: 'Jennifer Park',
                        role: t('testimonial_5_role', 'Front-end Developer'),
                        organization: 'WebTech Solutions',
                        rating: 4.5,
                        image: 'https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_6_content', 'Our research team uses the AI Graphics Generator to create visualizations for scientific publications. The quality and accuracy of the graphics are impressive, and they save us countless hours that we can now dedicate to actual research instead of creating figures. A must-have tool for any research group.'),
                        author: 'Dr. James Wilson',
                        role: t('testimonial_6_role', 'Research Director'),
                        organization: 'National Research Institute',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    }
                ]} />

            <HowToUse namespace="infographic" />

            {/* Replacing FAQ section to include items from ProductIntro */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('graphics_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {/* Existing FAQ items */}
                    {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                        <AccordionItem key={`existing-${i}`}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`graphics_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`graphics_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}

                    {/* Additional FAQ items from ProductIntro */}
                    {additionalFaqItems.map((item, index) => (
                        <AccordionItem key={`additional-${index}`}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{item.question}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{item.answer}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>

            {/* CTA section */}
            <Box
                w="100%"
                bgGradient="linear(to-r, blue.500, purple.500)"
                py={16}
                px={4}
            >
                <VStack spacing={6} maxW="3xl" mx="auto" textAlign="center">
                    <Heading color="white" size="xl">
                        {t('cta_title')}
                    </Heading>
                    <Text color="whiteAlpha.900" fontSize="lg">
                        {t('cta_description')}
                    </Text>
                    <Button
                        size="lg"
                        colorScheme="whiteAlpha"
                        rightIcon={<FaRocket />}
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('cta_button')}
                    </Button>
                </VStack>
            </Box>
        </VStack>
    )
}

export default GraphicsIntro