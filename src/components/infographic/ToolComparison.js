import { Box, Heading, Text, SimpleGrid, Icon, VStack, useBreakpointValue, Table, Thead, Tbody, Tr, Th, Td, Badge, Flex, Button } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaCheck, FaTimes, FaRocket, FaArrowRight } from 'react-icons/fa'
import Section from '../common/Section'

const FeatureRow = ({ feature, traditional, ai, highlight }) => {
    return (
        <Tr bg={highlight ? "blue.50" : "white"}>
            <Td fontWeight={highlight ? "bold" : "normal"}>{feature}</Td>
            <Td textAlign="center">
                {traditional === true ? (
                    <Icon as={FaCheck} color="green.500" />
                ) : traditional === false ? (
                    <Icon as={FaTimes} color="red.500" />
                ) : (
                    traditional
                )}
            </Td>
            <Td textAlign="center" bg={highlight ? "blue.100" : "blue.50"}>
                {ai === true ? (
                    <Icon as={FaCheck} color="green.500" />
                ) : ai === false ? (
                    <Icon as={FaTimes} color="red.500" />
                ) : (
                    <Text fontWeight="bold" color="blue.600">{ai}</Text>
                )}
            </Td>
        </Tr>
    )
}

const ToolComparison = () => {
    const { t } = useTranslation('infographic')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const comparisonData = [
        {
            feature: t('comparison_feature_1', 'Design Expertise Required'),
            traditional: t('comparison_traditional_1', 'High'),
            ai: t('comparison_ai_1', 'None'),
            highlight: true
        },
        {
            feature: t('comparison_feature_2', 'Creation Time'),
            traditional: t('comparison_traditional_2', '1-3 hours'),
            ai: t('comparison_ai_2', 'Seconds'),
            highlight: true
        },
        {
            feature: t('comparison_feature_3', 'Learning Curve'),
            traditional: t('comparison_traditional_3', 'Steep'),
            ai: t('comparison_ai_3', 'Minimal'),
            highlight: false
        },
        {
            feature: t('comparison_feature_4', 'Content Analysis'),
            traditional: t('comparison_traditional_4', 'Manual'),
            ai: t('comparison_ai_4', 'Automatic'),
            highlight: false
        },
        {
            feature: t('comparison_feature_5', 'Design Principles Application'),
            traditional: t('comparison_traditional_5', 'Manual'),
            ai: t('comparison_ai_5', 'Automatic'),
            highlight: false
        },
        {
            feature: t('comparison_feature_6', 'Visualization Type Selection'),
            traditional: t('comparison_traditional_6', 'Manual'),
            ai: t('comparison_ai_6', 'AI-Optimized'),
            highlight: false
        },
        {
            feature: t('comparison_feature_7', 'Cost'),
            traditional: t('comparison_traditional_7', '$$$'),
            ai: t('comparison_ai_7', '$'),
            highlight: true
        },
        {
            feature: t('comparison_feature_8', 'Consistency'),
            traditional: t('comparison_traditional_8', 'Variable'),
            ai: t('comparison_ai_8', 'High'),
            highlight: false
        },
        {
            feature: t('comparison_feature_9', 'Iteration Speed'),
            traditional: t('comparison_traditional_9', 'Slow'),
            ai: t('comparison_ai_9', 'Instant'),
            highlight: false
        },
        {
            feature: t('comparison_feature_10', 'Software Required'),
            traditional: t('comparison_traditional_10', 'Multiple Programs'),
            ai: t('comparison_ai_10', 'Web Browser Only'),
            highlight: false
        }
    ];

    return (
        <Section bg="white" id="tool-comparison">
            <VStack spacing={8} width="100%">
                <Badge colorScheme="blue" fontSize="md" px={3} py={1} borderRadius="full">
                    {t('comparison_badge', 'Why Choose AI')}
                </Badge>
                
                <Heading
                    size="lg"
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.400)"
                    bgClip="text"
                >
                    {t('comparison_heading', 'AI Infographics vs. Traditional Methods')}
                </Heading>
                
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={4}
                >
                    {t('comparison_description', 'See how our AI-powered approach compares to traditional infographic creation methods in terms of efficiency, expertise required, and results.')}
                </Text>

                <Box width="100%" overflowX="auto">
                    <Table variant="simple" width="100%">
                        <Thead bg="gray.100">
                            <Tr>
                                <Th width="40%">{t('comparison_header_feature', 'Feature')}</Th>
                                <Th width="30%" textAlign="center">{t('comparison_header_traditional', 'Traditional Methods')}</Th>
                                <Th width="30%" textAlign="center" bg="blue.100">{t('comparison_header_ai', 'AI Infographic Generator')}</Th>
                            </Tr>
                        </Thead>
                        <Tbody>
                            {comparisonData.map((item, index) => (
                                <FeatureRow
                                    key={index}
                                    feature={item.feature}
                                    traditional={item.traditional}
                                    ai={item.ai}
                                    highlight={item.highlight}
                                />
                            ))}
                        </Tbody>
                    </Table>
                </Box>
                
                <Box 
                    p={6} 
                    bg="blue.50" 
                    borderRadius="lg" 
                    borderWidth="1px" 
                    borderColor="blue.200"
                    width="100%"
                    maxW="3xl"
                    mt={6}
                >
                    <VStack spacing={4}>
                        <Heading size="md" color="blue.700">
                            {t('comparison_conclusion_title', 'The Bottom Line')}
                        </Heading>
                        <Text textAlign="center">
                            {t('comparison_conclusion_text', 'Our AI Infographic Generator saves you time, money, and the frustration of learning complex design software, while still producing professional-quality results that effectively communicate your information.')}
                        </Text>
                        <Button
                            colorScheme="blue"
                            rightIcon={<FaArrowRight />}
                            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        >
                            {t('comparison_cta_button', 'Experience the Difference')}
                        </Button>
                    </VStack>
                </Box>
            </VStack>
        </Section>
    )
}

export default ToolComparison
