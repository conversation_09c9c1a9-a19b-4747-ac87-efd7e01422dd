import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon, Button, Image, Flex, Badge, HStack, Divider, Grid, GridItem, Link } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaChartBar, FaMagic, FaLightbulb, FaClock, FaPalette, FaChartLine, FaSitemap, FaRegObjectGroup, FaDownload, FaShareAlt, FaArrowRight, FaCheck, FaRocket, FaBuffer, FaUserTie, FaSchool, FaNewspaper, FaFileInvoice, FaStar, FaRegLightbulb, FaGraduationCap, FaBullhorn, FaFlask } from 'react-icons/fa'
import { MdCheckCircle, MdAutoAwesome, MdTimeline, MdCategory, MdInsights, MdBarChart, MdCompareArrows, MdAccessTime, MdOutlineDesignServices } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'
import InfographicTypes from './InfographicTypes'
import DesignPrinciples from './DesignPrinciples'
import CaseStudies from '../common/CaseStudies'
import ToolComparison from './ToolComparison'
import Testimonials from '../common/Testimonials'

const appname = 'AI Infographic Generator'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={5}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={3}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

// 添加示例展示卡片组件
const ExampleCard = ({ title, description, imageSrc }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })

    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            overflow="hidden"
            h="100%"
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
        >
            <Image
                src={imageSrc}
                alt={title}
                height={isMobile ? "240px" : "320px"}
                width="100%"
                objectFit="contain"
            />
            <Box p={5}>
                <Heading size="md" mb={2}>{title}</Heading>
                <Text color="gray.600">{description}</Text>
            </Box>
        </Box>
    )
}

// 添加比较功能组件
const ComparisonFeature = ({ feature, traditional, aiPowered, withSeperator = true }) => {
    return (
        <Box borderBottomWidth={withSeperator ? "1px" : "0px"} py={3}>
            <Grid templateColumns="repeat(3, 1fr)">
                <GridItem>
                    <Text fontWeight="medium">{feature}</Text>
                </GridItem>
                <GridItem>
                    <Flex align="center">
                        <Text color="gray.500">{traditional}</Text>
                    </Flex>
                </GridItem>
                <GridItem>
                    <Flex align="center">
                        <Text color="blue.500" fontWeight="bold">{aiPowered}</Text>
                    </Flex>
                </GridItem>
            </Grid>
        </Box>
    )
}

const InfographicIntro = () => {
    const { t } = useTranslation('infographic')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaMagic,
            title: t('infographic_feature_1_title'),
            text: t('infographic_feature_1_text')
        },
        {
            icon: MdAutoAwesome,
            title: t('infographic_feature_2_title'),
            text: t('infographic_feature_2_text')
        },
        {
            icon: FaPalette,
            title: t('infographic_feature_3_title'),
            text: t('infographic_feature_3_text')
        },
        {
            icon: FaRegObjectGroup,
            title: t('infographic_feature_4_title'),
            text: t('infographic_feature_4_text')
        }
    ]

    const visualTypes = [
        {
            icon: FaChartBar,
            title: t('infographic_visual_1_title'),
            text: t('infographic_visual_1_text')
        },
        {
            icon: FaSitemap,
            title: t('infographic_visual_2_title'),
            text: t('infographic_visual_2_text')
        },
        {
            icon: MdTimeline,
            title: t('infographic_visual_3_title'),
            text: t('infographic_visual_3_text')
        }
    ]

    // 示例数据
    const examples = [
        {
            title: t('infographic_example_1_title'),
            description: t('infographic_example_1_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_piechart.png"
        },
        {
            title: t('infographic_example_2_title'),
            description: t('infographic_example_2_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_process.png"
        },
        {
            title: t('infographic_example_3_title'),
            description: t('infographic_example_3_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_timeline.png"
        },
        {
            title: t('infographic_example_4_title'),
            description: t('infographic_example_4_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_comparison.png"
        }
    ]

    // 比较数据
    const comparisonData = [
        {
            feature: t('comparison_time'),
            traditional: t('comparison_time_traditional'),
            aiPowered: t('comparison_time_ai')
        },
        {
            feature: t('comparison_skills'),
            traditional: t('comparison_skills_traditional'),
            aiPowered: t('comparison_skills_ai')
        },
        {
            feature: t('comparison_analysis'),
            traditional: t('comparison_analysis_traditional'),
            aiPowered: t('comparison_analysis_ai')
        },
        {
            feature: t('comparison_design'),
            traditional: t('comparison_design_traditional'),
            aiPowered: t('comparison_design_ai')
        },
        {
            feature: t('comparison_iteration'),
            traditional: t('comparison_iteration_traditional'),
            aiPowered: t('comparison_iteration_ai')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={isMobile ? 6 : 16}>
            {/* Hero 部分 */}
            <Box
                w="100%"
                bgGradient="linear(to-br, blue.50, purple.50)"
                py={isMobile ? 12 : 16}
                px={4}
                borderBottomWidth="1px"
                borderBottomColor="gray.200"
            >
                <VStack spacing={8} maxW="4xl" mx="auto" textAlign="center">
                    <Badge colorScheme="blue" fontSize={isMobile ? "sm" : "md"} px={3} py={1} borderRadius="full">
                        {t('hero_badge')}
                    </Badge>

                    <Heading
                        fontSize={isMobile ? "3xl" : "5xl"}
                        fontWeight="extrabold"
                        bgGradient="linear(to-r, blue.400, purple.500)"
                        bgClip="text"
                        lineHeight="1.2"
                    >
                        {t('hero_heading')}
                    </Heading>

                    <Text fontSize={isMobile ? "lg" : "xl"} color="gray.600" maxW="2xl">
                        {t('hero_description')}
                    </Text>

                    <HStack spacing={4}>
                        <Button
                            size="lg"
                            colorScheme="blue"
                            rightIcon={<FaArrowRight />}
                            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        >
                            {t('hero_start_button')}
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            colorScheme="purple"
                            onClick={() => document.querySelector('#examples').scrollIntoView({ behavior: 'smooth' })}
                        >
                            {t('hero_examples_button')}
                        </Button>
                    </HStack>

                    {/* 转化指标 */}
                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={isMobile ? 4 : 8} width="100%" pt={8}>
                        <VStack>
                            <Heading size="xl" color="blue.500">{t('hero_metric_1_number')}</Heading>
                            <Text color="gray.600">{t('hero_metric_1_text')}</Text>
                        </VStack>
                        <VStack>
                            <Heading size="xl" color="purple.500">{t('hero_metric_2_number')}</Heading>
                            <Text color="gray.600">{t('hero_metric_2_text')}</Text>
                        </VStack>
                        <VStack>
                            <Heading size="xl" color="green.500">{t('hero_metric_3_number')}</Heading>
                            <Text color="gray.600">{t('hero_metric_3_text')}</Text>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Box>

            {/* <Section bg="gray.50" title={t('infographic_visual_types_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {visualTypes.map((type, index) => (
                        <Feature
                            key={index}
                            icon={type.icon}
                            title={type.title}
                            text={type.text}
                        />
                    ))}
                </SimpleGrid>
            </Section> */}

            {/* 案例展示区 */}
            <Section bg="white" id="examples">
                <Heading
                    size="lg"
                    mb={8}
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.400)"
                    bgClip="text"
                >
                    {t('infographic_examples_title')}
                </Heading>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    {examples.map((example, index) => (
                        <ExampleCard
                            key={index}
                            title={example.title}
                            description={example.description}
                            imageSrc={example.imageSrc}
                        />
                    ))}
                </SimpleGrid>

                <Box textAlign="center" mt={10}>
                    <Text fontSize="lg" fontWeight="medium" mb={4}>
                        {t('examples_subtitle')}
                    </Text>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('examples_button')}
                    </Button>
                </Box>
            </Section>

            <Section bg="blue.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('infographic_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* 信息图表类型 */}
            <InfographicTypes />

            {/* 设计原则 */}
            <DesignPrinciples />

            {/* 工具比较 */}
            <ToolComparison />

            <Section bg="gray.50" title={t('infographic_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('infographic_benefits_pro_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`infographic_benefit_pro_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('infographic_benefits_business_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`infographic_benefit_business_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <Section bg="white">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('infographic_use_cases_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('infographic_use_cases_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('infographic_scenarios_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`infographic_scenario_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`infographic_scenario_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('infographic_users_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`infographic_user_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`infographic_user_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* 案例研究 */}
            <CaseStudies
                appname={appname}
                description={t('case_studies_description')}
                caseStudies={[
                    {
                        icon: FaChartLine,
                        title: t('case_study_1_title', 'Quarterly Sales Report Transformation'),
                        industry: t('case_study_1_industry', 'Business & Finance'),
                        challenge: t('case_study_1_challenge', 'A financial services company struggled to communicate complex quarterly sales data effectively to stakeholders, resulting in low engagement and poor information retention.'),
                        solution: t('case_study_1_solution', 'Used the AI Infographic Generator to transform dense sales statistics into a visually compelling infographic highlighting key metrics, trends, and regional comparisons.'),
                        // results: t('case_study_1_results', 'Presentation engagement increased by 64%, meeting time reduced by 30%, and stakeholder comprehension of key metrics improved significantly.'),
                        // imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_business.png"
                    },
                    {
                        icon: FaGraduationCap,
                        title: t('case_study_2_title', 'Educational Concept Visualization'),
                        industry: t('case_study_2_industry', 'Education'),
                        challenge: t('case_study_2_challenge', 'A university professor needed to explain complex scientific processes to undergraduate students but found traditional textbook explanations insufficient for student comprehension.'),
                        solution: t('case_study_2_solution', 'Created a series of process-based infographics that visually broke down complex concepts into clear, sequential steps with visual cues and simplified explanations.'),
                        // results: t('case_study_2_results', 'Student test scores improved by 27%, concept retention increased, and student satisfaction ratings for the course improved from 3.2/5 to 4.7/5.'),
                        // imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_education.png"
                    },
                    {
                        icon: FaBullhorn,
                        title: t('case_study_3_title', 'Marketing Campaign Performance'),
                        industry: t('case_study_3_industry', 'Marketing'),
                        challenge: t('case_study_3_challenge', 'A digital marketing agency needed to present campaign results to clients in a more engaging way than traditional spreadsheets and reports.'),
                        solution: t('case_study_3_solution', 'Developed comparative infographics showing before/after metrics, channel performance, and ROI calculations with visual hierarchy highlighting the most impressive results.'),
                        // results: t('case_study_3_results', 'Client retention improved by 35%, approval for campaign renewals increased by 42%, and the agency was able to secure higher budgets for future campaigns.'),
                        // imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_marketing.png"
                    },
                    {
                        icon: FaFlask,
                        title: t('case_study_4_title', 'Research Findings Visualization'),
                        industry: t('case_study_4_industry', 'Research & Development'),
                        challenge: t('case_study_4_challenge', 'A research team struggled to communicate their complex findings to non-technical stakeholders and potential funding sources.'),
                        solution: t('case_study_4_solution', 'Created data-driven infographics that translated technical research findings into visual stories highlighting key discoveries, methodologies, and potential applications.'),
                        // results: t('case_study_4_results', 'Secured 40% more funding than previous years, increased media coverage of research, and improved collaboration with industry partners who better understood the research value.'),
                        // imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_research.png"
                    }
                ]} />

            <Testimonials
                appname={appname}
                users={'10,000'}
                rating={4.8}
                testimonials={[
                    {
                        content: t('testimonial_1_content', 'MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I\'ve cut my study time by 30% while improving my grades!'),
                        author: 'Emily Johnson',
                        role: t('testimonial_1_role', 'Medical Student'),
                        organization: 'Stanford University',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_2_content', 'As a high school physics teacher, I\'ve been using MindLadder to create learning materials for my students. The 8-tier system is brilliant for gradually building understanding of difficult concepts like quantum mechanics. My students\' test scores have improved by 27% since implementing these knowledge ladders.'),
                        author: 'David Martinez',
                        role: t('testimonial_2_role', 'Physics Teacher'),
                        organization: 'Westlake High School',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_3_content', 'Our engineering team uses MindLadder to onboard new hires to our complex software architecture. The visual nature combined with progressive complexity layers has reduced our onboarding time by 40%. It\'s now our standard tool for knowledge transfer across departments.'),
                        author: 'Sarah Chen',
                        role: t('testimonial_3_role', 'Engineering Director'),
                        organization: 'Tesla',
                        rating: 4.5,
                        image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_4_content', 'As someone with ADHD, traditional learning resources often overwhelm me. MindLadder\'s visual approach and ability to explore concepts at my own pace has been a game-changer. For the first time, I can see how different ideas connect and build on each other.'),
                        author: 'Michael Rodriguez',
                        role: t('testimonial_4_role', 'Software Developer'),
                        organization: 'Freelance',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_5_content', 'I\'ve been using MindLadder to prepare for my MBA courses. The way it connects business concepts through multiple layers of understanding has given me an edge in class discussions. I especially appreciate how it highlights counterintuitive aspects that challenge my assumptions.'),
                        author: 'Jennifer Park',
                        role: t('testimonial_5_role', 'MBA Student'),
                        organization: 'Harvard Business School',
                        rating: 4.5,
                        image: 'https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_6_content', 'Our research team uses MindLadder to map interdisciplinary connections between AI ethics and policy. The ability to start with simple analogies and build to expert-level insights has helped us communicate complex ideas to stakeholders from diverse backgrounds.'),
                        author: 'Dr. James Wilson',
                        role: t('testimonial_6_role', 'Research Director'),
                        organization: 'MIT Media Lab',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    }
                ]} />

            <HowToUse namespace="infographic" />

            {/* 常见问题 */}
            <Section bg="white">
                <VStack spacing={6} width="100%">
                    <Badge colorScheme="purple" fontSize="md" px={3} py={1} borderRadius="full">
                        {t('faq_badge', 'Knowledge Base')}
                    </Badge>

                    <Heading
                        size="lg"
                        mb={isMobile ? 4 : 8}
                        textAlign="center"
                        bgGradient="linear(to-r, purple.400, blue.400)"
                        bgClip="text"
                    >
                        {t('infographic_faq_title')}
                    </Heading>

                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('faq_description', 'Find answers to commonly asked questions about our AI Infographic Generator and how to get the most out of it.')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%" mb={8}>
                        <Box>
                            <Heading size="md" mb={4} color="blue.600">
                                {t('faq_category_1', 'General Questions')}
                            </Heading>
                            <Accordion allowMultiple width="100%">
                                {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                                    <AccordionItem key={i}>
                                        <AccordionButton>
                                            <Box flex="1" textAlign="left">
                                                <Text fontWeight="medium">{t(`infographic_faq_${i}_q`)}</Text>
                                            </Box>
                                            <AccordionIcon />
                                        </AccordionButton>
                                        <AccordionPanel pb={4}>
                                            <Text color="gray.600">{t(`infographic_faq_${i}_a`)}</Text>
                                        </AccordionPanel>
                                    </AccordionItem>
                                ))}
                            </Accordion>
                        </Box>

                        <Box>
                            <Heading size="md" mb={4} color="blue.600">
                                {t('faq_category_2', 'Technical Questions')}
                            </Heading>
                            <Accordion allowMultiple width="100%">
                                <AccordionItem>
                                    <AccordionButton>
                                        <Box flex="1" textAlign="left">
                                            <Text fontWeight="medium">{t('infographic_faq_tech_1_q', 'What file formats are supported?')}</Text>
                                        </Box>
                                        <AccordionIcon />
                                    </AccordionButton>
                                    <AccordionPanel pb={4}>
                                        <Text color="gray.600">{t('infographic_faq_tech_1_a', 'Our AI generates infographics in SVG format, which can be exported to PNG, JPG, or PDF as needed. SVG is a vector format that allows for scaling without quality loss and easy editing in vector graphics software.')}</Text>
                                    </AccordionPanel>
                                </AccordionItem>

                                <AccordionItem>
                                    <AccordionButton>
                                        <Box flex="1" textAlign="left">
                                            <Text fontWeight="medium">{t('infographic_faq_tech_2_q', 'Can I customize the generated infographics?')}</Text>
                                        </Box>
                                        <AccordionIcon />
                                    </AccordionButton>
                                    <AccordionPanel pb={4}>
                                        <Text color="gray.600">{t('infographic_faq_tech_2_a', 'Yes, all infographics are generated in SVG format, which can be easily edited using vector graphics software like Adobe Illustrator, Inkscape (free), or Figma. You can change colors, fonts, sizes, and any other element.')}</Text>
                                    </AccordionPanel>
                                </AccordionItem>

                                <AccordionItem>
                                    <AccordionButton>
                                        <Box flex="1" textAlign="left">
                                            <Text fontWeight="medium">{t('infographic_faq_tech_3_q', 'What are the resolution and size limitations?')}</Text>
                                        </Box>
                                        <AccordionIcon />
                                    </AccordionButton>
                                    <AccordionPanel pb={4}>
                                        <Text color="gray.600">{t('infographic_faq_tech_3_a', 'Since our infographics are generated as SVG files, they are resolution-independent and can be scaled to any size without quality loss. The default dimensions are optimized for standard presentation and social media sharing.')}</Text>
                                    </AccordionPanel>
                                </AccordionItem>

                                <AccordionItem>
                                    <AccordionButton>
                                        <Box flex="1" textAlign="left">
                                            <Text fontWeight="medium">{t('infographic_faq_tech_4_q', 'Can I use the infographics for commercial purposes?')}</Text>
                                        </Box>
                                        <AccordionIcon />
                                    </AccordionButton>
                                    <AccordionPanel pb={4}>
                                        <Text color="gray.600">{t('infographic_faq_tech_4_a', 'Yes, infographics created with our tool can be used for both personal and commercial purposes. You retain full ownership of the content you create. For high-volume commercial use, please check our pricing plans for the most suitable option.')}</Text>
                                    </AccordionPanel>
                                </AccordionItem>

                                <AccordionItem>
                                    <AccordionButton>
                                        <Box flex="1" textAlign="left">
                                            <Text fontWeight="medium">{t('infographic_faq_tech_5_q', 'Is my data secure when using the tool?')}</Text>
                                        </Box>
                                        <AccordionIcon />
                                    </AccordionButton>
                                    <AccordionPanel pb={4}>
                                        <Text color="gray.600">{t('infographic_faq_tech_5_a', 'We take data security seriously. Your input content is processed securely and is not stored permanently after generation. We do not use your content for training our AI or share it with third parties. For sensitive information, we recommend reviewing our privacy policy.')}</Text>
                                    </AccordionPanel>
                                </AccordionItem>
                            </Accordion>
                        </Box>
                    </SimpleGrid>

                    <Box textAlign="center" mt={4}>
                        <Text mb={4}>{t('faq_more_questions', 'Still have questions?')}</Text>
                        <Link href="https://discord.gg/XtdZFBy4uR" target='_blank' color="blue.500" fontWeight="medium">
                            {t('faq_contact_support', 'Contact our support team')}
                        </Link>
                    </Box>
                </VStack>
            </Section>

            {/* CTA 部分 */}
            <Box
                w="100%"
                bgGradient="linear(to-r, blue.500, purple.500)"
                py={16}
                px={4}
                position="relative"
                overflow="hidden"
            >
                {/* 背景装饰元素 */}
                <Box
                    position="absolute"
                    top="-10%"
                    right="-5%"
                    width="300px"
                    height="300px"
                    borderRadius="full"
                    bg="rgba(255,255,255,0.1)"
                    zIndex={0}
                />
                <Box
                    position="absolute"
                    bottom="-15%"
                    left="-10%"
                    width="400px"
                    height="400px"
                    borderRadius="full"
                    bg="rgba(255,255,255,0.05)"
                    zIndex={0}
                />

                <VStack spacing={8} maxW="3xl" mx="auto" textAlign="center" position="relative" zIndex={1}>
                    <Badge colorScheme="whiteAlpha" fontSize="md" px={3} py={1} borderRadius="full">
                        {t('cta_badge', 'Start Creating Today')}
                    </Badge>

                    <Heading color="white" size="xl">
                        {t('cta_title')}
                    </Heading>

                    <Text color="whiteAlpha.900" fontSize="lg" maxW="2xl">
                        {t('cta_description')}
                    </Text>

                    <HStack spacing={4}>
                        <Button
                            size="lg"
                            colorScheme="whiteAlpha"
                            rightIcon={<FaRocket />}
                            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        >
                            {t('cta_button')}
                        </Button>

                        <Button
                            size="lg"
                            variant="outline"
                            colorScheme="whiteAlpha"
                            onClick={() => document.querySelector('#examples').scrollIntoView({ behavior: 'smooth' })}
                        >
                            {t('cta_examples_button', 'View Examples')}
                        </Button>
                    </HStack>

                    <Text color="whiteAlpha.800" fontSize="sm">
                        {t('cta_no_credit_card', 'No credit card required. Start with free daily generations.')}
                    </Text>
                </VStack>
            </Box>
        </VStack>
    )
}

export default InfographicIntro