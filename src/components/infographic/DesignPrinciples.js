import { Box, Heading, Text, SimpleGrid, Icon, VStack, useBreakpointValue, Image, Flex, Badge, HStack } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { MdOutlineDesignServices, MdColorLens, MdOutlineBalance, MdOutlineAlignHorizontalCenter, MdOutlineSpaceBar, MdOutlineFormatSize, MdOutlineAutoGraph } from 'react-icons/md'
import Section from '../common/Section'

const PrincipleCard = ({ icon, title, description, examples }) => {
    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            overflow="hidden"
            h="100%"
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
            p={6}
        >
            <Flex direction="column" height="100%">
                <Flex alignItems="center" mb={4}>
                    <Icon as={icon} w={8} h={8} color="purple.500" mr={3} />
                    <Heading size="md">{title}</Heading>
                </Flex>
                
                <Text color="gray.600" mb={4} flex="1">
                    {description}
                </Text>
                
                {examples && examples.length > 0 && (
                    <VStack align="start" spacing={2} mt="auto">
                        <Text fontWeight="bold" color="blue.600">How our AI applies this:</Text>
                        {examples.map((example, idx) => (
                            <Flex key={idx} alignItems="center">
                                <Box w={2} h={2} borderRadius="full" bg="blue.400" mr={2}></Box>
                                <Text fontSize="sm">{example}</Text>
                            </Flex>
                        ))}
                    </VStack>
                )}
            </Flex>
        </Box>
    )
}

const DesignPrinciples = () => {
    const { t } = useTranslation('infographic')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const principles = [
        {
            icon: MdOutlineBalance,
            title: t('design_principle_1_title', 'Visual Hierarchy'),
            description: t('design_principle_1_description', 'Organizing elements to show their order of importance, guiding viewers through the content in a deliberate sequence.'),
            examples: [
                t('design_principle_1_example_1', 'Automatically sizes elements based on importance'),
                t('design_principle_1_example_2', 'Places key information in focal areas'),
                t('design_principle_1_example_3', 'Uses contrast to highlight critical data')
            ]
        },
        {
            icon: MdColorLens,
            title: t('design_principle_2_title', 'Color Theory'),
            description: t('design_principle_2_description', 'Strategic use of color to convey meaning, create emotional responses, and enhance readability and comprehension.'),
            examples: [
                t('design_principle_2_example_1', 'Selects harmonious color palettes'),
                t('design_principle_2_example_2', 'Uses color to categorize related information'),
                t('design_principle_2_example_3', 'Ensures sufficient contrast for readability')
            ]
        },
        {
            icon: MdOutlineAlignHorizontalCenter,
            title: t('design_principle_3_title', 'Alignment'),
            description: t('design_principle_3_description', 'Creating order and organization by ensuring elements are properly aligned, creating clean visual connections.'),
            examples: [
                t('design_principle_3_example_1', 'Creates consistent alignment patterns'),
                t('design_principle_3_example_2', 'Aligns related elements to show relationships'),
                t('design_principle_3_example_3', 'Maintains grid-based layouts for clarity')
            ]
        },
        {
            icon: MdOutlineSpaceBar,
            title: t('design_principle_4_title', 'White Space'),
            description: t('design_principle_4_description', 'Strategic use of empty space to improve readability, focus attention, and create a clean, professional appearance.'),
            examples: [
                t('design_principle_4_example_1', 'Balances content density with breathing room'),
                t('design_principle_4_example_2', 'Uses margins to frame important information'),
                t('design_principle_4_example_3', 'Prevents visual clutter and cognitive overload')
            ]
        },
        {
            icon: MdOutlineFormatSize,
            title: t('design_principle_5_title', 'Typography'),
            description: t('design_principle_5_description', 'Selection and arrangement of typefaces to enhance readability, establish tone, and create visual interest.'),
            examples: [
                t('design_principle_5_example_1', 'Selects appropriate fonts for content type'),
                t('design_principle_5_example_2', 'Creates typographic hierarchy for scanning'),
                t('design_principle_5_example_3', 'Maintains consistent font usage for cohesion')
            ]
        },
        {
            icon: MdOutlineAutoGraph,
            title: t('design_principle_6_title', 'Data Visualization'),
            description: t('design_principle_6_description', 'Transforming complex data into clear, accurate, and meaningful visual representations that reveal patterns and insights.'),
            examples: [
                t('design_principle_6_example_1', 'Selects optimal chart types for data relationships'),
                t('design_principle_6_example_2', 'Simplifies complex data without distortion'),
                t('design_principle_6_example_3', 'Uses visual cues to highlight key insights')
            ]
        }
    ];

    return (
        <Section bg="blue.50" id="design-principles">
            <VStack spacing={8} width="100%">
                <Badge colorScheme="purple" fontSize="md" px={3} py={1} borderRadius="full">
                    {t('design_principles_badge', 'Professional Design')}
                </Badge>
                
                <Heading
                    size="lg"
                    textAlign="center"
                    bgGradient="linear(to-r, purple.400, blue.400)"
                    bgClip="text"
                >
                    {t('design_principles_title', 'Design Principles Our AI Applies')}
                </Heading>
                
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={4}
                >
                    {t('design_principles_description', 'Our AI automatically applies these professional design principles to every infographic it creates, ensuring high-quality, effective visual communication without requiring design expertise.')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8} width="100%">
                    {principles.map((principle, index) => (
                        <PrincipleCard
                            key={index}
                            icon={principle.icon}
                            title={principle.title}
                            description={principle.description}
                            examples={principle.examples}
                        />
                    ))}
                </SimpleGrid>
            </VStack>
        </Section>
    )
}

export default DesignPrinciples
