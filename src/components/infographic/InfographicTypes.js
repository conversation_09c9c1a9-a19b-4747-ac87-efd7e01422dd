import { Box, Heading, Text, SimpleGrid, Icon, VStack, useBreakpointValue, Image, Flex, Badge } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaChartBar, FaChartLine, FaSitemap, FaRegObjectGroup, FaListUl, FaRegClock, FaBalanceScale, FaLayerGroup } from 'react-icons/fa'
import Section from '../common/Section'

const TypeCard = ({ icon, title, description, imageSrc, benefits }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })

    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            overflow="hidden"
            h="100%"
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
            display="flex"
            flexDirection="column"
        >
            <Flex p={5} alignItems="center" borderBottomWidth="1px" borderBottomColor="gray.100">
                <Icon as={icon} w={6} h={6} color="blue.500" mr={3} />
                <Heading size="md">{title}</Heading>
            </Flex>
            
            {imageSrc && (
                <Image
                    src={imageSrc}
                    alt={title}
                    height={isMobile ? "180px" : "200px"}
                    width="100%"
                    objectFit="cover"
                />
            )}
            
            <Box p={5} flex="1">
                <Text color="gray.600" mb={4}>{description}</Text>
                
                {benefits && benefits.length > 0 && (
                    <VStack align="start" spacing={2} mt={3}>
                        <Text fontWeight="bold">Key Benefits:</Text>
                        {benefits.map((benefit, idx) => (
                            <Flex key={idx} alignItems="center">
                                <Box w={2} h={2} borderRadius="full" bg="green.400" mr={2}></Box>
                                <Text fontSize="sm">{benefit}</Text>
                            </Flex>
                        ))}
                    </VStack>
                )}
            </Box>
            
            <Box p={3} bg="blue.50">
                <Badge colorScheme="blue" px={2} py={1} borderRadius="full">
                    AI-Generated
                </Badge>
            </Box>
        </Box>
    )
}

const InfographicTypes = () => {
    const { t } = useTranslation('infographic')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const types = [
        {
            icon: FaChartBar,
            title: t('infographic_type_1_title', 'Statistical Infographics'),
            description: t('infographic_type_1_description', 'Transform complex data and statistics into easy-to-understand visual representations. Perfect for reports, presentations, and data-driven content.'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_piechart.png",
            benefits: [
                t('infographic_type_1_benefit_1', 'Simplifies complex data'),
                t('infographic_type_1_benefit_2', 'Increases information retention'),
                t('infographic_type_1_benefit_3', 'Highlights key metrics')
            ]
        },
        {
            icon: FaRegClock,
            title: t('infographic_type_2_title', 'Timeline Infographics'),
            description: t('infographic_type_2_description', 'Visualize chronological events, historical data, project timelines, or any sequence of events in a clear, engaging format.'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_timeline.png",
            benefits: [
                t('infographic_type_2_benefit_1', 'Shows progression over time'),
                t('infographic_type_2_benefit_2', 'Clarifies sequence of events'),
                t('infographic_type_2_benefit_3', 'Highlights key milestones')
            ]
        },
        {
            icon: FaSitemap,
            title: t('infographic_type_3_title', 'Process Infographics'),
            description: t('infographic_type_3_description', 'Illustrate steps in a process, workflow, or procedure with clear visual guidance. Ideal for tutorials, guides, and explaining complex processes.'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_process.png",
            benefits: [
                t('infographic_type_3_benefit_1', 'Clarifies complex procedures'),
                t('infographic_type_3_benefit_2', 'Improves process understanding'),
                t('infographic_type_3_benefit_3', 'Enhances workflow efficiency')
            ]
        },
        {
            icon: FaBalanceScale,
            title: t('infographic_type_4_title', 'Comparison Infographics'),
            description: t('infographic_type_4_description', 'Compare and contrast different options, products, ideas, or concepts side by side. Perfect for decision-making content and product comparisons.'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_comparison.png",
            benefits: [
                t('infographic_type_4_benefit_1', 'Facilitates decision making'),
                t('infographic_type_4_benefit_2', 'Highlights key differences'),
                t('infographic_type_4_benefit_3', 'Presents balanced information')
            ]
        },
        {
            icon: FaLayerGroup,
            title: t('infographic_type_5_title', 'Hierarchical Infographics'),
            description: t('infographic_type_5_description', 'Display organizational structures, taxonomies, or any hierarchical relationships. Ideal for organizational charts, categorizations, and nested concepts.'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_layered.png",
            benefits: [
                t('infographic_type_5_benefit_1', 'Clarifies organizational structure'),
                t('infographic_type_5_benefit_2', 'Shows relationships between elements'),
                t('infographic_type_5_benefit_3', 'Simplifies complex hierarchies')
            ]
        },
        {
            icon: FaListUl,
            title: t('infographic_type_6_title', 'List-Based Infographics'),
            description: t('infographic_type_6_description', 'Transform simple lists into engaging visual content. Perfect for tips, features, benefits, and any content that can be presented as a list.'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_swot.png",
            benefits: [
                t('infographic_type_6_benefit_1', 'Enhances readability of lists'),
                t('infographic_type_6_benefit_2', 'Increases visual engagement'),
                t('infographic_type_6_benefit_3', 'Simplifies complex information')
            ]
        }
    ];

    return (
        <Section bg="gray.50" id="infographic-types">
            <VStack spacing={8} width="100%">
                <Heading
                    size="lg"
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.400)"
                    bgClip="text"
                >
                    {t('infographic_types_title', 'Types of Infographics You Can Create')}
                </Heading>
                
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={4}
                >
                    {t('infographic_types_description', 'Our AI can generate various types of infographics to suit your specific content needs. Each type is optimized for different information presentation goals.')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8} width="100%">
                    {types.map((type, index) => (
                        <TypeCard
                            key={index}
                            icon={type.icon}
                            title={type.title}
                            description={type.description}
                            imageSrc={type.imageSrc}
                            benefits={type.benefits}
                        />
                    ))}
                </SimpleGrid>
            </VStack>
        </Section>
    )
}

export default InfographicTypes
