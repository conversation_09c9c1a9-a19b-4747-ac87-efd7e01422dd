import React, { useState, useEffect, useCallback } from 'react';
import {
    ReactFlow,
    useNodesState,
    useEdgesState,
    addEdge,
    MiniMap,
    Controls,
    MarkerType,
    Background,
} from '@xyflow/react';
import { IconButton, Tooltip, useBreakpointValue } from '@chakra-ui/react';
import { MdEdit, MdFullscreen, MdFullscreenExit } from 'react-icons/md';

import '@xyflow/react/dist/style.css';

import SimpleFloatingEdge from './SimpleFloatingEdge';
import AINode from './AINode';
import { extractJSONFromString } from '../../utils/jsonStringUtil';
import { node_color_themes } from './ColorMenu';
import { apiUrl } from '../../utils/apiUtils';
import { useToast, Button } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { FaEdit } from 'react-icons/fa';
import { APP_TYPE } from '../../utils/constants';

const snapGrid = [20, 20];
const nodeTypes = { ai_node: AINode };
const edgeTypes = { float_edge: SimpleFloatingEdge };

const defaultViewport = { x: 0, y: 0, zoom: 1.5 };

const FlowEditor = ({ content, mimeContents, imgGenerated, setImgGenerated, context, ai_action, mode, app, onDocSaved, doc }) => {
    const [nodes, setNodes, onNodesChange] = useNodesState([]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);
    const [rfInstance, setRfInstance] = useState(null);
    // const [doc, setDoc] = useState();
    const [isSaving, setIsSaving] = useState();
    const toast = useToast();
    const { t } = useTranslation('common');
    const [fullscreenRef, setFullscreenRef] = useState(null);
    const [isFullscreen, setIsFullscreen] = useState(false);

    const isMobile = useBreakpointValue({ base: true, md: false })

    const addNewNode = useCallback((data, nodes, edges, addToHead) => {
        let node = {
            id: (new Date().getTime() + nodes.length) + '',
            type: 'ai_node',
            data: {
                aigc_triggered: true,
                nodeType: 'aigc',
                ...data
            }
        };
        if (nodes.length) {
            edges.push({
                id: (new Date().getTime() + edges.length) + '',
                source: addToHead ? node.id : nodes[0].id,
                target: addToHead ? nodes[0].id : node.id,
                type: 'float_edge',
                markerEnd: { type: MarkerType.ArrowClosed }
            })
        }
        if (addToHead) {
            nodes.unshift(node);
        } else {
            nodes.push(node);
        }

        return node;
    }, [])

    useEffect(() => {
        if (!content && !mimeContents?.length) return;

        let nodes = [];
        let edges = [];

        if ([APP_TYPE.erase, APP_TYPE.avatar].includes(app)) {
            setImgGenerated(mimeContents[0]?.src);

            mimeContents[0]?.src && addNewNode({
                nodeType: 'image',
                queryType: 'image',
                title: context?.title,
                content: {
                    src: mimeContents[0].src,
                    caption: content,
                }
            }, nodes, edges);

        } else {
            const jsonResult = extractJSONFromString(content);

            const generated = jsonResult?.generated;
            if (!generated) {
                return;
            }

            if (ai_action === 'flow_task_breakdown') {
                const timeStamp = new Date().getTime();
                let priorities = []
                generated.items?.forEach(item => {
                    if (!priorities.includes(item.priority)) {
                        priorities.push(item.priority)
                    }
                })

                if (!priorities.length) {
                    priorities = ['high', 'medium', 'low'];
                }

                addNewNode({
                    aigc_done: false,
                    content: '',
                    contentType: 'todos',
                    todos: generated.items?.map((item, index) => {
                        item.id = timeStamp + index;
                        return item;
                    }),
                    priorities: priorities.length > 0 ? priorities : undefined,
                }, nodes, edges)
            } else if (ai_action === 'summary_keypoints') {
                addNewNode({
                    aigc_done: false,
                    title: 'Summary and key points',
                    content: generated.summary,
                    items: generated.keypoints
                }, nodes, edges)

            } else {
                let rootNode = addNewNode({
                    // aigc_done: false,
                    ai_action,
                    queryType: ['movie', 'book', 'link', 'video'].includes(mode) && mode || 'brainstorming',
                    content: generated?.central_topic || generated?.core_story || generated?.initial_analysis || generated?.decision || generated?.description || generated?.summary,
                    title: generated?.theme || generated?.title || generated?.problem || 'FunBlocks AI Mindmap',
                    brainstorming_scenario: generated?.target_scenario,
                    is_mindmap_root: true
                }, nodes, edges);

                const perspectives = generated?.key_perspectives || generated?.primary_branches || generated?.branches;
                if (perspectives?.length > 0) {
                    // const positions = getSubnodesPositions(node, perspectives.length);

                    perspectives.map((item, index) => {
                        if (!item.branches) return;

                        const color_theme = node_color_themes[index % node_color_themes.length];

                        addNewNode({
                            queryType: ['flow_subtopic_brainstorming', 'flow_brainstorming', 'flow_decision_analysis'].includes(ai_action) && 'perspective' || ['flow_mindmap', 'flow_book_mindmap', 'flow_movie_mindmap', 'describe_image_mindmap'].includes(ai_action) && 'mindmap_primary_branch' || 'perspective',
                            ai_action: ['flow_subtopic_brainstorming', 'flow_brainstorming', 'flow_decision_analysis'].includes(ai_action) && 'brainstorming_perspective' || ai_action === 'flow_mindmap' && 'mindmap_primary_branch' || ai_action === 'flow_book_mindmap' && 'book_mindmap_primary_branch' || ai_action === 'flow_movie_mindmap' && 'movie_mindmap_primary_branch' || ai_action === 'describe_image_mindmap' && 'image_mindmap_primary_branch' || 'brainstorming_perspective',
                            title: item.name,
                            items: item.branches,
                            userInput: item.name,
                            context_node_id: rootNode.id,
                            // context: { topic: generated.central_topic || node.data.title },
                            color_theme: color_theme?.id,
                        }, nodes, edges);
                    })
                }

                const insights = generated?.summary_insights || generated?.key_insights || generated?.transformative_insights || generated?.insights
                if (insights) {
                    addNewNode({
                        queryType: 'brainstorming_insights',
                        // ai_action: 'brainstorming_perspective',
                        context_node_id: rootNode.id,
                        title: 'Summary insights',
                        items: insights,
                        // context: { topic: generated.central_topic || node.data.title },
                        color_theme: node_color_themes.find(theme => theme.id === 'blue')?.id,
                    }, nodes, edges);
                }
            }
        }

        nodes = nodes.map((node, index) => {
            if (index === 0) {
                node.position = { x: 0, y: 0 };
            } else {
                const angle = - (index - 1) * (2 * Math.PI / (nodes.length - 1));
                const radius = 650; // You can adjust the radius as needed
                node.position = {
                    x: radius * Math.cos(angle),
                    y: radius * Math.sin(angle) - (index > nodes.length / 2 ? 300 : 0)
                };
            }
            return node;
        });

        if (nodes && ['video', 'link', 'image', 'others'].includes(mode)) {
            let contextNode = addNewNode({
                nodeType: mode === 'video' && 'video' || mode === 'image' && 'image' || mode === 'others' && 'note' || 'aigc',
                queryType: ['video', 'link', 'image'].includes(mode) && mode || undefined,
                vid: context.vid,
                context: ['others', 'image'].includes(mode) ? undefined : context,
                title: context.title,
                content: mode === 'others' ? context.userInput : (mode === 'image' ? context : undefined),
                userInput: mode === 'others' ? context.userInput : undefined
            }, nodes, edges, true);

            nodes[0].position = {
                x: [APP_TYPE.erase, APP_TYPE.avatar].includes(app) ? -450 : -900,
                y: 0
            }

            nodes[1].data.context_node_id = contextNode.id;
        }

        setNodes(nodes);
        setEdges(edges);
        onDocSaved(null);

    }, [content, mimeContents])

    useEffect(() => {
        if (nodes?.length) return;
        if (!doc?.jsonString) return;

        const flow = JSON.parse(doc.jsonString || '{}');
        // const { x = 0, y = 0, zoom = 1 } = flow.viewport || {};
        setNodes(flow.nodes || []);
        setEdges(flow.edges || []);

    }, [doc])

    const saveDoc = useCallback(async (nodes, jsonString) => {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 180000);

            let data = doc ? {
                doc: {
                    hid: doc.hid,
                    type: 'flow',
                    jsonString
                }
            } : {
                doc: {
                    type: 'flow',
                    title: nodes[0].data.userInput || nodes[0].data.title,
                    jsonString,
                    app,
                    mode
                },
                source: 'aitools'
            };

            setIsSaving(true)
            const endpoint = '/doc/upsert';
            const response = await fetch(`${apiUrl}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    data
                }),
                credentials: 'include',
                signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const result = await response.json();

                if (result?.data) {
                    // setDoc(result.data)
                    !!onDocSaved && onDocSaved(result.data)
                }
            }
        } catch (error) {
            console.error('Error in saving doc:', error);

        } finally {
            setIsSaving(false);
        }
    }, [onDocSaved, doc])

    useEffect(() => {
        if (nodes?.length) {
            if (!doc && !isSaving) {
                let flow = rfInstance?.getNodes()?.length ? rfInstance.toObject() : {
                    nodes,
                    edges,
                    viewport: {
                        x: 0,
                        y: 0,
                        zoom: 1
                    }
                };
                const jsonString = JSON.stringify(flow);
                saveDoc(nodes, jsonString);
            }
        }
    }, [nodes, edges, rfInstance])

    const onConnect = useCallback(
        (params) =>
            setEdges((eds) =>
                addEdge({ ...params, animated: true }, eds),
            ),
        [],
    );

    React.useEffect(() => {
        const handleShowToast = () => {
            toast({
                title: t('to_aiflow'),
                description: <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    rowGap: 10
                }}>

                    <span style={{
                        font: 18,
                        // fontWeight: 'bold'
                    }}>{t('to_aiflow_tips')}</span>

                    <Button
                        size={'sm'}
                        onClick={() => window.open('https://app.funblocks.net/#/aiflow?hid=' + doc.hid)}
                    >FunBlocks AIFlow</Button>
                </div>,
                duration: 10000,
                isClosable: true,
            });
        };

        window.addEventListener('showAIFlowToast', handleShowToast);

        return () => {
            window.removeEventListener('showAIFlowToast', handleShowToast);
        };
    }, [toast, t, doc]);

    const toggleFullscreen = () => {
        if (!fullscreenRef) return;

        if (!document.fullscreenElement) {
            fullscreenRef.requestFullscreen().catch(err => {
                console.error(`Error attempting to enable fullscreen: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    };

    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
        };
    }, []);

    return (
        <DndProvider backend={HTML5Backend}>
            <div
                ref={setFullscreenRef}
                style={{
                    position: 'relative',
                    width: '100%',
                    height: '100%',
                    backgroundColor: isFullscreen ? '#f8f8f8' : 'transparent'
                }}
            >
                {
                    isMobile && imgGenerated && <img
                        src={imgGenerated}
                    />
                }
                {
                    !(isMobile && imgGenerated) &&
                    <ReactFlow
                        nodes={nodes}
                        edges={edges}
                        onNodesChange={onNodesChange}
                        onEdgesChange={onEdgesChange}
                        onConnect={onConnect}
                        onInit={setRfInstance}
                        nodeTypes={nodeTypes}
                        edgeTypes={edgeTypes}
                        snapToGrid={true}
                        snapGrid={snapGrid}
                        defaultViewport={defaultViewport}
                        fitView
                        attributionPosition="bottom-left"
                        style={{
                            borderRadius: 6
                        }}
                    >
                        <Background style={{ backgroundColor: '#f8f8f8' }} />
                        <Controls />

                        <div
                            style={{
                                position: 'absolute',
                                right: '20px',
                                bottom: '20px',
                                zIndex: 4,
                                display: 'flex',
                                flexDirection: 'column',
                                rowGap: 10
                            }}
                        >
                            <Tooltip label={"Open with FunBlocks AIFlow"} aria-label="Open with FunBlocks AIFlow">
                                <IconButton
                                    aria-label={"Open with FunBlocks AIFlow"}
                                    icon={<MdEdit size={20} />}
                                    onClick={() => window.open('https://app.funblocks.net/#/aiflow?hid=' + doc.hid)}
                                    colorScheme="gray"
                                    variant="solid"
                                    size="md"
                                    borderRadius="full"
                                    boxShadow="md"
                                    _hover={{ transform: 'scale(1.1)' }}
                                    transition="all 0.2s"
                                />
                            </Tooltip>
                            <Tooltip label={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"} aria-label="Fullscreen tooltip">
                                <IconButton
                                    aria-label={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
                                    icon={isFullscreen ? <MdFullscreenExit size={24} /> : <MdFullscreen size={24} />}
                                    onClick={toggleFullscreen}
                                    colorScheme="gray"
                                    variant="solid"
                                    size="md"
                                    borderRadius="full"
                                    boxShadow="md"
                                    _hover={{ transform: 'scale(1.1)' }}
                                    transition="all 0.2s"
                                />
                            </Tooltip>
                        </div>
                    </ReactFlow>
                }

            </div>
        </DndProvider>
    );
};

export default FlowEditor;