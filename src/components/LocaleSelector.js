import { Select } from '@chakra-ui/react'
import { useRouter } from 'next/router'

const LocaleSelector = () => {
  const router = useRouter()

  const changeLanguage = (e) => {
    const locale = e.target.value
    router.push(router.pathname, router.asPath, { locale })
  }

  return (
    <Select onChange={changeLanguage} value={router.locale} size="sm" width="auto" variant={'filled'} borderRadius="full">
      <option value="en">English</option>
      <option value="zh">中文</option>
    </Select>
  )
}

export default LocaleSelector
