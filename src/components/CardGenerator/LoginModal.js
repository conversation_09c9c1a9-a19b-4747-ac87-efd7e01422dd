import React, { useEffect, useState } from 'react';
import {
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalCloseButton,
    Button,
    VStack,
    Text,
} from '@chakra-ui/react';
import { useAuth } from '../../contexts/AuthContext';
import { GoogleLogin } from './GoogleLogin';
import EmailVerificationForm from './EmailVerificationForm';
import { FaEnvelopeOpen } from 'react-icons/fa';
import { useTranslation } from 'next-i18next';

const LoginModal = ({ }) => {
    const { t } = useTranslation('common');
    const { isLoginModalOpen, closeLoginModal } = useAuth();
    const [showEmailVerification, setShowEmailVerification] = useState(false);

    const handleEmailLogin = () => {
        setShowEmailVerification(true);
    };

    useEffect(() => {
        if (isLoginModalOpen) {
            setShowEmailVerification(false);
        }
    }, [isLoginModalOpen]);

    return (
        <Modal isOpen={isLoginModalOpen} onClose={closeLoginModal} isCentered>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader>{t('login')}</ModalHeader>
                <ModalCloseButton />
                <ModalBody pt={4} pb={10}>
                    {!showEmailVerification ? (
                        <VStack spacing={4}>
                            <GoogleLogin showButton={true} />
                            <Button onClick={handleEmailLogin} width="100%" justifyContent={'flex-start'}>
                                <FaEnvelopeOpen style={{ padding: 4, marginRight: 6 }} size={32} color='dodgerblue'/><Text>{t('email_verification_login')}</Text>
                            </Button>
                        </VStack>
                    ) : (
                        <EmailVerificationForm />
                    )}
                </ModalBody>
            </ModalContent>
        </Modal>
    );
};

export default LoginModal;
