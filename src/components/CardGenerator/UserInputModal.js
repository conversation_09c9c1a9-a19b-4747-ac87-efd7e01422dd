import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'next-i18next';
import { 
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Button,
  Textarea,
  Text,
  VStack
} from '@chakra-ui/react';

const UserInputModal = ({ 
  visible, 
  title,
  placeholder,
  onConfirm, 
  onCancel 
}) => {
  const { t } = useTranslation('common');
  const [userInput, setUserInput] = useState('');
  const textInputRef = useRef(null);

  useEffect(() => {
    if (visible) {
      setUserInput('');
      // 延迟聚焦，确保模态框已完全渲染
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.focus();
        }
      }, 100);
    }
  }, [visible]);

  const handleConfirm = () => {
    if (userInput.trim()) {
      onConfirm(userInput.trim());
    }
    handleClose();
  };

  const handleClose = () => {
    setUserInput('');
    onCancel();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      handleClose();
    } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleConfirm();
    }
  };

  return (
    <Modal 
      isOpen={visible} 
      onClose={handleClose}
      size="lg"
      isCentered
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {title || t('user_input')}
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Textarea
              ref={textInputRef}
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              placeholder={placeholder || t('please_enter_your_requirements')}
              rows={6}
              resize="vertical"
              onKeyDown={handleKeyDown}
            />
            
            <Text fontSize="sm" color="gray.500">
              {t('input_hint')}
            </Text>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button 
            variant="outline" 
            mr={3} 
            onClick={handleClose}
          >
            {t('cancel')}
          </Button>
          <Button 
            colorScheme="blue" 
            onClick={handleConfirm}
            isDisabled={!userInput.trim()}
          >
            {t('confirm')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default UserInputModal;
