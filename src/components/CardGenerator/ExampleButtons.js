import React from 'react';
import { Box, FormLabel, HStack, useBreakpointValue } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

const ExampleButtons = ({ onSelectExample, mode }) => {
  const { t } = useTranslation('common');
  const isMobile = useBreakpointValue({ base: true, md: false })

  const examples = {
    cardGuru: [
      { 
        desc: 'Paradoxical interpretation',
        type: 'infographics_paradoxical_interpretation', 
        message: 'Success' 
      },
      { 
        desc: 'Aha insight',
        type: 'infographics_aha_insight', 
        message: 'Tariff' 
      },
      {
        desc: 'Multi-perspective',
        type: 'infographics_multi_perspectives',
        message: 'The future of AI in society',
      },
      { 
        desc: 'Cross Disciplinary',
        type: 'infographics_cross_disciplinary', 
        message: 'Artifical Intelligence',
      },
    ],
    graphChat: [
      {
        type: 'chart_infographics',
        message: 'Analyze the relationship between climate change and global economy'
      },
      {
        type: 'chart_infographics',
        message: 'Compare the education systems of Finland and the United States'
      },
      {
        type: 'chart_infographics',
        message: 'Explain the process of photosynthesis in plants'
      },
    ],
  };

  const currentExamples = examples[mode] || [];

  if (currentExamples.length === 0) {
    return null;
  }

  return (
    <HStack spacing={isMobile ? 2 : 2} wrap="wrap">
      <FormLabel m={0}>{t('examples')}</FormLabel>
      {currentExamples.map((example, index) => (
        <Box
          key={index}
          size="sm"
          variant="ghost"
          borderRadius="full"
          bg='white'
          color={'gray.600'}
          cursor="pointer"
          fontSize="sm"
          padding={1}
          paddingLeft={3}
          paddingRight={3}
          onClick={() => onSelectExample(example.type, example.message)}
        >
          {example.desc ? `${example.desc}: ${example.message}` : example.message}
        </Box>
      ))}
    </HStack>
  );
};

export default ExampleButtons;
