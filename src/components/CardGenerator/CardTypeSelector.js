import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>S<PERSON>ck, Select } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { useEffect } from 'react';

const CardTypeSelector = ({ onSelect, value, cardTypes, mode }) => {
  const { t } = useTranslation('common');

  // useEffect(() => {
  //   if(!value) {
  //     onSelect(cardTypes[0])
  //   }
  // }, [value])

  return (
    <HStack width={'100%'}>
      <FormLabel whiteSpace="nowrap" m={0} marginRight={0}>{t(mode === 'makeGraph' ? 'select_graph_type' : 'select_card')}</FormLabel>
      <Select
        bg='white'
        variant={'ghost'}
        onChange={(e) => e.target.value && onSelect(cardTypes.find(type => type.value === e.target.value))}
        value={value}
        isRequired
      >
        {cardTypes?.map((type) => (
          <option key={type.value} value={type.value}>
            {type.label}
          </option>
        ))}
      </Select>
    </HStack>
  )
}

export default CardTypeSelector
