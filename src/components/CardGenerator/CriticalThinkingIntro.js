import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaChartLine, FaGraduationCap, FaUsers, FaRegCompass, FaMapSigns, FaRegChartBar } from 'react-icons/fa'
import { MdCheckCircle, MdTimeline, MdAutoGraph, MdPsychology, MdOutlinePsychology, MdOutlineAnalytics } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="teal.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const CriticalThinkingIntro = () => {
    const { t } = useTranslation('criticalthinking')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaBrain,
            title: t('critical_feature_1_title'),
            text: t('critical_feature_1_text')
        },
        {
            icon: MdTimeline,
            title: t('critical_feature_2_title'),
            text: t('critical_feature_2_text')
        },
        {
            icon: MdAutoGraph,
            title: t('critical_feature_3_title'),
            text: t('critical_feature_3_text')
        },
        {
            icon: MdPsychology,
            title: t('critical_feature_4_title'),
            text: t('critical_feature_4_text')
        }
    ]

    const applications = [
        {
            icon: FaGraduationCap,
            title: t('critical_app_1_title'),
            text: t('critical_app_1_text')
        },
        {
            icon: FaRegChartBar,
            title: t('critical_app_2_title'),
            text: t('critical_app_2_text')
        },
        {
            icon: FaRegCompass,
            title: t('critical_app_3_title'),
            text: t('critical_app_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('critical_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('critical_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="teal.500">{t('critical_learning_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`critical_learning_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="teal.500">{t('critical_practical_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`critical_practical_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Applications Section */}
            <Section bg="gray.50" title={t('critical_applications_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {applications.map((app, index) => (
                        <Feature
                            key={index}
                            icon={app.icon}
                            title={app.title}
                            text={app.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Critical Thinking Section */}
            <Section bg="white" title={t('critical_thinking_title')}>
                <VStack spacing={6} align="start" maxW="4xl" mx="auto">
                    <Text fontSize="lg" color="gray.600">
                        {t('critical_thinking_intro')}
                    </Text>
                    <Box bg="gray.50" p={6} rounded="xl" width="100%">
                        <Text fontWeight="bold" mb={4}>{t('critical_thinking_key_points')}</Text>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdOutlinePsychology} color="teal.500" />
                                    <Text>{t(`critical_thinking_point_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </Box>
                </VStack>
            </Section>

            {/* Critical Analysis Section */}
            <Section bg="gray.50" title={t('critical_analysis_title')}>
                <VStack spacing={6} align="start" maxW="4xl" mx="auto">
                    <Text fontSize="lg" color="gray.600">
                        {t('critical_analysis_intro')}
                    </Text>
                    <Box bg="white" p={6} rounded="xl" width="100%" shadow="md">
                        <Text fontWeight="bold" mb={4}>{t('critical_analysis_components')}</Text>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdOutlineAnalytics} color="teal.500" />
                                    <Text>{t(`critical_analysis_component_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </Box>
                </VStack>
            </Section>

            {/* Methodology Section */}
            <Section bg="white" title={t('critical_methodology_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="teal.500">{t('critical_framework_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4, 5].map((i) => (
                                <ListItem key={i}>
                                    <Text fontWeight="bold">{t(`critical_framework_step_${i}_title`)}</Text>
                                    <Text color="gray.600">{t(`critical_framework_step_${i}_text`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="teal.500">{t('critical_principles_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i}>
                                    <Text fontWeight="bold">{t(`critical_principle_${i}_title`)}</Text>
                                    <Text color="gray.600">{t(`critical_principle_${i}_text`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('critical_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`critical_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`critical_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default CriticalThinkingIntro 