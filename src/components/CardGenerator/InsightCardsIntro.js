import React from 'react'
import { VStack, Heading, Text, SimpleGrid, Box, Flex, Button, Image, Badge, useBreakpointValue, Icon, HStack, Link, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon } from '@chakra-ui/react'
import { FaMagic, FaLightbulb, FaBrain, FaChartLine, FaGraduationCap, FaRegLightbulb, FaQuoteLeft, FaArrowRight, FaRocket, FaBalanceScale, FaUniversity, FaComments, FaLayerGroup, FaExchangeAlt, FaQuestionCircle } from 'react-icons/fa'
import { MdAutoAwesome, MdInsights, MdPsychology, MdOutlineDesignServices, MdCategory } from 'react-icons/md'
import { useTranslation } from 'next-i18next'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'
import Testimonials from '../common/Testimonials'
import CaseStudies from '../common/CaseStudies'
import ComparisonTable from '../common/ComparisonTable'
import ResearchBacked from '../common/ResearchBacked'

const appname = 'AI InsightCards Generator'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

// Example card component
const ExampleCard = ({ title, description, imageSrc, cardType }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })

    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            overflow="hidden"
            h="100%"
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
        >
            <Image
                src={imageSrc}
                alt={title}
                height={isMobile ? "240px" : "440px"}
                width="100%"
                objectFit="contain"
            />
            <Box p={5}>
                <Badge colorScheme="purple" mb={2}>{cardType}</Badge>
                <Heading size="md" mb={2}>{title}</Heading>
                <Text color="gray.600">{description}</Text>
            </Box>
        </Box>
    )
}

const InsightCardsIntro = () => {
    const { t } = useTranslation('insightcards')
    const isMobile = useBreakpointValue({ base: true, md: false })

    // Main features
    const mainFeatures = [
        {
            icon: MdInsights,
            title: t('insightcards_feature_1_title'),
            text: t('insightcards_feature_1_text')
        },
        {
            icon: FaExchangeAlt,
            title: t('insightcards_feature_2_title'),
            text: t('insightcards_feature_2_text')
        },
        {
            icon: FaLayerGroup,
            title: t('insightcards_feature_3_title'),
            text: t('insightcards_feature_3_text')
        },
        {
            icon: MdOutlineDesignServices,
            title: t('insightcards_feature_4_title'),
            text: t('insightcards_feature_4_text')
        }
    ]

    // Card types
    const cardTypes = [
        {
            icon: FaLightbulb,
            title: t('card_type_1_title'),
            text: t('card_type_1_text')
        },
        {
            icon: FaBalanceScale,
            title: t('card_type_2_title'),
            text: t('card_type_2_text')
        },
        {
            icon: FaExchangeAlt,
            title: t('card_type_3_title'),
            text: t('card_type_3_text')
        },
        {
            icon: FaUniversity,
            title: t('card_type_4_title'),
            text: t('card_type_4_text')
        },
        {
            icon: FaBrain,
            title: t('card_type_5_title'),
            text: t('card_type_5_text')
        },
        {
            icon: FaComments,
            title: t('card_type_6_title'),
            text: t('card_type_6_text')
        }
    ]

    // Example cards
    const examples = [
        {
            title: t('example_1_title'),
            description: t('example_1_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_insightcards_paradoxical.png",
            cardType: t('example_1_type')
        },
        {
            title: t('example_2_title'),
            description: t('example_2_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_insightcards_crossdisciplinary.png",
            cardType: t('example_2_type')
        },
        {
            title: t('example_3_title'),
            description: t('example_3_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_insightcards_aha.png",
            cardType: t('example_3_type')
        },
        {
            title: t('example_4_title'),
            description: t('example_4_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_insightcards_philosophical.png",
            cardType: t('example_4_type')
        }
    ]

    // Use cases
    const useCases = [
        {
            icon: FaGraduationCap,
            title: t('use_case_1_title'),
            text: t('use_case_1_text')
        },
        {
            icon: FaRocket,
            title: t('use_case_2_title'),
            text: t('use_case_2_text')
        },
        {
            icon: FaChartLine,
            title: t('use_case_3_title'),
            text: t('use_case_3_text')
        },
        {
            icon: MdPsychology,
            title: t('use_case_4_title'),
            text: t('use_case_4_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={isMobile ? 6 : 16}>
            {/* Hero section */}
            <Box
                w="100%"
                bgGradient="linear(to-br, blue.50, purple.50)"
                py={isMobile ? 12 : 16}
                px={4}
                borderBottomWidth="1px"
                borderBottomColor="gray.200"
            >
                <VStack spacing={8} maxW="4xl" mx="auto" textAlign="center">
                    <Badge colorScheme="blue" fontSize={isMobile ? "sm" : "md"} px={3} py={1} borderRadius="full">
                        {t('hero_badge')}
                    </Badge>

                    <Heading
                        fontSize={isMobile ? "3xl" : "5xl"}
                        fontWeight="extrabold"
                        bgGradient="linear(to-r, blue.400, purple.500)"
                        bgClip="text"
                        lineHeight="1.2"
                    >
                        {t('hero_heading')}
                    </Heading>

                    <Text fontSize={isMobile ? "lg" : "xl"} color="gray.600" maxW="2xl">
                        {t('hero_description')}
                    </Text>

                    <HStack spacing={4}>
                        <Button
                            size="lg"
                            colorScheme="purple"
                            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        >
                            {t('hero_start_button')}
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            colorScheme="blue"
                            onClick={() => document.getElementById('examples').scrollIntoView({ behavior: 'smooth' })}
                        >
                            {t('hero_examples_button')}
                        </Button>
                    </HStack>

                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} width="100%" mt={4}>
                        <VStack>
                            <Text fontSize="3xl" fontWeight="bold" color="purple.500">
                                {t('hero_metric_1_number')}
                            </Text>
                            <Text color="gray.600">{t('hero_metric_1_text')}</Text>
                        </VStack>
                        <VStack>
                            <Text fontSize="3xl" fontWeight="bold" color="purple.500">
                                {t('hero_metric_2_number')}
                            </Text>
                            <Text color="gray.600">{t('hero_metric_2_text')}</Text>
                        </VStack>
                        <VStack>
                            <Text fontSize="3xl" fontWeight="bold" color="purple.500">
                                {t('hero_metric_3_number')}
                            </Text>
                            <Text color="gray.600">{t('hero_metric_3_text')}</Text>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Box>

            {/* Introduction section */}
            <Section bg="white">
                <VStack spacing={8} maxW="3xl" mx="auto" textAlign="center">
                    <Text fontSize={isMobile ? "md" : "lg"} color="gray.600">
                        {t('insightcards_intro_description')}
                    </Text>
                </VStack>

                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8} mt={12} width="100%">
                    {mainFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            title={feature.title}
                            text={feature.text}
                            icon={feature.icon}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Card Types section */}
            <Section bg="gray.50" title={t('card_types_title')}>
                <Text
                    fontSize={isMobile ? "md" : "lg"}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('card_types_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8} width="100%">
                    {cardTypes.map((type, index) => (
                        <Feature
                            key={index}
                            title={type.title}
                            text={type.text}
                            icon={type.icon}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Examples showcase */}
            <Section bg="white" id="examples">
                <Heading
                    size="lg"
                    mb={8}
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.400)"
                    bgClip="text"
                >
                    {t('examples_title')}
                </Heading>

                <Text
                    fontSize={isMobile ? "md" : "lg"}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('examples_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    {examples.map((example, index) => (
                        <ExampleCard
                            key={index}
                            title={example.title}
                            description={example.description}
                            imageSrc={example.imageSrc}
                            cardType={example.cardType}
                        />
                    ))}
                </SimpleGrid>

                <Box textAlign="center" mt={10}>
                    <Text fontSize="lg" fontWeight="medium" mb={4}>
                        {t('examples_subtitle')}
                    </Text>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('examples_button')}
                    </Button>
                </Box>
            </Section>

            {/* Use cases section */}
            <Section bg="gray.50" title={t('use_cases_title')}>
                <Text
                    fontSize={isMobile ? "md" : "lg"}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('use_cases_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8} width="100%">
                    {useCases.map((useCase, index) => (
                        <Feature
                            key={index}
                            title={useCase.title}
                            text={useCase.text}
                            icon={useCase.icon}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Testimonials section */}
            <Testimonials
                appname={appname}
                users={'10,000+'}
                rating={4.8}
                testimonials={[
                    {
                        content: t('testimonial_1_content', 'InsightCards has transformed how I teach critical thinking. The way it presents multiple perspectives on complex topics helps my students develop nuanced understanding. The visual format makes abstract concepts tangible and shareable, which has significantly increased engagement in my classes.'),
                        author: 'Dr. Emily Chen',
                        role: t('testimonial_1_role', 'Professor of Philosophy'),
                        organization: 'Stanford University',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_2_content', 'As a content creator, I\'ve been using InsightCards to generate unique perspectives for my articles and social media posts. The Paradoxical Interpretation cards consistently give me angles that I would never have thought of myself. My engagement has increased by 40% since I started sharing these visual insights.'),
                        author: 'Michael Torres',
                        role: t('testimonial_2_role', 'Content Strategist'),
                        organization: 'Digital Horizons',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_3_content', 'The Cross-disciplinary Analysis cards have been invaluable for our innovation team. We were stuck in our industry-specific thinking patterns, but InsightCards helped us apply principles from biology, physics, and psychology to our business challenges. We\'ve developed three patent-pending solutions as a direct result.'),
                        author: 'Sarah Johnson',
                        role: t('testimonial_3_role', 'Innovation Director'),
                        organization: 'TechNova Inc.',
                        rating: 4.5,
                        image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_4_content', 'I use InsightCards to prepare for client presentations. The Aha Insight cards help me identify the hidden logic behind complex market trends, which makes my analysis much more compelling. Clients are consistently impressed by the depth of understanding I can demonstrate in a visually appealing format.'),
                        author: 'David Rodriguez',
                        role: t('testimonial_4_role', 'Management Consultant'),
                        organization: 'Global Strategy Partners',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_5_content', 'The Philosophical Analysis cards have added incredible depth to my research on ethical implications of emerging technologies. Being able to quickly generate insights through the lens of different philosophical traditions has accelerated my work and opened new avenues of inquiry I hadn\'t considered.'),
                        author: 'Dr. Aisha Patel',
                        role: t('testimonial_5_role', 'Research Scientist'),
                        organization: 'Institute for Ethical Technology',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                        content: t('testimonial_6_content', 'As a high school teacher, I\'ve found the Layered Understanding cards to be perfect for helping students grasp difficult concepts. Starting with the basics and progressively adding complexity has helped my students build confidence while developing deeper understanding. The visual format is perfect for today\'s visual learners.'),
                        author: 'James Wilson',
                        role: t('testimonial_6_role', 'High School Teacher'),
                        organization: 'Westlake Academy',
                        rating: 5,
                        image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    }
                ]}
            />

            {/* Case studies section */}
            <CaseStudies
                appname={appname}
                description={t('case_studies_description', 'See how professionals across different fields have used InsightCards to transform their thinking, teaching, and communication.')}
                caseStudies={[
                    {
                        icon: FaGraduationCap,
                        title: t('case_study_1_title', 'Transforming Critical Thinking Education'),
                        industry: t('case_study_1_industry', 'Higher Education'),
                        challenge: t('case_study_1_challenge', 'A university philosophy department struggled to make abstract critical thinking concepts accessible to first-year students, resulting in high dropout rates and low engagement.'),
                        solution: t('case_study_1_solution', 'They implemented InsightCards across their curriculum, using Multi-perspectives and Paradoxical Interpretation cards to visually demonstrate how different viewpoints can coexist on complex topics.'),
                        results: t('case_study_1_results', 'Student engagement increased by 68%, course completion rates improved by 42%, and the department saw a 35% increase in students choosing philosophy as their major.')
                    },
                    {
                        icon: FaChartLine,
                        title: t('case_study_2_title', 'Breaking Innovation Barriers'),
                        industry: t('case_study_2_industry', 'Product Development'),
                        challenge: t('case_study_2_challenge', 'A technology company had hit a creative plateau, struggling to develop truly innovative solutions beyond incremental improvements to their existing product line.'),
                        solution: t('case_study_2_solution', 'Their innovation team began using Cross-disciplinary Analysis and Aha Insight cards to explore their challenges from completely new angles, applying principles from fields like biology, architecture, and psychology.'),
                        results: t('case_study_2_results', 'Within six months, the team developed four breakthrough product concepts, two of which have been fast-tracked to development with projected first-year revenues of $12M.')
                    }
                ]}
            />

            {/* Comparison table */}
            <ComparisonTable
                title={t("comparison_title")}
                description={t("comparison_description")}
                bg="white"
                productName={appname}
                highlightColumn="funblocks"
                columns={[
                    { key: 'funblocks', label: t('comparison_funblocks', 'FunBlocks InsightCards'), highlight: true },
                    { key: 'traditional', label: t('comparison_traditional', 'Traditional Research') },
                    { key: 'other_ai', label: t('comparison_other_ai', 'Text-Only AI Tools') },
                    { key: 'templates', label: t('comparison_templates', 'Static Templates') }
                ]}
                features={[
                    {
                        name: t('comparison_feature_1', 'AI-Generated Content'),
                        tooltip: t('comparison_feature_1_tooltip', 'Content is automatically generated by artificial intelligence'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_2', 'Visual Format'),
                        tooltip: t('comparison_feature_2_tooltip', 'Information is presented in visual, engaging format'),
                        funblocks: true,
                        traditional: false,
                        other_ai: false,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_3', 'Multiple Thinking Frameworks'),
                        tooltip: t('comparison_feature_3_tooltip', 'Ability to apply different cognitive frameworks to the same topic'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_4', 'One-Click Generation'),
                        tooltip: t('comparison_feature_4_tooltip', 'Create insights with minimal input required'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_5', 'Shareable Output'),
                        tooltip: t('comparison_feature_5_tooltip', 'Results can be easily shared with others'),
                        funblocks: true,
                        traditional: false,
                        other_ai: false,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_6', 'Time Efficiency'),
                        tooltip: t('comparison_feature_6_tooltip', 'Speed of generating useful insights'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_7', 'Perspective Diversity'),
                        tooltip: t('comparison_feature_7_tooltip', 'Ability to see topics from multiple viewpoints'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_8', 'Learning Effectiveness'),
                        tooltip: t('comparison_feature_8_tooltip', 'How well the format enhances understanding and retention'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: false
                    }
                ]}
            />

            {/* Research backed section */}
            <ResearchBacked
                title={t("research_title")}
                description={t("research_description")}
                bg="blue.50"
                productName={appname}
                buttons={[
                    { label: t('research_button_cognitive', 'Cognitive Science'), icon: FaBrain, colorScheme: 'blue' },
                    { label: t('research_button_visual', 'Visual Learning'), icon: FaLightbulb, colorScheme: 'purple' },
                    { label: t('research_button_perspective', 'Perspective-Taking'), icon: FaExchangeAlt, colorScheme: 'green' }
                ]}
                researchAreas={[
                    {
                        title: t('research_area_1_title', 'Dual Coding Theory'),
                        description: t('research_area_1_description', 'Research shows that combining visual and verbal information enhances learning and retention. InsightCards leverages this principle by presenting complex ideas in a visually engaging format.'),
                        icon: FaBrain,
                        color: 'blue',
                        citations: [
                            {
                                text: 'Paivio, A. (1991). Dual coding theory: Retrospect and current status. Canadian Journal of Psychology, 45(3), 255-287.',
                                url: 'https://doi.org/10.1037/h0084295'
                            },
                            {
                                text: 'Clark, J. M., & Paivio, A. (1991). Dual coding theory and education. Educational Psychology Review, 3(3), 149-210.',
                                url: 'https://doi.org/10.1007/BF01320076'
                            }
                        ]
                    },
                    {
                        title: t('research_area_2_title', 'Perspective-Taking'),
                        description: t('research_area_2_description', 'Studies demonstrate that examining topics from multiple perspectives leads to deeper understanding and more creative problem-solving. InsightCards facilitates this through its various card types.'),
                        icon: FaExchangeAlt,
                        color: 'green',
                        citations: [
                            {
                                text: 'Galinsky, A. D., & Moskowitz, G. B. (2000). Perspective-taking: Decreasing stereotype expression, stereotype accessibility, and in-group favoritism. Journal of Personality and Social Psychology, 78(4), 708-724.',
                                url: 'https://doi.org/10.1037/0022-3514.78.4.708'
                            },
                            {
                                text: 'Hoever, I. J., van Knippenberg, D., van Ginkel, W. P., & Barkema, H. G. (2012). Fostering team creativity: Perspective taking as key to unlocking diversity\'s potential. Journal of Applied Psychology, 97(5), 982-996.',
                                url: 'https://doi.org/10.1037/a0029159'
                            }
                        ]
                    },
                    {
                        title: t('research_area_3_title', 'Insight Formation'),
                        description: t('research_area_3_description', 'Cognitive research on how "aha moments" occur when new connections are formed between concepts. InsightCards is designed to facilitate these connections through its Aha Insight and Cross-disciplinary Analysis cards.'),
                        icon: FaLightbulb,
                        color: 'yellow',
                        citations: [
                            {
                                text: 'Kounios, J., & Beeman, M. (2014). The cognitive neuroscience of insight. Annual Review of Psychology, 65, 71-93.',
                                url: 'https://doi.org/10.1146/annurev-psych-010213-115154'
                            },
                            {
                                text: "Shen, W., Yuan, Y., Liu, C., & Luo, J. (2016). In search of the 'Aha!' experience: Elucidating the emotionality of insight problem-solving. British Journal of Psychology, 107(2), 281-298.",
                                url: 'https://doi.org/10.1111/bjop.12142'
                            }
                        ]
                    },
                    {
                        title: t('research_area_4_title', 'Visual Learning'),
                        description: t('research_area_4_description', 'Evidence that visual formats can improve comprehension of complex information by up to 400%. InsightCards transforms abstract concepts into visual representations that are easier to understand and remember.'),
                        icon: MdOutlineDesignServices,
                        color: 'purple',
                        citations: [
                            {
                                text: 'Mayer, R. E. (2020). Multimedia learning. Cambridge University Press.',
                                url: 'https://www.cambridge.org/highereducation/books/multimedia-learning/FB7E79A165D24D47CEACEB4D2C426ECD#overview'
                            },
                            {
                                text: 'Medina, J. (2008). Brain rules: 12 principles for surviving and thriving at work, home, and school. Pear Press.',
                                url: 'https://brainrules.net/'
                            }
                        ]
                    }
                ]}
            />

            {/* How to use section */}
            <HowToUse namespace="insightcards" />

            {/* FAQ Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('insightcards_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl" mx="auto">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                        <AccordionItem
                            key={i}
                            mb={4}
                            border="none"
                            bg="white"
                            borderRadius="lg"
                            boxShadow="md"
                            _hover={{ boxShadow: 'lg' }}
                            transition="all 0.3s"
                        >
                            <AccordionButton
                                pt={3}
                                pb={3}
                                _hover={{ bg: 'transparent' }}
                                borderRadius="lg"
                            >
                                <Box flex="1" textAlign="left">
                                    <HStack spacing={3}>
                                        <Icon as={FaQuestionCircle} color="blue.500" />
                                        <Text fontWeight="medium" fontSize="md">
                                            {t(`insightcards_faq_${i}_q`)}
                                        </Text>
                                    </HStack>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={6} px={6}>
                                <Text color="gray.600" whiteSpace="pre-wrap">
                                    {t(`insightcards_faq_${i}_a`)}
                                </Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>

            {/* CTA section */}
            <Box
                w="100%"
                bgGradient="linear(to-r, blue.500, purple.500)"
                py={16}
                px={4}
            >
                <VStack spacing={6} maxW="3xl" mx="auto" textAlign="center">
                    <Heading color="white" size="xl">
                        {t('cta_title')}
                    </Heading>
                    <Text color="whiteAlpha.900" fontSize="lg">
                        {t('cta_description')}
                    </Text>
                    <Button
                        size="lg"
                        colorScheme="whiteAlpha"
                        rightIcon={<FaRocket />}
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('cta_button')}
                    </Button>
                </VStack>
            </Box>
        </VStack>
    )
}

export default InsightCardsIntro
