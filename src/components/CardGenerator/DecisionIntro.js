import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaChartLine, FaBalanceScale, FaMagic, FaLightbulb, FaUsers } from 'react-icons/fa'
import { MdCheckCircle } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const DecisionIntro = () => {
    const { t } = useTranslation('decision')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const keyFeatures = [
        {
            icon: FaBrain,
            title: t('decision_feature_1_title'),
            text: t('decision_feature_1_text')
        },
        {
            icon: FaChartLine,
            title: t('decision_feature_2_title'),
            text: t('decision_feature_2_text')
        },
        {
            icon: FaBalanceScale,
            title: t('decision_feature_3_title'),
            text: t('decision_feature_3_text')
        }
    ]

    const useCases = [
        {
            icon: FaUsers,
            title: t('decision_usecase_1_title'),
            text: t('decision_usecase_1_text')
        },
        {
            icon: FaLightbulb,
            title: t('decision_usecase_2_title'),
            text: t('decision_usecase_2_text')
        },
        {
            icon: FaMagic,
            title: t('decision_usecase_3_title'),
            text: t('decision_usecase_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {keyFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white">
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('decision_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`decision_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('decision_advantages_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`decision_advantage_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Use Cases Section */}
            <Section bg="gray.50" title={t('decision_usecases_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {useCases.map((useCase, index) => (
                        <Feature
                            key={index}
                            icon={useCase.icon}
                            title={useCase.title}
                            text={useCase.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`decision_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`decision_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default DecisionIntro 