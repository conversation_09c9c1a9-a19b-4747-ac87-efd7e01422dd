import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaRocket, FaPuzzlePiece, FaChartLine, FaUsers, FaClock, FaSitemap, FaProjectDiagram, FaRegCompass, FaQuoteLeft } from 'react-icons/fa'
import { MdCheckCircle, MdAutoGraph, MdInsights, MdAccountTree } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const MindKitIntro = () => {
    const { t } = useTranslation('mindkit')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaBrain,
            title: t('mindkit_feature_1_title'),
            text: t('mindkit_feature_1_text')
        },
        {
            icon: FaSitemap,
            title: t('mindkit_feature_2_title'),
            text: t('mindkit_feature_2_text')
        },
        {
            icon: MdInsights,
            title: t('mindkit_feature_3_title'),
            text: t('mindkit_feature_3_text')
        }
    ]

    const benefits = [
        {
            icon: FaRegCompass,
            title: t('mindkit_benefit_1_title'),
            text: t('mindkit_benefit_1_text')
        },
        {
            icon: FaClock,
            title: t('mindkit_benefit_2_title'),
            text: t('mindkit_benefit_2_text')
        },
        {
            icon: FaProjectDiagram,
            title: t('mindkit_benefit_3_title'),
            text: t('mindkit_benefit_3_text')
        },
        {
            icon: FaUsers,
            title: t('mindkit_benefit_4_title'),
            text: t('mindkit_benefit_4_text')
        }
    ]

    const mentalModelQuotes = [
        {
            icon: FaQuoteLeft,
            author: t('mindkit_quote_1_author'),
            role: t('mindkit_quote_1_role'),
            text: t('mindkit_quote_1_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Core Features Section */}
            <Section bg="gray.50" title={t('mindkit_features_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('mindkit_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {benefits.map((benefit, index) => (
                        <Feature
                            key={index}
                            icon={benefit.icon}
                            title={benefit.title}
                            text={benefit.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Why Mental Models Section */}
            <Section bg="gray.50" title={t('mindkit_why_mental_models_title')}>
                <VStack spacing={8} align="start" width="100%" alignItems={'center'}>
                    <Text fontSize="lg" color="gray.600" maxW="3xl">
                        {t('mindkit_why_mental_models_description')}
                    </Text>
                    
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="white" rounded="xl">
                            <Heading size="md" color="blue.500">
                                {t('mindkit_mental_models_value_title')}
                            </Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i} display="flex" alignItems="center">
                                        <ListIcon as={MdCheckCircle} color="green.500" />
                                        <Text>{t(`mindkit_mental_models_value_${i}`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>

                        <VStack align="start" spacing={6} p={6} bg="white" rounded="xl">
                            {mentalModelQuotes.map((quote, index) => (
                                <VStack key={index} align="start" spacing={2} p={4} bg="gray.50" rounded="md">
                                    <Icon as={quote.icon} w={6} h={6} color="blue.500" />
                                    <Text fontStyle="italic" color="gray.600">"{quote.text}"</Text>
                                    <Text fontWeight="bold">{quote.author}</Text>
                                    <Text fontSize="sm" color="gray.500">{quote.role}</Text>
                                </VStack>
                            ))}
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Use Cases Section */}
            <Section bg="white" title={t('mindkit_use_cases_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl">
                        <Heading size="md" color="blue.500">{t('mindkit_business_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`mindkit_business_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl">
                        <Heading size="md" color="blue.500">{t('mindkit_personal_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`mindkit_personal_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindmap" />
            {/* FAQ Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`mindkit_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`mindkit_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default MindKitIntro 