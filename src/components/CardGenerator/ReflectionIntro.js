import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaChartLine, FaCompass, FaUsers, FaBalanceScale, FaMapSigns, FaRegChartBar, FaRegLightbulb } from 'react-icons/fa'
import { MdCheckCircle, MdTimeline, MdAutoGraph, MdPsychology, MdOutlinePersonalGrowth } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="purple.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const ReflectionIntro = () => {
    const { t } = useTranslation('reflection')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: MdPsychology,
            title: t('reflection_feature_1_title'),
            text: t('reflection_feature_1_text')
        },
        {
            icon: FaChartLine,
            title: t('reflection_feature_2_title'),
            text: t('reflection_feature_2_text')
        },
        {
            icon: FaBalanceScale,
            title: t('reflection_feature_3_title'),
            text: t('reflection_feature_3_text')
        },
        {
            icon: FaRegLightbulb,
            title: t('reflection_feature_4_title'),
            text: t('reflection_feature_4_text')
        }
    ]

    const useCases = [
        {
            icon: FaCompass,
            title: t('reflection_usecase_1_title'),
            text: t('reflection_usecase_1_text')
        },
        {
            icon: FaUsers,
            title: t('reflection_usecase_2_title'),
            text: t('reflection_usecase_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('reflection_usecase_3_title'),
            text: t('reflection_usecase_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('reflection_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('reflection_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="purple.500">{t('reflection_personal_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`reflection_personal_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="purple.500">{t('reflection_professional_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`reflection_professional_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <Section bg="gray.50" title={t('reflection_usecases_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {useCases.map((useCase, index) => (
                        <Feature
                            key={index}
                            icon={useCase.icon}
                            title={useCase.title}
                            text={useCase.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, purple.400, blue.400)"
                        bgClip="text"
                    >
                        {t('reflection_methodology_title')}
                    </Heading>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="purple.500">{t('reflection_framework_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`reflection_framework_step_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`reflection_framework_step_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="purple.500">{t('reflection_principles_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`reflection_principle_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`reflection_principle_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="mindmap" />

            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('reflection_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`reflection_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`reflection_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default ReflectionIntro 