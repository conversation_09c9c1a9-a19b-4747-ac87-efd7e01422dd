import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaChartLine, FaBullseye, FaRocket, FaBrain, FaUsers, FaLightbulb, FaRegChartBar, FaMapSigns } from 'react-icons/fa'
import { MdCheckCircle } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const OKRAssistantIntro = () => {
    const { t } = useTranslation('okr')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaBullseye,
            title: t('okr_feature_1_title'),
            text: t('okr_feature_1_text')
        },
        {
            icon: FaChartLine,
            title: t('okr_feature_2_title'),
            text: t('okr_feature_2_text')
        },
        {
            icon: FaRocket,
            title: t('okr_feature_3_title'),
            text: t('okr_feature_3_text')
        }
    ]

    const useCases = [
        {
            icon: FaUsers,
            title: t('okr_usecase_1_title'),
            text: t('okr_usecase_1_text')
        },
        {
            icon: FaBrain,
            title: t('okr_usecase_2_title'),
            text: t('okr_usecase_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('okr_usecase_3_title'),
            text: t('okr_usecase_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('okr_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('okr_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('okr_org_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`okr_org_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('okr_personal_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`okr_personal_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <Section bg="gray.50" title={t('okr_usecases_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {useCases.map((useCase, index) => (
                        <Feature
                            key={index}
                            icon={useCase.icon}
                            title={useCase.title}
                            text={useCase.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('okr_methodology_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('okr_methodology_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('okr_principles_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`okr_principle_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`okr_principle_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('okr_best_practices_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`okr_practice_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`okr_practice_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="mindmap" />

            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('okr_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`okr_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`okr_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default OKRAssistantIntro 