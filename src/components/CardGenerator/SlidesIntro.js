import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaRocket, FaMagic, FaMarkdown, FaCloud, FaPalette, FaMicrophone, FaShareAlt, FaLightbulb, FaWindows, FaWindowMaximize } from 'react-icons/fa'
import { MdCheckCircle, MdPresentToAll, MdAutoAwesome, MdTimer } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const SlidesIntro = () => {
    const { t } = useTranslation('slides')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const mainFeatures = [
        {
            icon: FaMagic,
            title: t('slides_feature_1_title'),
            text: t('slides_feature_1_text')
        },
        {
            icon: FaMarkdown,
            title: t('slides_feature_2_title'),
            text: t('slides_feature_2_text')
        },
        {
            icon: FaWindowMaximize,
            title: t('slides_feature_3_title'),
            text: t('slides_feature_3_text')
        },
        {
            icon: FaCloud,
            title: t('slides_feature_4_title'),
            text: t('slides_feature_4_text')
        }
    ]

    const aiCapabilities = [
        {
            icon: FaRocket,
            title: t('slides_ai_1_title'),
            text: t('slides_ai_1_text')
        },
        {
            icon: FaMicrophone,
            title: t('slides_ai_2_title'),
            text: t('slides_ai_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('slides_ai_3_title'),
            text: t('slides_ai_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50" title={t('slides_intro_title')}> 
                {/* <VStack spacing={isMobile ? 4 : 8} mb={isMobile ? 0 : 10}>
                    <Heading
                        fontSize={isMobile ? '2xl' : '3xl'}
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('slides_intro_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                    >
                        {t('slides_intro_description')}
                    </Text>
                </VStack>  */}

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {mainFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Key Benefits */}
            <Section bg="white" title={t('slides_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('slides_benefits_pro_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`slides_benefit_pro_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('slides_benefits_use_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`slides_benefit_use_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* AI Capabilities */}
            <Section bg="gray.50" title={t('slides_ai_capabilities_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {aiCapabilities.map((capability, index) => (
                        <Feature
                            key={index}
                            icon={capability.icon}
                            title={capability.title}
                            text={capability.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <HowToUse namespace="slides" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`slides_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`slides_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default SlidesIntro 