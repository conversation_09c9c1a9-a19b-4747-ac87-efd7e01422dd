import { <PERSON>L<PERSON>l, HStack, Select, Input, Flex, useBreakpointValue } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { useEffect, useMemo } from 'react';

const PersonalForm = ({ onChange, value }) => {
  const { t } = useTranslation('common');
  const isMobile = useBreakpointValue({ base: true, md: false })
  
  const Genders = useMemo(() => [{ value: 'male', label: t('male') }, { value: 'female', label: t('female') }], [t])

  return (
    <Flex width={'100%'} columnGap={4} rowGap={3} flexDirection={isMobile? 'column': 'row'}>
      <HStack width={'100%'}>
        <FormLabel whiteSpace="nowrap" m={0} marginRight={0}>{t('gender')}</FormLabel>
        <Select
          bg='white'
          variant={'ghost'}
          onChange={(e) => {
            if (!e.target.value) return;
            onChange({ ...value, gender: e.target.value })
          }}
          value={value?.gender || 'female'}
          isRequired
        >
          {Genders?.map((type) => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </Select>
      </HStack>

      <HStack width={'100%'}>
        <FormLabel whiteSpace="nowrap" m={0} marginRight={0}>{t('birthdate')}</FormLabel>
        <Input
          bg='white'
          type="date"
          variant={'ghost'}
          onChange={(e) => {
            onChange({ ...value, birthdate: e.target.value })
          }}
          value={value?.birthdate || ''}
          isRequired
        />
      </HStack>
    </Flex>
  )
}

export default PersonalForm
