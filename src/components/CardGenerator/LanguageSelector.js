import { useState, useEffect } from 'react';
import { FormLabel, HStack, Select } from '@chakra-ui/react';
import { apiUrl } from '../../utils/apiUtils';
import { useTranslation } from 'next-i18next'

const LanguageSelector = ({ onSelect, value }) => {
    const [languages, setLanguages] = useState([]);
    const { t } = useTranslation('common');

    useEffect(() => {
        const fetchLanguages = async () => {
            try {
                const response = await fetch(`${apiUrl}/info/getLanguageList`);
                if (response.ok) {
                    const data = await response.json();
                    setLanguages(data.data?.map(item => ({ value: item.label, label: item.label })));
                } else {
                    console.error('Failed to fetch language list');
                }
            } catch (error) {
                console.error('Error fetching language list:', error);
            }
        };

        fetchLanguages();
    }, []);

    return (
        <HStack width={'100%'} alignItems={'center'}>
            <FormLabel whiteSpace="nowrap" m={0} marginRight={0}>{t('output_language')}</FormLabel>
            <Select
                bg='white'
                variant={'ghost'}
                onChange={(e) => e.target.value && onSelect(e.target.value)}
                value={value}
            >
                {languages?.map((lang) => (
                    <option key={lang.value} value={lang.value}>
                        {lang.label}
                    </option>
                ))}
            </Select>
        </HStack>
    );
};

export default LanguageSelector;