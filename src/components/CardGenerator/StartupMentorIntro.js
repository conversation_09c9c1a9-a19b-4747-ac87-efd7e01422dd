import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaRocket, FaChartLine, FaLightbulb, FaUserTie, FaShieldAlt, FaUsers, FaRegCompass } from 'react-icons/fa'
import { MdCheckCircle } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="teal.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const StartupMentorIntro = () => {
    const { t } = useTranslation('startup')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const mainFeatures = [
        {
            icon: FaRocket,
            title: t('startup_feature_1_title'),
            text: t('startup_feature_1_text')
        },
        {
            icon: FaChartLine,
            title: t('startup_feature_2_title'),
            text: t('startup_feature_2_text')
        },
        {
            icon: FaShieldAlt,
            title: t('startup_feature_3_title'),
            text: t('startup_feature_3_text')
        }
    ]

    const analysisFramework = [
        {
            icon: FaLightbulb,
            title: t('startup_analysis_1_title'),
            text: t('startup_analysis_1_text')
        },
        {
            icon: FaUserTie,
            title: t('startup_analysis_2_title'),
            text: t('startup_analysis_2_text')
        },
        {
            icon: FaRegCompass,
            title: t('startup_analysis_3_title'),
            text: t('startup_analysis_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('startup_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {mainFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('startup_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="teal.500">{t('startup_founders_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`startup_founder_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="teal.500">{t('startup_business_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`startup_business_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <Section bg="gray.50" title={t('startup_analysis_framework_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {analysisFramework.map((framework, index) => (
                        <Feature
                            key={index}
                            icon={framework.icon}
                            title={framework.title}
                            text={framework.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('startup_target_users_title')}>
                <VStack spacing={6}>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('startup_target_users_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="teal.500">{t('startup_primary_users_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`startup_primary_user_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`startup_primary_user_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="teal.500">{t('startup_secondary_users_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`startup_secondary_user_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`startup_secondary_user_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="mindmap" />

            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('startup_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`startup_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`startup_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default StartupMentorIntro 