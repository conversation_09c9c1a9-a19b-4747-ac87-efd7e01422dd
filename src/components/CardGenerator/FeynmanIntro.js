import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaChalkboardTeacher, FaMapMarkedAlt, FaGraduationCap, FaUsers } from 'react-icons/fa'
import { MdCheckCircle, MdTimeline, MdSchool, MdPsychology } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const FeynmanIntro = () => {
    const { t } = useTranslation('feynman')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaBrain,
            title: t('feynman_feature_1_title'),
            text: t('feynman_feature_1_text')
        },
        {
            icon: FaChalkboardTeacher,
            title: t('feynman_feature_2_title'),
            text: t('feynman_feature_2_text')
        },
        {
            icon: FaMapMarkedAlt,
            title: t('feynman_feature_3_title'),
            text: t('feynman_feature_3_text')
        }
    ]

    const learningScenarios = [
        {
            icon: MdSchool,
            title: t('feynman_scenario_1_title'),
            text: t('feynman_scenario_1_text')
        },
        {
            icon: FaUsers,
            title: t('feynman_scenario_2_title'),
            text: t('feynman_scenario_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('feynman_scenario_3_title'),
            text: t('feynman_scenario_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('feynman_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('feynman_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('feynman_benefits_learning_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`feynman_benefit_learning_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('feynman_benefits_teaching_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`feynman_benefit_teaching_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Learning Scenarios */}
            <Section bg="gray.50" title={t('feynman_scenarios_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {learningScenarios.map((scenario, index) => (
                        <Feature
                            key={index}
                            icon={scenario.icon}
                            title={scenario.title}
                            text={scenario.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Methodology Section */}
            <Section bg="white">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('feynman_methodology_title')}
                    </Heading>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('feynman_principles_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`feynman_principle_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`feynman_principle_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('feynman_techniques_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`feynman_technique_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`feynman_technique_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('feynman_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`feynman_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`feynman_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default FeynmanIntro 