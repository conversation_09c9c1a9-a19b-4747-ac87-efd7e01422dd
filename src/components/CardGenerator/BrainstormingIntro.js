import { Box, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon, Image, Button, Flex, Table, Thead, Tbody, Tr, Th, Td, Badge, Link, Grid, GridItem } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaRocket, FaPuzzlePiece, FaUsers, FaSyncAlt, FaStar, FaStarHalfAlt, FaArrowRight, FaBolt, FaRegLightbulb, FaLayerGroup, FaBalanceScale, FaProjectDiagram, FaHourglassHalf } from 'react-icons/fa'
import { MdCheckCircle } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'
import { motion } from 'framer-motion'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
            _hover={{ shadow: "lg", transform: "translateY(-5px)" }}
            transition="all 0.3s"
        >
            <Icon as={icon} w={6} h={6} color="purple.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const InfoCard = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={5}
            bg="white"
            rounded="lg"
            shadow="sm"
            borderWidth="1px"
            borderColor="gray.200"
            spacing={3}
            height="100%"
        >
            <Flex
                w={10}
                h={10}
                align={'center'}
                justify={'center'}
                rounded={'full'}
                bg="blue.50"
            >
                <Icon as={icon} color="blue.500" w={5} h={5} />
            </Flex>
            <Text fontWeight="bold" fontSize="md">{title}</Text>
            <Text color="gray.600" fontSize="sm">{text}</Text>
        </VStack>
    )
}

const BenefitCard = ({ title, text, icon, index }) => {
    const isEven = index % 2 === 0;
    return (
        <Box
            p={5}
            bg={isEven ? "purple.50" : "blue.50"}
            rounded="lg"
            borderWidth="1px"
            borderColor={isEven ? "purple.100" : "blue.100"}
            position="relative"
            overflow="hidden"
            _hover={{ shadow: "md" }}
            transition="all 0.3s"
        >
            <Flex align="center" mb={3}>
                <Icon as={icon} w={5} h={5} color={isEven ? "purple.500" : "blue.500"} mr={3} />
                <Text fontWeight="bold" fontSize="md">{title}</Text>
            </Flex>
            <Text color="gray.700" fontSize="sm">{text}</Text>
        </Box>
    )
}

const UserTestimonial = ({ name, role, company, quote, rating }) => {
    return (
        <VStack
            align={"start"}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Flex>
                {[...Array(5)].map((_, i) => (
                    <Icon
                        key={i}
                        as={i < Math.floor(rating) ? FaStar : (i < Math.ceil(rating) ? FaStarHalfAlt : FaStar)}
                        color={i < Math.ceil(rating) ? "yellow.400" : "gray.200"}
                        w={4}
                        h={4}
                    />
                ))}
            </Flex>
            <Text fontSize="md" fontStyle="italic" color="gray.600">"{quote}"</Text>
            <Flex width="100%" justify="space-between" align="center">
                <Box>
                    <Text fontWeight="bold">{name}</Text>
                    <Text fontSize="sm" color="gray.500">{role}, {company}</Text>
                </Box>
            </Flex>
        </VStack>
    )
}

const ThinkingMethod = ({ title, description, benefits, iconComponent }) => {
    return (
        <Box
            p={6}
            borderWidth="1px"
            borderRadius="lg"
            overflow="hidden"
            bg="white"
            shadow="md"
            _hover={{ shadow: "lg" }}
            transition="all 0.3s"
        >
            <Flex align="center" mb={4}>
                <Icon as={iconComponent} w={6} h={6} color="purple.500" mr={3} />
                <Heading size="md">{title}</Heading>
            </Flex>
            <Text mb={4}>{description}</Text>
            <Text fontWeight="medium" mb={2}>Benefits:</Text>
            <List spacing={2}>
                {benefits.map((benefit, index) => (
                    <ListItem key={index} display="flex" alignItems="center">
                        <ListIcon as={MdCheckCircle} color="green.500" />
                        <Text>{benefit}</Text>
                    </ListItem>
                ))}
            </List>
        </Box>
    )
}

const CaseStudy = ({ title, challenge, solution, results, industry }) => {
    return (
        <Box
            p={6}
            borderWidth="1px"
            borderRadius="lg"
            overflow="hidden"
            bg="white"
            shadow="md"
        >
            <Badge colorScheme="blue" mb={2}>{industry}</Badge>
            <Heading size="md" mb={4}>{title}</Heading>

            <Text fontWeight="medium" color="gray.700" mb={1}>Challenge:</Text>
            <Text mb={4} color="gray.600">{challenge}</Text>

            <Text fontWeight="medium" color="gray.700" mb={1}>Solution:</Text>
            <Text mb={4} color="gray.600">{solution}</Text>

            <Text fontWeight="medium" color="gray.700" mb={1}>Results:</Text>
            <Text color="gray.600">{results}</Text>
        </Box>
    )
}

const Showcase = ({ title, description, imageSrc, altText }) => {
    return (
        <Box
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            position="relative"
            overflow="hidden"
        >
            {/* <Badge colorScheme="purple" position="absolute" top={4} right={4}>
                {industry}
            </Badge> */}
            <VStack align="start" spacing={4}>
                <Heading size="md" color="blue.600">{title}</Heading>
                <Text color="gray.700">{description}</Text>
                <Box width="100%" position="relative" rounded="md" overflow="hidden">
                    <Image
                        src={imageSrc}
                        alt={altText}
                        width="100%"
                        height="auto"
                        objectFit='contain'
                    />
                </Box>
            </VStack>
        </Box>
    )
}

const ComparisonItem = ({ feature, traditional, funblocks }) => {
    return (
        <Tr>
            <Td fontWeight="medium" borderBottomColor="gray.300">{feature}</Td>
            <Td borderBottomColor="gray.300">{traditional}</Td>
            <Td color="blue.600" fontWeight="medium" borderBottomColor="gray.300">{funblocks}</Td>
        </Tr>
    )
}

const FeatureComparison = () => {
    const { t } = useTranslation('brainstorm')

    return (
        <Box overflowX="auto" width="100%">
            <Table variant="simple" colorScheme="gray">
                <Thead bg="gray.100">
                    <Tr>
                        <Th borderBottomColor="gray.200">{t('brainstorm_comparison_feature', 'Feature')}</Th>
                        <Th borderBottomColor="gray.200">{t('brainstorm_comparison_traditional', 'Traditional Brainstorming')}</Th>
                        <Th borderBottomColor="gray.200">{t('brainstorm_comparison_funblocks', 'FunBlocks AI Brainstorming')}</Th>
                    </Tr>
                </Thead>
                <Tbody>
                    <ComparisonItem
                        feature={t('brainstorm_comparison_idea', 'Idea Generation')}
                        traditional={t('brainstorm_comparison_idea_trad', 'Limited by team knowledge and creativity')}
                        funblocks={t('brainstorm_comparison_idea_fun', 'AI-powered with access to vast knowledge base')}
                    />
                    <ComparisonItem
                        feature={t('brainstorm_comparison_models', 'Thinking Models')}
                        traditional={t('brainstorm_comparison_models_trad', 'Requires facilitation expertise')}
                        funblocks={t('brainstorm_comparison_models_fun', 'Built-in classic thinking frameworks')}
                    />
                    <ComparisonItem
                        feature={t('brainstorm_comparison_visual', 'Visualization')}
                        traditional={t('brainstorm_comparison_visual_trad', 'Manual creation')}
                        funblocks={t('brainstorm_comparison_visual_fun', 'Automatic mind map generation')}
                    />
                    <ComparisonItem
                        feature={t('brainstorm_comparison_avail', 'Availability')}
                        traditional={t('brainstorm_comparison_avail_trad', 'Requires scheduling and coordination')}
                        funblocks={t('brainstorm_comparison_avail_fun', 'Available 24/7, instant access')}
                    />
                    <ComparisonItem
                        feature={t('brainstorm_comparison_depth', 'Exploration Depth')}
                        traditional={t('brainstorm_comparison_depth_trad', 'Often surface-level due to time constraints')}
                        funblocks={t('brainstorm_comparison_depth_fun', 'Unlimited depth exploration of sub-topics')}
                    />
                </Tbody>
            </Table>
        </Box>
    )
}



const BrainstormingIntro = () => {
    const { t } = useTranslation('brainstorm')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const itemVariants = {
        hidden: { y: 15, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: isMobile ? 0.3 : 0.5
            }
        }
    };

    const whatIsBrainstorming = [
        {
            icon: FaBolt,
            title: t('brainstorm_what_is_point_1_title', 'Idea Generation'),
            text: t('brainstorm_what_is_point_1_text', 'The process of producing numerous ideas without immediate evaluation, focusing on quantity over quality initially.')
        },
        {
            icon: FaUsers,
            title: t('brainstorm_what_is_point_2_title', 'Creative Collaboration'),
            text: t('brainstorm_what_is_point_2_text', 'Bringing together diverse perspectives to build upon each other\'s ideas and create synergistic solutions.')
        },
        {
            icon: FaPuzzlePiece,
            title: t('brainstorm_what_is_point_3_title', 'Problem Solving'),
            text: t('brainstorm_what_is_point_3_text', 'Breaking down complex challenges into manageable components and exploring multiple solution paths.')
        },
        {
            icon: FaRegLightbulb,
            title: t('brainstorm_what_is_point_4_title', 'Innovation Catalyst'),
            text: t('brainstorm_what_is_point_4_text', 'Serving as a springboard for breakthrough thinking by encouraging unconventional connections and perspectives.')
        }
    ]

    const aiBenefits = [
        {
            icon: FaLayerGroup,
            title: t('brainstorm_benefits_point_1_title', 'Knowledge Expansion'),
            text: t('brainstorm_benefits_point_1_text', 'Access a vast knowledge base that extends far beyond individual expertise, introducing diverse perspectives and information.')
        },
        {
            icon: FaBrain,
            title: t('brainstorm_benefits_point_2_title', 'Structured Thinking'),
            text: t('brainstorm_benefits_point_2_text', 'Leverage proven thinking frameworks automatically applied to your topics, ensuring comprehensive exploration.')
        },
        {
            icon: FaBalanceScale,
            title: t('brainstorm_benefits_point_3_title', 'Bias Reduction'),
            text: t('brainstorm_benefits_point_3_text', 'Overcome human cognitive biases with AI-generated ideas that aren\'t limited by conventional thinking patterns.')
        },
        {
            icon: FaProjectDiagram,
            title: t('brainstorm_benefits_point_4_title', 'Visual Organization'),
            text: t('brainstorm_benefits_point_4_text', 'Transform complex ideas into clear, interactive mind maps that enhance understanding and facilitate further development.')
        },
        {
            icon: FaHourglassHalf,
            title: t('brainstorm_benefits_point_5_title', 'Time Efficiency'),
            text: t('brainstorm_benefits_point_5_text', 'Generate and organize dozens of ideas in minutes rather than hours, accelerating your creative and problem-solving processes.')
        },
        {
            icon: FaRocket,
            title: t('brainstorm_benefits_point_6_title', 'End-to-End Solution'),
            text: t('brainstorm_benefits_point_6_text', 'Complete workflow from brainstorming and exploring ideas to generating final deliverables like slides, documents, and infographics—all in one place.')
        }
    ]

    const aiFeatures = [
        {
            icon: FaBrain,
            title: t('brainstorm_feature_1_title', 'AI驱动的创意生成'),
            text: t('brainstorm_feature_1_text', '利用先进的LLM技术，从多个角度激发创新想法，突破思维局限')
        },
        {
            icon: FaLightbulb,
            title: t('brainstorm_feature_2_title', '经典思维模型'),
            text: t('brainstorm_feature_2_text', '集成人类文明积累的经典思维模型，让AI具备超强创意生成能力')
        },
        {
            icon: FaSyncAlt,
            title: t('brainstorm_feature_3_title', '递进式探索'),
            text: t('brainstorm_feature_3_text', '支持对任意子主题持续深入探索，让创意源源不断')
        }
    ]

    const thinkingMethods = [
        {
            title: t('brainstorm_six_hats_title', 'Six Thinking Hats'),
            description: t('brainstorm_six_hats_description', "Edward de Bono's method using six different perspectives (white, red, black, yellow, green, blue) to analyze problems from different angles."),
            benefits: [
                t('brainstorm_six_hats_benefit_1', 'Encourages parallel thinking'),
                t('brainstorm_six_hats_benefit_2', 'Separates ego from performance'),
                t('brainstorm_six_hats_benefit_3', 'Reduces conflict')
            ],
            iconComponent: FaLightbulb
        },
        {
            title: t('brainstorm_scamper_title', 'SCAMPER Technique'),
            description: t('brainstorm_scamper_description', 'An idea generation method that stands for Substitute, Combine, Adapt, Modify/Magnify, Put to other uses, Eliminate, and Reverse/Rearrange.'),
            benefits: [
                t('brainstorm_scamper_benefit_1', 'Systematically explores improvements'),
                t('brainstorm_scamper_benefit_2', 'Works for products, services, or ideas'),
                t('brainstorm_scamper_benefit_3', 'Simple to understand and apply')
            ],
            iconComponent: FaRocket
        },
        {
            title: t('brainstorm_mindmap_title', 'Mind Mapping'),
            description: t('brainstorm_mindmap_description', 'A visual thinking tool that connects ideas around a central concept, creating branches of related thoughts.'),
            benefits: [
                t('brainstorm_mindmap_benefit_1', 'Visual organization of ideas'),
                t('brainstorm_mindmap_benefit_2', 'Enhances memory and recall'),
                t('brainstorm_mindmap_benefit_3', 'Stimulates creativity')
            ],
            iconComponent: FaBrain
        },
        {
            title: t('brainstorm_first_principles_title', 'First Principles Thinking'),
            description: t('brainstorm_first_principles_description', 'Breaking down complex problems into basic elements and reassembling them from the ground up.'),
            benefits: [
                t('brainstorm_first_principles_benefit_1', 'Eliminates assumptions'),
                t('brainstorm_first_principles_benefit_2', 'Creates innovative solutions'),
                t('brainstorm_first_principles_benefit_3', 'Builds deep understanding')
            ],
            iconComponent: FaPuzzlePiece
        }
    ]

    const caseStudies = [
        {
            title: t('brainstorm_case_marketing_title', 'Marketing Campaign Innovation'),
            challenge: t('brainstorm_case_marketing_challenge', 'A marketing team struggling to develop fresh campaign ideas after three years of similar approaches.'),
            solution: t('brainstorm_case_marketing_solution', 'Used FunBlocks AI Brainstorming with SCAMPER technique to transform existing campaign concepts.'),
            results: t('brainstorm_case_marketing_results', 'Generated 27 unique campaign ideas, with 3 selected for implementation resulting in 42% higher engagement than previous campaigns.'),
            industry: t('brainstorm_case_marketing_industry', 'Marketing')
        },
        {
            title: t('brainstorm_case_product_title', 'Product Feature Prioritization'),
            challenge: t('brainstorm_case_product_challenge', 'Software startup unable to decide which features to prioritize for their next release.'),
            solution: t('brainstorm_case_product_solution', 'Applied Six Thinking Hats method through FunBlocks AI to evaluate feature options from multiple perspectives.'),
            results: t('brainstorm_case_product_results', 'Reached consensus on top 5 features within one session, avoiding weeks of debate and accelerating development cycle by 40%.'),
            industry: t('brainstorm_case_product_industry', 'Software Development')
        },
        {
            title: t('brainstorm_case_education_title', 'Educational Curriculum Redesign'),
            challenge: t('brainstorm_case_education_challenge', 'University department needed to modernize curriculum but faculty had divergent views on approach.'),
            solution: t('brainstorm_case_education_solution', 'Used mind mapping functionality to explore different curriculum paths and integrate diverse requirements.'),
            results: t('brainstorm_case_education_results', 'Created comprehensive curriculum map that satisfied all stakeholders and increased student enrollment by 25% the following semester.'),
            industry: t('brainstorm_case_education_industry', 'Education')
        }
    ]

    const testimonials = [
        {
            name: t('brainstorm_testimonial_1_name', 'Sarah Chen'),
            role: t('brainstorm_testimonial_1_role', 'Product Manager'),
            company: t('brainstorm_testimonial_1_company', 'TechInnovate'),
            quote: t('brainstorm_testimonial_1_quote', 'FunBlocks AI Brainstorming transformed our product development process. We now generate twice the ideas in half the time.'),
            rating: 5
        },
        {
            name: t('brainstorm_testimonial_2_name', 'Marcus Johnson'),
            role: t('brainstorm_testimonial_2_role', 'Creative Director'),
            company: t('brainstorm_testimonial_2_company', 'DesignForward'),
            quote: t('brainstorm_testimonial_2_quote', 'The Six Thinking Hats implementation is brilliant. It\'s like having Edward de Bono in the room guiding our creative process.'),
            rating: 4.5
        },
        {
            name: t('brainstorm_testimonial_3_name', 'Elena Rodriguez'),
            role: t('brainstorm_testimonial_3_role', 'Entrepreneur'),
            company: t('brainstorm_testimonial_3_company', 'StartupLabs'),
            quote: t('brainstorm_testimonial_3_quote', 'As a solo founder, this tool has become my virtual brainstorming team. The quality of ideas and mind maps it generates is exceptional.'),
            rating: 5
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section with Screenshot */}
            <Section bg="gray.50">
                {/* Product Hunt Badge */}
                <Box
                    as={motion.div}
                    variants={itemVariants}
                    display="flex"
                    justifyContent="center"
                    width="100%"
                    mb={4}
                    gap={4}
                    flexWrap={{ base: "nowrap", sm: "wrap" }}
                    alignItems="center"
                >
                    <a href="https://www.producthunt.com/posts/funblocks-ai-brainstorming?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_source=badge-funblocks&#0045;ai&#0045;brainstorming" target="_blank">
                        <img
                            src="https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=963998&theme=light&period=daily&t=1747700452719"
                            alt="FunBlocks&#0032;AI&#0032;Brainstorming - AI&#0045;Powered&#0032;Brainstorming&#0032;to&#0032;Ignite&#0032;Unlimited&#0032;Creativity | Product Hunt"
                            style={{ width: "250px", height: "54px" }}
                            width="250"
                            height="54" />
                    </a>
                    <a href="https://www.producthunt.com/posts/funblocks-ai-brainstorming?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_source=badge-funblocks&#0045;ai&#0045;brainstorming" target="_blank">
                        <img
                            src="https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=963998&theme=light&period=weekly&topic_id=204&t=1747700452719"
                            alt="FunBlocks&#0032;AI&#0032;Brainstorming - AI&#0045;Powered&#0032;Brainstorming&#0032;to&#0032;Ignite&#0032;Unlimited&#0032;Creativity | Product Hunt"
                            style={{ width: "250px", height: "54px" }}
                            width="250"
                            height="54"
                        />
                    </a>
                </Box>


                <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={8} width="100%">
                    <GridItem>
                        <VStack align="start" spacing={6}>
                            <Heading size="xl" bgGradient="linear(to-r, blue.400, purple.500)" bgClip="text">
                                {t('brainstorm_hero_title', 'AI Brainstorm & Mindmap: Supercharge Your Creative Process')}
                            </Heading>
                            <Text fontSize="lg" color="gray.700">
                                {t('brainstorm_hero_description', 'FunBlocks AI Brainstorming combines the power of artificial intelligence with proven thinking methodologies to help you generate innovative ideas and overcome creative blocks.')}
                            </Text>
                            <Text fontSize="md" fontWeight="bold" color="purple.600" mt={2}>
                                Advanced AI Ideation • Interactive AI Mindmaps • Creative AI Thinking
                            </Text>
                            <List spacing={3}>
                                {aiFeatures.map((feature, idx) => (
                                    <ListItem key={idx} display="flex" alignItems="center">
                                        <ListIcon as={feature.icon} color="purple.500" />
                                        <Text fontWeight="medium">{feature.title}</Text>
                                    </ListItem>
                                ))}
                            </List>
                            <Link href="#how-it-works">
                                <Button rightIcon={<FaArrowRight />} colorScheme="blue" size="lg" mt={4}>
                                    {t('brainstorm_hero_button', 'See How It Works')}
                                </Button>
                            </Link>
                        </VStack>
                    </GridItem>
                    <GridItem display="flex" justifyContent="center" alignItems="center">
                        <Image
                            backgroundColor={'white'}
                            padding={2}
                            src="https://www.funblocks.net/img/portfolio/thumbnails/aiflow_productivity.png"
                            alt={t('brainstorm_hero_image_alt', 'FunBlocks AI Brainstorming Interface')}
                            borderRadius="lg"
                            shadow="xl"
                            fallbackSrc="https://via.placeholder.com/500x300?text=Brainstorming+Tool+Screenshot"
                        />
                    </GridItem>
                </Grid>
            </Section>

            {/* What is Brainstorming Section */}
            <Section bg="white" title={t('brainstorm_what_is_title', 'What is Brainstorming?')}>
                <VStack spacing={8} width="100%">
                    <Text fontSize="lg" textAlign="center" maxW="3xl" mb={4} color="gray.700">
                        {t('brainstorm_what_is_description', 'Brainstorming is a creative problem-solving technique designed to generate a wide range of ideas through collaborative or individual thinking. It encourages free thinking without judgment, allowing innovative solutions to emerge.')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} width="100%">
                        {whatIsBrainstorming.map((item, index) => (
                            <InfoCard
                                key={index}
                                icon={item.icon}
                                title={item.title}
                                text={item.text}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Benefits of AI Brainstorming Section */}
            <Section bg="gray.50" title={t('brainstorm_benefits_title', 'Benefits of FunBlocks AI Brainstorming')}>
                <VStack spacing={8} width="100%">
                    <Text fontSize="lg" textAlign="center" maxW="3xl" mb={4} color="gray.700">
                        {t('brainstorm_benefits_description', 'Our AI-powered brainstorming tool transforms the traditional ideation process with these powerful advantages:')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} width="100%">
                        {aiBenefits.map((benefit, index) => (
                            <BenefitCard
                                key={index}
                                icon={benefit.icon}
                                title={benefit.title}
                                text={benefit.text}
                                index={index}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Key Features Section */}
            <Section bg="white" title={t('brainstorm_features_title', 'AI-Powered Features')}>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={10}
                >
                    {t('brainstorm_features_description', 'Leverage the power of artificial intelligence to enhance your creative thinking with these innovative features.')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {aiFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Tool Comparison Section */}
            <Section bg="gray.50" title={t('brainstorm_why_choose_title', 'Why Choose FunBlocks AI Brainstorming?')}>
                <Text fontSize="lg" textAlign="center" maxW="3xl" mb={8} color="gray.600">
                    {t('brainstorm_why_choose_description', 'See how our AI-powered brainstorming tool compares to traditional methods:')}
                </Text>

                <FeatureComparison />

                <Flex justifyContent="center" mt={8}>
                    <Button
                        size="lg"
                        colorScheme="purple"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('brainstorm_try_free_button', 'Try It Free')}
                    </Button>
                </Flex>
            </Section>

            {/* Thinking Methods Section */}
            <Section bg="white" title={t('brainstorm_thinking_models_title', 'Classic Mental Models Integrated')} id="thinking-models">
                <Text fontSize="lg" textAlign="center" maxW="3xl" mb={8} color="gray.600">
                    {t('brainstorm_thinking_models_description', 'Our AI is trained to apply these proven thinking methodologies to help you generate better ideas:')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} mb={8}>
                    {thinkingMethods.map((method, index) => (
                        <ThinkingMethod
                            key={index}
                            title={method.title}
                            description={method.description}
                            benefits={method.benefits}
                            iconComponent={method.iconComponent}
                        />
                    ))}
                </SimpleGrid>

                <Link
                    fontSize='large'
                    fontWeight='bold'
                    color='blue.400'
                    href='https://www.funblocks.net/thinking-matters/category/classic-mental-models'
                    target='_blank'
                >
                    {t('brainstorm_more_mental_models', 'More Mental Models')}
                </Link>
            </Section>

            {/* Screenshot Showcase */}
            <Section bg="gray.50" title={t('brainstorm_see_action', 'See It In Action')} id="how-it-works">
                <VStack spacing={8} width="100%">
                    <Text fontSize="lg" textAlign="center" maxW="3xl" mb={4} color="gray.600">
                        {t('brainstorm_see_action_desc', 'FunBlocks AI Brainstorming creates beautiful, interactive mind maps from your initial ideas:')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <Showcase
                            title={t('brainstorm_see_action_case_1_title')}
                            description={t('brainstorm_see_action_case_1_description')}
                            imageSrc="https://www.funblocks.net/img/portfolio/thumbnails/aitools_brainstorming_product.png"
                            altText={t('brainstorm_product_image_alt', 'Example of product innovation mind map')}
                        />
                        <Showcase
                            title={t('brainstorm_see_action_case_2_title')}
                            description={t('brainstorm_see_action_case_2_description')}
                            imageSrc="https://www.funblocks.net/img/portfolio/thumbnails/aitools_brainstorming_marketing.png"
                            altText={t('brainstorm_marketing_image_alt', 'Example of marketing strategy mind map')}
                        />
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* How To Use Section */}
            <HowToUse />

            {/* Case Studies Section */}
            <Section bg="gray.50" title={t('brainstorm_success_stories_title', 'Success Stories')} id="case-studies">
                <Text fontSize="lg" textAlign="center" maxW="3xl" mb={8} color="gray.600">
                    {t('brainstorm_success_stories_description', 'See how organizations and individuals have transformed their creative process:')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {caseStudies.map((caseStudy, index) => (
                        <CaseStudy
                            key={index}
                            title={caseStudy.title}
                            challenge={caseStudy.challenge}
                            solution={caseStudy.solution}
                            results={caseStudy.results}
                            industry={caseStudy.industry}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Testimonials Section */}
            <Section bg="white" title={t('brainstorm_testimonials_title', 'What Our Users Say')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {testimonials.map((testimonial, index) => (
                        <UserTestimonial
                            key={index}
                            name={testimonial.name}
                            role={testimonial.role}
                            company={testimonial.company}
                            quote={testimonial.quote}
                            rating={testimonial.rating}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Use Cases */}
            <Section bg="gray.50" title={t('brainstorm_use_cases_title', '应用场景')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md">
                        <Heading size="md" color="blue.500">{t('brainstorm_business_title', '商业应用')}</Heading>
                        <List spacing={3}>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_business_1', '产品创新与开发')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_business_2', '市场营销策略')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_business_3', '问题解决方案')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_business_4', '业务流程优化')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_business_5', '战略规划')}</Text>
                            </ListItem>
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md">
                        <Heading size="md" color="blue.500">{t('brainstorm_personal_title', '个人应用')}</Heading>
                        <List spacing={3}>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_personal_1', '学习规划')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_personal_2', '写作创作')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_personal_3', '生活决策')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_personal_4', '职业发展规划')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t('brainstorm_personal_5', '创意项目构思')}</Text>
                            </ListItem>
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center" id="faq">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`brainstorm_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`brainstorm_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>

            {/* CTA Section */}
            <Section bg="blue.50">
                <VStack spacing={6} py={10}>
                    <Heading textAlign="center" size="xl">{t('brainstorm_cta_title', 'Ready to Transform Your Creative Process?')}</Heading>
                    <Text fontSize="lg" textAlign="center" maxW="2xl">
                        {t('brainstorm_cta_description', 'Start generating innovative ideas and mind maps with FunBlocks AI Brainstorming today.')}
                    </Text>
                    <Button
                        size="lg"
                        colorScheme="blue"
                        height="60px"
                        px={10}
                        fontSize="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('brainstorm_cta_button', 'Get Started For Free')}
                    </Button>
                    <Text fontSize="sm" color="gray.600">{t('brainstorm_cta_no_card', 'No credit card required')}</Text>
                </VStack>
            </Section>
        </VStack>
    )
}

export default BrainstormingIntro