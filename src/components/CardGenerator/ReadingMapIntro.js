import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaBookReader, FaChartLine, FaGraduationCap, FaUsers, FaLightbulb } from 'react-icons/fa'
import { MdCheckCircle, MdSettings, MdSchool, MdTimeline } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const ReadingMapIntro = () => {
    const { t } = useTranslation('reading')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaBrain,
            title: t('reading_feature_1_title'),
            text: t('reading_feature_1_text')
        },
        {
            icon: FaChartLine,
            title: t('reading_feature_2_title'),
            text: t('reading_feature_2_text')
        },
        {
            icon: FaBookReader,
            title: t('reading_feature_3_title'),
            text: t('reading_feature_3_text')
        }
    ]

    const applicationScenarios = [
        {
            icon: MdSchool,
            title: t('reading_scenario_1_title'),
            text: t('reading_scenario_1_text')
        },
        {
            icon: FaUsers,
            title: t('reading_scenario_2_title'),
            text: t('reading_scenario_2_text')
        },
        {
            icon: MdSettings,
            title: t('reading_scenario_3_title'),
            text: t('reading_scenario_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('reading_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('reading_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('reading_benefits_personal_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`reading_benefit_personal_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('reading_benefits_professional_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`reading_benefit_professional_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Application Scenarios */}
            <Section bg="gray.50" title={t('reading_scenario_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {applicationScenarios.map((scenario, index) => (
                        <Feature
                            key={index}
                            icon={scenario.icon}
                            title={scenario.title}
                            text={scenario.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Technical Advantages */}
            <Section bg="white">
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('reading_tech_advantages_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i}>
                                    <Text fontWeight="bold">{t(`reading_tech_advantage_${i}_title`)}</Text>
                                    <Text color="gray.600">{t(`reading_tech_advantage_${i}_text`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('reading_unique_features_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i}>
                                    <Text fontWeight="bold">{t(`reading_unique_feature_${i}_title`)}</Text>
                                    <Text color="gray.600">{t(`reading_unique_feature_${i}_text`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('reading_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`reading_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`reading_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default ReadingMapIntro 