import React, { useState, useEffect, useRef, useMemo, useCallback, memo } from 'react'
import { FormControl, FormLabel, Textarea, Input, Box, Tag, Text, Button, HStack, Image, Spinner, useBreakpointValue, Modal, ModalOverlay, ModalContent, ModalHeader, ModalBody, ModalCloseButton, VStack } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { isValidURL } from '../../utils/urlUtils'
import { apiUrl } from '../../utils/apiUtils'
import { getProxiedImageUrl } from '../../utils/imageUtils'
import { useAuth } from '../../contexts/AuthContext'
import { APP_TYPE } from '../../utils/constants'

// 添加最大长度限制
const MAX_LENGTHS = {
  makeGraph: 4000,
  graphChat: 1000,
  cardGuru: 400
}

const InputForm = ({ cardType, onInputChange, onLoginRequired, onConfirm, value, imageUrl, mode, app, focus }) => {
  const { t } = useTranslation('common')
  const [isValidUrl, setIsValidUrl] = useState(false)
  const [inputLength, setInputLength] = useState(0)
  const inputRef = useRef(null)
  const textareaRef = useRef(null)
  const fileInputRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);
  const { isLoggedIn } = useAuth();
  const [url, setUrl] = useState('');
  const [imageLoadError, setImageLoadError] = useState();
  const isMobile = useBreakpointValue({ base: true, md: false })

  const maxLength = MAX_LENGTHS[mode] || Infinity

  const acceptLink = useMemo(() => ['makeGraph', 'video', 'link'].includes(mode), [mode])

  useEffect(() => {
    const trimmedValue = value?.trim() || '';
    setIsValidUrl(acceptLink && isValidURL(trimmedValue))
    setInputLength(trimmedValue.length)
  }, [value, acceptLink])

  useEffect(() => {
    setTimeout(() => {
      if (acceptLink && isValidUrl) {
        focusAndMoveCursorToEnd(inputRef.current)
      } else {
        focusAndMoveCursorToEnd(textareaRef.current)
      }
    }, 100)
  }, [isValidUrl, acceptLink])

  const handleChange = (e) => {
    const newValue = e.target.value.slice(0, maxLength)
    let inputValue = { message: newValue }
    if (acceptLink && isValidURL(inputValue.message.trim())) {
      inputValue.url = inputValue.message.trim()
    }
    onInputChange(inputValue)
  }

  const focusAndMoveCursorToEnd = (element) => {
    if (element) {
      element.focus()
      const length = element.value.length
      element.setSelectionRange(length, length)
    }
  }

  const showLengthWarning = inputLength > maxLength * 0.8

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      try {
        setIsLoading(true);
        
        // Check file size and compress if necessary
        const maxSize = 1 * 1024 * 1024; // 1 MB
        let compressedFile = file;

        if (file.size > maxSize) {
          const imageBitmap = await createImageBitmap(file);
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const scaleFactor = Math.sqrt(maxSize / file.size);
          canvas.width = imageBitmap.width * scaleFactor;
          canvas.height = imageBitmap.height * scaleFactor;
          ctx.drawImage(imageBitmap, 0, 0, canvas.width, canvas.height);
          compressedFile = await new Promise((resolve) => {
            canvas.toBlob((blob) => {
              resolve(blob);
            }, 'image/jpeg', 0.7); // Adjust quality as needed
          });
        }

        const formData = new FormData();
        formData.append('files', compressedFile);

        const response = await fetch(`${apiUrl}/media/upload`, {
          method: 'POST',
          credentials: 'include',
          body: formData,
        });

        if (!response.ok) {
          throw new Error('Upload failed');
        }

        const data = await response.json();

        if (!data?.data?.length) {
          throw new Error('Upload failed');
        }
        const fileUrl = data.data[0].uri;
        onInputChange({
          imageUrl: fileUrl
        });
      } catch (error) {
        console.error('Error uploading image:', error);
        alert(t('upload_failed'));
      } finally {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    !!imageUrl && imageUrl.startsWith(apiUrl) && !imageUrl.includes('/imgproxy') && setUrl(imageUrl)
  }, [imageUrl])

  useEffect(() => {
    setTimeout(() => !!focus && focusAndMoveCursorToEnd(textareaRef?.current), 200)
  }, [focus])

  const handleUrlChange = (e) => {
    const url = e.target.value;
    setUrl(url);
    onInputChange({ imageUrl: getProxiedImageUrl(url, apiUrl) });
  };

  const inputRows = useMemo(() => (mode === 'others' || [APP_TYPE.onePageSlides].includes(app)) && 6 || mode === 'topic' && 1 || [APP_TYPE.mindmap, APP_TYPE.art, APP_TYPE.reading, APP_TYPE.movie].includes(app) && 1 || mode === 'makeGraph' && 6 || 3, [app, mode])

  const onKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      !!onConfirm && onConfirm(event);
    }
  }

  if (['imageInsights', 'image'].includes(mode)) {
    return (
      <Box width="100%" pt={1}>
        {
          app === APP_TYPE.graphics &&
          <Text mb={4} color={'gray.600'} textAlign={'left'}>{t('wonderlens_tip')}</Text>
        }
        <input
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          ref={fileInputRef}
          style={{ display: 'none' }}
        />
        <HStack width="100%" spacing={2} mb={4}>
          <Button
            colorScheme='teal'
            onClick={() => {
              if (!isLoggedIn) {
                onLoginRequired();
                return;
              }
              fileInputRef.current.click();
            }}
            width="20%"
            minWidth="120px"
            maxWidth="200px"
            isDisabled={isLoading}
          >
            {isLoading ? <Spinner size="sm" /> : t('choose_image')}
          </Button>

          <Text>{t('or')}</Text>

          <Input
            placeholder={t('paste_image_url')}
            value={url}
            onChange={handleUrlChange}
            isDisabled={isLoading}
            backgroundColor={'white'}
          />
        </HStack>

        {imageUrl && (
          <Box mt={4} maxWidth="500px">
            <Text mb={2} fontWeight="medium">{t('uploaded_image')}</Text>
            <Image
              src={imageUrl}
              alt="Preview"
              borderRadius="md"
              fallback={<Box p={4} textAlign="center">{t('loading')}</Box>}
              onError={() => setImageLoadError(true)}
              onLoad={() => setImageLoadError(false)}
            />
            {imageLoadError && (
              <Box p={4} textAlign="center">{t('image_load_error')}</Box>
            )}
          </Box>
        )}

      </Box>
    );
  }

  return (
    <FormControl>
      {
        !isMobile && cardType?.desc && <Text textAlign={'left'} color={'gray.600'} mb={2}>{t(cardType.desc)}</Text>
      }
      {/* <FormLabel>{t(mode === 'book' && 'book_name' || mode === 'movie' && 'movie_name' || mode === 'video' && 'video_url' || mode === 'link' && 'webpage_url' || mode === 'makeGraph' && 'provide_text_or_webpage' || 'your_input')}</FormLabel> */}
      <Box position="relative">
        <Box
          className="gradient-border-textarea-bg"
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          pointerEvents="none"
        />
        {['video', 'link'].includes(mode) || mode === 'makeGraph' && isValidUrl ? (
          <Box display="flex" alignItems="center">
            <Tag colorScheme='blue' zIndex={1000} ml={1} whiteSpace={'nowrap'} p={2} paddingRight={3} width={'-webkit-fit-content'}>Content@</Tag>
            <Input
              ref={inputRef}
              value={value}
              onChange={handleChange}
              placeholder={t('enter_webpage_url')}
              border="none"
              _focus={{ boxShadow: 'none' }}
              zIndex="1"
              position="relative"
              maxLength={maxLength}
              onKeyPress={onKeyPress}
            />
          </Box>
        ) : (
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={handleChange}
            placeholder={t(app === APP_TYPE.startupmentor && 'enter_your_ideas' || (mode === 'others' || [APP_TYPE.onePageSlides, APP_TYPE.infographic].includes(app)) && 'given_text' || mode === 'book' && 'book_name' || mode === 'movie' && 'movie_name' || mode === 'video' && 'video_url' || mode === 'link' && 'webpage_url' || mode === 'makeGraph' && 'provide_text_or_webpage' || mode === 'topic' && 'topic' || app === APP_TYPE.refineQuestion && 'your_question' || [APP_TYPE.graphics, APP_TYPE.mindsnap].includes(app) && 'enter_your_topic' || 'your_input')}
            border="none"
            rows={inputRows}
            _focus={{ boxShadow: 'none' }}
            zIndex="1"
            position="relative"
            maxLength={maxLength}
            onKeyPress={(event) => { inputRows === 1 && onKeyPress(event) }}
          />
        )}
      </Box>
      {showLengthWarning && (
        <Box mt={1} display="flex" alignItems="center" justifyContent="flex-end" width="100%">
          <Text fontSize="sm" color="orange.500">
            {t('input_length_warning', { current: inputLength, max: maxLength })}
          </Text>
        </Box>
      )}
    </FormControl>
  )
}

export default InputForm
