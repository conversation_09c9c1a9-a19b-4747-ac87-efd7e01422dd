import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon, Image, Button, Flex, Badge, HStack, Link } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaShareAlt, FaMagic, FaBook, FaYoutube, FaGlobe, FaUsers, FaHandPointer, FaGraduationCap, FaChartLine, FaBriefcase, FaLaptopCode, FaUserCheck, FaStar, FaArrowRight, FaUniversity, FaSchool, FaBuilding } from 'react-icons/fa'
import { MdCheckCircle, MdCompare, MdPsychology, MdOutlineDesignServices } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'
import Testimonials from '../common/Testimonials'
import CaseStudies from '../common/CaseStudies'
import ComparisonTable from '../common/ComparisonTable'
import ResearchBacked from '../common/ResearchBacked'
import NextLink from 'next/link'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="dodgerblue" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const CaseStudy = ({ title, industry, description, imageSrc, altText }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })

    return (
        <Box
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            position="relative"
            overflow="hidden"
        >
            <Badge colorScheme="purple" position="absolute" top={4} right={4}>
                {industry}
            </Badge>
            <VStack align="start" spacing={4}>
                <Heading size="md" color="blue.600">{title}</Heading>
                <Text color="gray.700">{description}</Text>
                <Box width="100%" position="relative" rounded="md" overflow="hidden">
                    <Image
                        src={imageSrc}
                        alt={altText}
                        width="100%"
                        height="auto"
                        objectFit='contain'
                    />
                </Box>
            </VStack>
        </Box>
    )
}

const ComparisonItem = ({ title, before, after }) => {
    return (
        <Box p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
            <VStack align="start" spacing={4} width="100%">
                <Heading size="md" color="purple.600">{title}</Heading>
                <Flex width="100%" direction={{ base: 'column', md: 'row' }} gap={4}>
                    <VStack flex={1} align="start" bg="red.50" p={4} rounded="md">
                        <Text fontWeight="bold" color="red.600">Before FunBlocks AI</Text>
                        <Text>{before}</Text>
                    </VStack>
                    <Icon as={FaArrowRight} display={{ base: 'none', md: 'block' }} alignSelf="center" boxSize={6} color="gray.400" />
                    <VStack flex={1} align="start" bg="green.50" p={4} rounded="md">
                        <Text fontWeight="bold" color="green.600">With FunBlocks AI</Text>
                        <Text>{after}</Text>
                    </VStack>
                </Flex>
            </VStack>
        </Box>
    )
}

const Testimonial = ({ quote, author, position, company, rating }) => {
    return (
        <VStack
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
            align="start"
        >
            <HStack spacing={1} color="yellow.400">
                {Array(5).fill(0).map((_, i) => (
                    <Icon key={i} as={FaStar} w={4} h={4} color={i < rating ? "yellow.400" : "gray.200"} />
                ))}
            </HStack>
            <Text fontStyle="italic" color="gray.600">"{quote}"</Text>
            <VStack spacing={0} align="start">
                <Text fontWeight="bold">{author}</Text>
                <Text fontSize="sm" color="gray.500">{position}, {company}</Text>
            </VStack>
        </VStack>
    )
}

const MindmapIntro = () => {
    const { t } = useTranslation('mindmap')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const aiFeatures = [
        {
            icon: FaHandPointer,
            title: t('mindmap_feature_1_title'),
            text: t('mindmap_feature_1_text')
        },
        {
            icon: FaLightbulb,
            title: t('mindmap_feature_2_title'),
            text: t('mindmap_feature_2_text')
        },
        {
            icon: FaBrain,
            title: t('mindmap_feature_3_title'),
            text: t('mindmap_feature_3_text')
        }
    ]

    const supportedTypes = [
        {
            icon: FaBook,
            title: t('content_type_1_title'),
            text: t('content_type_1_text')
        },
        {
            icon: FaYoutube,
            title: t('content_type_2_title'),
            text: t('content_type_2_text')
        },
        {
            icon: FaGlobe,
            title: t('content_type_3_title'),
            text: t('content_type_3_text')
        }
    ]

    const useCases = [
        {
            icon: FaGraduationCap,
            title: t('use_case_1_title'),
            text: t('use_case_1_text')
        },
        {
            icon: FaChartLine,
            title: t('use_case_2_title'),
            text: t('use_case_2_text')
        },
        {
            icon: FaBriefcase,
            title: t('use_case_3_title'),
            text: t('use_case_3_text')
        },
        {
            icon: FaLaptopCode,
            title: t('use_case_4_title'),
            text: t('use_case_4_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* 1. Enhanced Hero Section */}
            <Section bg="gray.50">
                <VStack spacing={isMobile ? 4 : 8} mb={6}>
                    <Heading
                        fontSize={isMobile ? '2xl' : '3xl'}
                        textAlign="center"
                        bgGradient="linear(to-l, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('mindmap_intro_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                    >
                        {t('mindmap_intro_enhanced_description')}
                    </Text>
                </VStack>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {aiFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Main Hero CTA Section */}
            <Section bg="blue.50">
                <VStack spacing={6} align="center" maxW="800px" mx="auto" py={4}>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        rounded="full"
                        px={8}
                        fontSize="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('try_mindmap_now')} <Icon as={FaArrowRight} ml={2} />
                    </Button>
                    <Text textAlign="center" fontWeight="medium">{t('free_trial_offer')}</Text>
                </VStack>
            </Section>

            {/* 2. Mind Mapping Essence */}
            <Section bg="white" title={t('why_mindmap_description_title')}>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={isMobile ? 4 : 8}
                >
                    {t('why_mindmap_description')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="purple.500">{t('mindmap_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`mindmap_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="purple.500">{t('mindmap_uses_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`mindmap_use_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* NEW SECTION: Use Cases */}
            <Section bg="gray.50" title={t('popular_use_cases')}>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={isMobile ? 4 : 8}
                >
                    {t('use_cases_description')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8}>
                    {useCases.map((useCase, index) => (
                        <Feature
                            key={index}
                            icon={useCase.icon}
                            title={useCase.title}
                            text={useCase.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* NEW SECTION: Before/After Comparison */}
            <Section bg="white" title={t('mindmap_transformation')}>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={isMobile ? 4 : 8}
                >
                    {t('comparison_description')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 1 }} spacing={8} width="100%">
                    <ComparisonItem
                        title={t('comparison_time_title')}
                        before={t('comparison_time_before')}
                        after={t('comparison_time_after')}
                    />
                    <ComparisonItem
                        title={t('comparison_quality_title')}
                        before={t('comparison_quality_before')}
                        after={t('comparison_quality_after')}
                    />
                    <ComparisonItem
                        title={t('comparison_insights_title')}
                        before={t('comparison_insights_before')}
                        after={t('comparison_insights_after')}
                    />
                </SimpleGrid>
            </Section>

            {/* NEW SECTION: Case Studies */}
            <Section bg="blue.50" title={t('success_stories')}>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={isMobile ? 4 : 8}
                >
                    {t('case_studies_description')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <CaseStudy
                        title={t('case_study_1_title')}
                        industry={t('case_study_1_industry')}
                        description={t('case_study_1_description')}
                        imageSrc="https://www.funblocks.net/img/portfolio/fullsize/aitools_mindmap_book.png"
                        altText={t('case_study_1_alt')}
                    />
                    <CaseStudy
                        title={t('case_study_2_title')}
                        industry={t('case_study_2_industry')}
                        description={t('case_study_2_description')}
                        imageSrc="https://www.funblocks.net/img/portfolio/fullsize/aitools_mindmap_marketing.png"
                        altText={t('case_study_2_alt')}
                    />
                </SimpleGrid>
            </Section>

            {/* New Section: Why Not Use ChatGPT/Claude */}
            <Section bg="gray.50" title={t('why_not_chatgpt_title')}>
                <VStack spacing={6} align="start" width="100%" maxW="3xl" mx="auto">
                    <Box p={6} bg="orange.50" rounded="xl" width="100%">
                        <VStack align="start" spacing={4}>
                            <Heading size="md" color="orange.600">
                                {t('common_mistakes_title')}
                            </Heading>
                            <Text color="gray.700">
                                {t('chatgpt_mindmap_problems')}
                            </Text>
                        </VStack>
                    </Box>

                    <Box p={6} bg="green.50" rounded="xl" width="100%">
                        <VStack align="start" spacing={4}>
                            <Heading size="md" color="green.600">
                                {t('funblocks_advantages_title')}
                            </Heading>
                            <List spacing={3}>
                                {[1, 2, 3].map((i) => (
                                    <ListItem key={i} display="flex" alignItems="center">
                                        <ListIcon as={MdCheckCircle} color="green.500" />
                                        <Text>{t(`funblocks_advantage_${i}`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </Box>
                </VStack>
            </Section>

            {/* 3. How It Works */}
            <Section bg="white" title={t('how_it_works_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {supportedTypes.map((type, index) => (
                        <Feature
                            key={index}
                            icon={type.icon}
                            title={type.title}
                            text={type.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Comparison Table */}
            <ComparisonTable
                title={t("comparison_title", "FunBlocks AI Mindmap vs. Traditional Methods")}
                description={t("comparison_description", "See how our AI-powered approach compares to traditional mind mapping methods in terms of efficiency, expertise required, and results.")}
                bg="blue.50"
                productName="FunBlocks AI Mindmap"
                columns={[
                    { key: 'funblocks', label: t('comparison_funblocks', 'FunBlocks AI Mindmap'), highlight: true },
                    { key: 'traditional', label: t('comparison_traditional', 'Traditional Mind Maps') },
                    { key: 'other_ai', label: t('comparison_other_ai', 'Text-Only AI Tools') },
                    { key: 'templates', label: t('comparison_templates', 'Template Services') }
                ]}
                features={[
                    {
                        name: t('comparison_feature_1', 'One-Click Generation'),
                        tooltip: t('comparison_feature_1_tooltip', 'Create complete mind maps with a single input'),
                        funblocks: true,
                        traditional: false,
                        other_ai: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_2', 'AI-Powered Content Analysis'),
                        tooltip: t('comparison_feature_2_tooltip', 'Automatically extracts key concepts and relationships'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_3', 'Visual Knowledge Mapping'),
                        tooltip: t('comparison_feature_3_tooltip', 'Information is presented in visual, connected format'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_4', 'No Design Skills Required'),
                        tooltip: t('comparison_feature_4_tooltip', 'Create professional mind maps without design expertise'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_5', 'Multiple Content Sources'),
                        tooltip: t('comparison_feature_5_tooltip', 'Create mind maps from books, videos, websites, and more'),
                        funblocks: true,
                        traditional: false,
                        other_ai: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_6', 'Hierarchical Organization'),
                        tooltip: t('comparison_feature_6_tooltip', 'Content is organized in logical hierarchical structure'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_7', 'Cross-Concept Connections'),
                        tooltip: t('comparison_feature_7_tooltip', 'Shows relationships between different concepts'),
                        funblocks: true,
                        traditional: true,
                        other_ai: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_8', 'Creation Speed'),
                        tooltip: t('comparison_feature_8_tooltip', 'Time required to create a comprehensive mind map'),
                        funblocks: true,
                        traditional: false,
                        other_ai: true,
                        templates: true
                    }
                ]}
                id="comparison"
            />

            {/* Testimonials Section */}
            <Testimonials
                appname="FunBlocks AI Mindmap"
                rating="4.8"
                users="10,000+"
                testimonials={[
                    {
                        content: t('testimonial_1_quote'),
                        author: t('testimonial_1_author'),
                        role: t('testimonial_1_position'),
                        organization: t('testimonial_1_company'),
                        rating: 5
                    },
                    {
                        content: t('testimonial_2_quote'),
                        author: t('testimonial_2_author'),
                        role: t('testimonial_2_position'),
                        organization: t('testimonial_2_company'),
                        rating: 5
                    },
                    {
                        content: t('testimonial_3_quote'),
                        author: t('testimonial_3_author'),
                        role: t('testimonial_3_position'),
                        organization: t('testimonial_3_company'),
                        rating: 4.5
                    }
                ]}
                bg="white"
                id="testimonials"
            />

            {/* Research-Backed Section */}
            <ResearchBacked
                title={t("research_title", "Research-Backed Mind Mapping")}
                description={t("research_description", "FunBlocks AI Mindmap is built on solid cognitive science and educational research principles.")}
                bg="gray.50"
                id="research"
                productName="FunBlocks AI Mindmap"
                researchAreas={[
                    {
                        title: t('research_area_1_title', 'Dual Coding Theory'),
                        description: t('research_area_1_description', 'Research shows that combining visual and verbal information enhances learning and retention. FunBlocks AI Mindmap leverages this principle by presenting complex ideas in a visually engaging format.'),
                        icon: FaBrain,
                        color: 'blue',
                        citations: [
                            {
                                text: 'Paivio, A. (1991). Dual coding theory: Retrospect and current status. Canadian Journal of Psychology, 45(3), 255-287.',
                                url: 'https://doi.org/10.1037/h0084295'
                            },
                            {
                                text: 'Clark, J. M., & Paivio, A. (1991). Dual coding theory and education. Educational Psychology Review, 3(3), 149-210.',
                                url: 'https://doi.org/10.1007/BF01320076'
                            }
                        ]
                    },
                    {
                        title: t('research_area_2_title', 'Cognitive Load Theory'),
                        description: t('research_area_2_description', 'Mind maps reduce cognitive load by organizing information in a way that aligns with how our brains naturally process information, making complex topics easier to understand and remember.'),
                        icon: MdPsychology,
                        color: 'purple',
                        citations: [
                            {
                                text: 'Sweller, J. (2011). Cognitive load theory. Psychology of Learning and Motivation, 55, 37-76.',
                                url: 'https://doi.org/10.1016/B978-0-12-387691-1.00002-8'
                            },
                            {
                                text: 'Paas, F., & Ayres, P. (2014). Cognitive load theory: A broader view on the role of memory in learning and education. Educational Psychology Review, 26(2), 191-195.',
                                url: 'https://doi.org/10.1007/s10648-014-9263-5'
                            }
                        ]
                    },
                    {
                        title: t('research_area_3_title', 'Visual Learning'),
                        description: t('research_area_3_description', 'Studies show that visual formats can improve comprehension of complex information by up to 400%. FunBlocks AI Mindmap transforms abstract concepts into visual representations that are easier to understand and remember.'),
                        icon: MdOutlineDesignServices,
                        color: 'green',
                        citations: [
                            {
                                text: 'Mayer, R. E. (2020). Multimedia learning. Cambridge University Press.',
                                url: 'https://www.cambridge.org/highereducation/books/multimedia-learning/FB7E79A165D24D47CEACEB4D2C426ECD#overview'
                            },
                            {
                                text: 'Medina, J. (2008). Brain rules: 12 principles for surviving and thriving at work, home, and school. Pear Press.',
                                url: 'https://brainrules.net/'
                            }
                        ]
                    },
                    {
                        title: t('research_area_4_title', 'Knowledge Organization and Schema Theory'),
                        description: t('research_area_4_description', 'Schema theory explains how our brains organize knowledge into interconnected networks. Mind maps mirror this natural cognitive structure, making information easier to integrate with existing knowledge and retrieve later.'),
                        icon: FaUniversity,
                        color: 'orange',
                        citations: [
                            {
                                text: 'Nesbit, J. C., & Adesope, O. O. (2006). Learning with concept and knowledge maps: A meta-analysis. Review of Educational Research, 76(3), 413-448.',
                                url: 'https://doi.org/10.3102/00346543076003413'
                            },
                            {
                                text: 'Novak, J. D., & Cañas, A. J. (2008). The theory underlying concept maps and how to construct and use them. Technical Report IHMC CmapTools 2006-01 Rev 01-2008.',
                                url: 'https://cmap.ihmc.us/docs/theory-of-concept-maps'
                            },
                            {
                                text: 'Rumelhart, D. E. (2017). Schemata: The building blocks of cognition. In R. J. Spiro et al. (Eds.), Theoretical Issues in Reading Comprehension (pp. 33-58). Routledge.',
                                url: 'https://doi.org/10.4324/9781315107493'
                            }
                        ]
                    }
                ]}
            />

            {/* Related Resources Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('related_resources', 'Related Resources')}
                </Heading>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} maxW="6xl" mx="auto">
                    <Box
                        p={5}
                        shadow="md"
                        borderWidth="1px"
                        borderRadius="lg"
                        bg="blue.50"
                        transition="transform 0.3s"
                        _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
                    >
                        <VStack align="start" spacing={3}>
                            <Icon as={FaBook} w={6} h={6} color="blue.500" />
                            <Heading size="md">{t('related_resource_1_title', 'Classic Book Mind Maps')}</Heading>
                            <Text color="gray.600">{t('related_resource_1_desc', 'Explore mind maps of classic literature and non-fiction books.')}</Text>
                            <Link href="https://www.funblocks.net/aitools/collections/Reading" color="blue.500" isExternal>
                                {t('learn_more', 'Learn More')} <Icon as={FaArrowRight} ml={1} w={3} h={3} />
                            </Link>
                        </VStack>
                    </Box>

                    <Box
                        p={5}
                        shadow="md"
                        borderWidth="1px"
                        borderRadius="lg"
                        bg="purple.50"
                        transition="transform 0.3s"
                        _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
                    >
                        <VStack align="start" spacing={3}>
                            <Icon as={FaLightbulb} w={6} h={6} color="purple.500" />
                            <Heading size="md">{t('related_resource_2_title', 'Mental Models Library')}</Heading>
                            <Text color="gray.600">{t('related_resource_2_desc', 'Discover powerful mental models to enhance your thinking.')}</Text>
                            <Link href="https://www.funblocks.net/thinking-matters/category/classic-mental-models" color="purple.500" isExternal>
                                {t('learn_more', 'Learn More')} <Icon as={FaArrowRight} ml={1} w={3} h={3} />
                            </Link>
                        </VStack>
                    </Box>

                    <Box
                        p={5}
                        shadow="md"
                        borderWidth="1px"
                        borderRadius="lg"
                        bg="green.50"
                        transition="transform 0.3s"
                        _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
                    >
                        <VStack align="start" spacing={3}>
                            <Icon as={FaGraduationCap} w={6} h={6} color="green.500" />
                            <Heading size="md">{t('related_resource_3_title', 'Educational Mind Maps')}</Heading>
                            <Text color="gray.600">{t('related_resource_3_desc', 'Mind maps designed specifically for educational purposes.')}</Text>
                            <Link href="https://www.funblocks.net/aitools/layered-explanation" color="green.500" isExternal>
                                {t('learn_more', 'Learn More')} <Icon as={FaArrowRight} ml={1} w={3} h={3} />
                            </Link>
                        </VStack>
                    </Box>

                    <Box
                        p={5}
                        shadow="md"
                        borderWidth="1px"
                        borderRadius="lg"
                        bg="orange.50"
                        transition="transform 0.3s"
                        _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
                    >
                        <VStack align="start" spacing={3}>
                            <Icon as={FaBriefcase} w={6} h={6} color="orange.500" />
                            <Heading size="md">{t('related_resource_4_title', 'Business Mind Maps')}</Heading>
                            <Text color="gray.600">{t('related_resource_4_desc', 'Mind maps for business planning and strategy.')}</Text>
                            <Link href="https://www.funblocks.net/aitools/businessmodel" color="orange.500" isExternal>
                                {t('learn_more', 'Learn More')} <Icon as={FaArrowRight} ml={1} w={3} h={3} />
                            </Link>
                        </VStack>
                    </Box>
                </SimpleGrid>
            </Section>

            {/* New Section: How to Use */}
            <HowToUse namespace="mindmap" />

            {/* 4. Updated FAQ */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`mindmap_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`mindmap_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>

            {/* Final CTA Section */}
            <Section bg="blue.50">
                <VStack spacing={6} align="center" maxW="800px" mx="auto" py={6}>
                    <Heading size="lg" textAlign="center" color="blue.600">
                        {t('ready_to_transform')}
                    </Heading>
                    <Text textAlign="center" fontSize="lg">{t('final_cta_description')}</Text>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        rounded="full"
                        px={10}
                        fontSize="xl"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('start_creating_now')} <Icon as={FaArrowRight} ml={2} />
                    </Button>
                    <HStack spacing={4} pt={2}>
                        <Icon as={FaUserCheck} color="green.500" />
                        <Text fontSize="sm" color="gray.600">{t('free_trial_message')}</Text>
                    </HStack>
                </VStack>
            </Section>
        </VStack>
    )
}

export default MindmapIntro