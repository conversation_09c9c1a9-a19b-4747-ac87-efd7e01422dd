import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaHeart, FaBrain, FaChartLine, FaShieldAlt, FaUserMd, FaComments, FaLightbulb, FaHandHoldingHeart } from 'react-icons/fa'
import { MdCheckCircle, MdPsychology, MdSupportAgent, MdMindfulness } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="teal.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const CounselorIntro = () => {
    const { t } = useTranslation('counselor')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaHeart,
            title: t('counselor_feature_1_title'),
            text: t('counselor_feature_1_text')
        },
        {
            icon: FaBrain,
            title: t('counselor_feature_2_title'),
            text: t('counselor_feature_2_text')
        },
        {
            icon: FaChartLine,
            title: t('counselor_feature_3_title'),
            text: t('counselor_feature_3_text')
        },
        {
            icon: FaShieldAlt,
            title: t('counselor_feature_4_title'),
            text: t('counselor_feature_4_text')
        }
    ]

    const approaches = [
        {
            icon: MdPsychology,
            title: t('counselor_approach_1_title'),
            text: t('counselor_approach_1_text')
        },
        {
            icon: FaHandHoldingHeart,
            title: t('counselor_approach_2_title'),
            text: t('counselor_approach_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('counselor_approach_3_title'),
            text: t('counselor_approach_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('counselor_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('counselor_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="teal.500">{t('counselor_personal_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`counselor_personal_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="teal.500">{t('counselor_professional_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`counselor_professional_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <Section bg="gray.50" title={t('counselor_approaches_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {approaches.map((approach, index) => (
                        <Feature
                            key={index}
                            icon={approach.icon}
                            title={approach.title}
                            text={approach.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('counselor_framework_title')}>
                <VStack spacing={6}>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('counselor_framework_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="teal.500">{t('counselor_methods_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`counselor_method_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`counselor_method_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="teal.500">{t('counselor_principles_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`counselor_principle_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`counselor_principle_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="mindmap" />

            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('counselor_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`counselor_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`counselor_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default CounselorIntro 