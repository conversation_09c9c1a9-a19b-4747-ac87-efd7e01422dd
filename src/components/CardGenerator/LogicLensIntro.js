import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaSearch, FaBalanceScale, FaChartLine, FaGraduationCap, FaLightbulb } from 'react-icons/fa'
import { MdCheckCircle, MdTimeline, MdPsychology, MdAutoGraph, MdArticle, MdBusinessCenter, MdNewspaper, MdChat } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const ApplicationCard = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="purple.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const LogicLensIntro = () => {
    const { t } = useTranslation('logic')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaBrain,
            title: t('logic_feature_1_title'),
            text: t('logic_feature_1_text')
        },
        {
            icon: FaSearch,
            title: t('logic_feature_2_title'),
            text: t('logic_feature_2_text')
        },
        {
            icon: FaBalanceScale,
            title: t('logic_feature_3_title'),
            text: t('logic_feature_3_text')
        },
        {
            icon: MdAutoGraph,
            title: t('logic_feature_4_title'),
            text: t('logic_feature_4_text')
        }
    ]

    const userScenarios = [
        {
            icon: FaGraduationCap,
            title: t('logic_scenario_1_title'),
            text: t('logic_scenario_1_text')
        },
        {
            icon: MdPsychology,
            title: t('logic_scenario_2_title'),
            text: t('logic_scenario_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('logic_scenario_3_title'),
            text: t('logic_scenario_3_text')
        }
    ]

    const applications = [
        {
            icon: MdArticle,
            title: t('logic_application_1_title'),
            text: t('logic_application_1_text')
        },
        {
            icon: MdBusinessCenter,
            title: t('logic_application_2_title'),
            text: t('logic_application_2_text')
        },
        {
            icon: MdNewspaper,
            title: t('logic_application_3_title'),
            text: t('logic_application_3_text')
        },
        {
            icon: MdChat,
            title: t('logic_application_4_title'),
            text: t('logic_application_4_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('logic_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Applications Section */}
            <Section bg="white" title={t('logic_applications_title')}>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('logic_applications_description')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {applications.map((app, index) => (
                        <ApplicationCard
                            key={index}
                            icon={app.icon}
                            title={app.title}
                            text={app.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('logic_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('logic_benefits_individual_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`logic_benefit_individual_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('logic_benefits_professional_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`logic_benefit_professional_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Use Cases */}
            <Section bg="gray.50" title={t('logic_scenarios_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {userScenarios.map((scenario, index) => (
                        <Feature
                            key={index}
                            icon={scenario.icon}
                            title={scenario.title}
                            text={scenario.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('logic_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`logic_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`logic_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default LogicLensIntro 