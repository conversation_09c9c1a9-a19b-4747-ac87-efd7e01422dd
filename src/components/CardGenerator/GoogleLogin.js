import React from 'react';
import { useGoogleLogin, useGoogleOneTapLogin, GoogleOAuthProvider } from '@react-oauth/google';
import { Button, useToast } from '@chakra-ui/react';
import { apiUrl } from '../../utils/apiUtils';
import { useAuth } from '../../contexts/AuthContext';
import { FaGoogle } from 'react-icons/fa';
import { useTranslation } from 'next-i18next';

const GoogleLoginComponent = ({ inviteCode, showButton }) => {
  const { t } = useTranslation('common');
  const { logon } = useAuth();
  const toast = useToast()
  const login = useGoogleLogin({
    onSuccess: async tokenResponse => {
      try {
        const response = await fetch(`${apiUrl}/users/oauth-sign-in`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ tokenInfo: tokenResponse, inviteCode }),
          credentials: 'include',
        })

        if (response.ok) {
          const result = await response.json()

          console.log(result, result?.data)
          logon(result?.data)

          toast({
            title: t('login_success'),
            status: "success",
            duration: 3000,
            isClosable: true,
          })
        } else {
          console.error('Failed to login')
          toast({
            title: t('login_failed'),
            status: "error",
            duration: 3000,
            isClosable: true,
          })
        }
      } catch (error) {
        console.error('Error login:', error)
        toast({
          title: t('login_error'),
          description: error.message,
          status: "error",
          duration: 3000,
          isClosable: true,
        })
      }
    },
  });

  useGoogleOneTapLogin({
    onSuccess: async credentialResponse => {
      try {
        const response = await fetch(`${apiUrl}/users/oauth-sign-in-credential`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ credential: credentialResponse.credential }),
          credentials: 'include',
        })

        if (response.ok) {
          const result = await response.json()

          console.log(result, result?.data)
          logon(result?.data)

          toast({
            title: t('login_success'),
            status: "success",
            duration: 3000,
            isClosable: true,
          })
        } else {
          console.error('Failed to login')
          toast({
            title: t('login_failed'),
            status: "error",
            duration: 3000,
            isClosable: true,
          })
        }
      } catch (error) {
        console.error('Error login:', error)
        toast({
          title: t('login_error'),
          description: error.message,
          status: "error",
          duration: 3000,
          isClosable: true,
        })
      }
    },
    onError: () => {
      console.log('Login Failed');
    },
  });

  let loginButton = showButton ? <Button
    width="100%"
    onClick={login}
    justifyContent={'flex-start'}
    spacing={4}
  >
    <FaGoogle style={{ padding: 4, marginRight: 6 }} size={32} color='dodgerblue'/> {t('login_with_google')}
  </Button> : <></>;

  return loginButton;
};

export const GoogleLogin = ({ inviteCode, showButton }) => {
  return <GoogleOAuthProvider
    clientId={'988058218123-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com'}
  >
    <GoogleLoginComponent
      inviteCode={inviteCode}
      showButton={showButton}
    />
  </GoogleOAuthProvider>
};
