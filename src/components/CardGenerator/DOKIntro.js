import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaGraduationCap, FaChalkboardTeacher, FaUserGraduate, FaUsers, FaLightbulb, FaChartLine, FaLayerGroup, FaMapMarkedAlt, FaFileAlt } from 'react-icons/fa'
import { MdCheckCircle, MdSchool, MdTimeline, MdAutoGraph, MdPsychology, MdOutlineAssignment, MdOutlineStairs, MdSettings, MdLayers } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const DOKIntro = () => {
    const { t } = useTranslation('dok')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaBrain,
            title: t('dok_feature_1_title'),
            text: t('dok_feature_1_text')
        },
        {
            icon: FaMapMarkedAlt,
            title: t('dok_feature_2_title'),
            text: t('dok_feature_2_text')
        },
        {
            icon: MdLayers,
            title: t('dok_feature_3_title'),
            text: t('dok_feature_3_text')
        }
    ]

    const targetAudiences = [
        {
            icon: FaChalkboardTeacher,
            title: t('dok_audience_1_title'),
            text: t('dok_audience_1_text')
        },
        {
            icon: MdOutlineAssignment,
            title: t('dok_audience_2_title'),
            text: t('dok_audience_2_text')
        },
        {
            icon: FaUserGraduate,
            title: t('dok_audience_3_title'),
            text: t('dok_audience_3_text')
        },
        {
            icon: FaUsers,
            title: t('dok_audience_4_title'),
            text: t('dok_audience_4_text')
        },
        {
            icon: FaFileAlt,
            title: t('dok_audience_5_title'),
            text: t('dok_audience_5_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('dok_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('dok_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('dok_benefits_edu_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`dok_benefit_edu_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('dok_benefits_learner_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`dok_benefit_learner_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Target Audience Section */}
            <Section bg="gray.50" title={t('dok_audience_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {targetAudiences.map((audience, index) => (
                        <Feature
                            key={index}
                            icon={audience.icon}
                            title={audience.title}
                            text={audience.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Webb's DOK Framework */}
            <Section bg="white">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('dok_framework_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('dok_framework_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('dok_cognitive_levels_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`dok_level_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`dok_level_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('dok_advantages_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4, 5].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`dok_advantage_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`dok_advantage_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Use Cases Section */}
            <Section bg="gray.50" title={t('dok_usecases_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('dok_usecases_edu_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3].map((i) => (
                                <ListItem key={i}>
                                    <Text fontWeight="bold">{t(`dok_usecase_edu_${i}_title`)}</Text>
                                    <Text color="gray.600">{t(`dok_usecase_edu_${i}_text`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('dok_usecases_business_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3].map((i) => (
                                <ListItem key={i}>
                                    <Text fontWeight="bold">{t(`dok_usecase_business_${i}_title`)}</Text>
                                    <Text color="gray.600">{t(`dok_usecase_business_${i}_text`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('dok_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`dok_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`dok_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default DOKIntro 