import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaChartLine, FaMoon, FaBook, FaUserMd, FaGlobe, FaUsers } from 'react-icons/fa'
import { MdCheckCircle } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="purple.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const DreamLensIntro = () => {
    const { t } = useTranslation('dreamlens')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const analysisFeatures = [
        {
            icon: FaBrain,
            title: t('dreamlens_feature_1_title'),
            text: t('dreamlens_feature_1_text')
        },
        {
            icon: FaLightbulb,
            title: t('dreamlens_feature_2_title'),
            text: t('dreamlens_feature_2_text')
        },
        {
            icon: FaChartLine,
            title: t('dreamlens_feature_3_title'),
            text: t('dreamlens_feature_3_text')
        }
    ]

    const perspectives = [
        {
            icon: FaUserMd,
            title: t('dream_perspective_1_title'),
            text: t('dream_perspective_1_text')
        },
        {
            icon: FaMoon,
            title: t('dream_perspective_2_title'),
            text: t('dream_perspective_2_text')
        },
        {
            icon: FaGlobe,
            title: t('dream_perspective_3_title'),
            text: t('dream_perspective_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('dreamlens_intro_description')}
                </Text>
                {/* </VStack>  */}

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {analysisFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('dreamlens_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="purple.500">{t('personal_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`personal_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="purple.500">{t('professional_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`professional_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Analysis Perspectives */}
            <Section bg="gray.50" title={t('analysis_perspectives_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {perspectives.map((perspective, index) => (
                        <Feature
                            key={index}
                            icon={perspective.icon}
                            title={perspective.title}
                            text={perspective.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* New Scientific Framework Section */}
            <Section bg="white" title={t('dream_scientific_title')}>
                <VStack spacing={6}>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('dream_scientific_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="purple.500">{t('dream_psychology_title')}</Heading>
                            <List spacing={3}>
                                <ListItem>
                                    <Text fontWeight="bold">{t('dream_freud_title')}</Text>
                                    <Text color="gray.600">{t('dream_freud_text')}</Text>
                                </ListItem>
                                <ListItem>
                                    <Text fontWeight="bold">{t('dream_jung_title')}</Text>
                                    <Text color="gray.600">{t('dream_jung_text')}</Text>
                                </ListItem>
                                <ListItem>
                                    <Text fontWeight="bold">{t('dream_gestalt_title')}</Text>
                                    <Text color="gray.600">{t('dream_gestalt_text')}</Text>
                                </ListItem>
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="purple.500">{t('dream_modern_title')}</Heading>
                            <List spacing={3}>
                                <ListItem>
                                    <Text fontWeight="bold">{t('dream_cognitive_title')}</Text>
                                    <Text color="gray.600">{t('dream_cognitive_text')}</Text>
                                </ListItem>
                                <ListItem>
                                    <Text fontWeight="bold">{t('dream_integration_title')}</Text>
                                    <Text color="gray.600">{t('dream_integration_text')}</Text>
                                </ListItem>
                                <ListItem>
                                    <Text fontWeight="bold">{t('dream_cultural_title')}</Text>
                                    <Text color="gray.600">{t('dream_cultural_text')}</Text>
                                </ListItem>
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('dreamlens_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`dreamlens_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`dreamlens_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default DreamLensIntro 