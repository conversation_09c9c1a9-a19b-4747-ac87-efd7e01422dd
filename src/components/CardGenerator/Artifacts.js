import { useState, useEffect, useRef, useMemo } from 'react';
import { Box, Button, Flex, IconButton, Link, VStack, useToast } from '@chakra-ui/react';
import { useInView } from 'react-intersection-observer';
import styled from '@emotion/styled';
import { apiUrl } from '../../utils/apiUtils';
import ArtifactCard from './ArtifactCard';
import { APP_TYPE } from '../../utils/constants';
import getConfig from 'next/config';
// 修改 WaterfallGrid 组件以使用 CSS columns
const WaterfallGrid = styled(Box)`
  column-count: 2;
  column-gap: 16px;
  
  @media (max-width: 768px) {
    column-count: 1;
  }
`;

// 修改 CardWrapper 以确保卡片不会被分割到多列
const CardWrapper = styled(Box)`
  break-inside: avoid;
  margin-bottom: 16px;
`;

// 添加一个新的 styled 组件用于展示列表
const ListContainer = styled(VStack)`
  width: 100%;
  align-items: stretch;
  gap: 36px;
`;

const ShowcaseContainer = styled(Box)`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`;

const ShowcaseItem = styled(Box)`
  padding: 5px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f7fafc;
    transform: translateY(-1px);
  }
`;

const Artifacts = ({ app = '', mode, collection, initial_items = [] }) => {
    const { basePath } = getConfig().publicRuntimeConfig;
    const [cards, setCards] = useState(initial_items);
    const [page, setPage] = useState(!!initial_items?.length ? 1 : 0);
    const pageSize = app === APP_TYPE.graphics && 10 || collection === 'collection' && 500 || !app && 10 || 20;
    const [hasMore, setHasMore] = useState(!!initial_items?.length ? (initial_items.length === pageSize) : true);
    const [ref, inView] = useInView({
        threshold: 0,
    });


    const fetchCards = async () => {
        try {
            const service = app === APP_TYPE.graphics && 'aiinsights' || '';
            const response = await fetch(`${apiUrl}/ai/${collection === 'collection' ? 'showcase' : collection}_artifacts?app=${app}&mode=${mode || ''}&service=${service}&pageNum=${page}&pageSize=${pageSize}`, {
                credentials: 'include'
            });
            const data = await response.json();

            if (data?.data?.length) {
                setCards(prevCards => [...prevCards, ...(data?.data || [])]);
                setPage(prevPage => prevPage + 1);
            }

            setHasMore(data?.data?.length == pageSize);
        } catch (error) {
            console.error('Error fetching cards:', error);
        }
    };

    const handleDelete = (cardId) => {
        setCards(prevCards => prevCards.filter(card => card._id !== cardId));
    };

    useEffect(() => {
        if (inView && hasMore && (app === APP_TYPE.graphics || page === 0)) {
            fetchCards();
        }
    }, [inView]);

    return (
        <Box width="100%" maxWidth="900px">
            {
                app === APP_TYPE.graphics ? (
                    <WaterfallGrid>
                        {cards.map((card) => (
                            <CardWrapper key={card._id}>
                                <ArtifactCard
                                    data={card}
                                    app={app}
                                    onDelete={handleDelete}
                                    showDeleteButton={collection === 'my'}
                                />
                            </CardWrapper>
                        ))}
                    </WaterfallGrid>
                ) : (
                    collection === 'my' ? (
                        <ListContainer>
                            {cards.map((card) => {
                                return (
                                    <ArtifactCard
                                        key={card._id}
                                        data={card}
                                        app={card.app}
                                        mode={card.mode}
                                        onDelete={handleDelete}
                                        showDeleteButton={true}
                                    />
                                )
                            })}
                        </ListContainer>
                    ) : (
                        <ShowcaseContainer>
                            {cards.map((card) => (
                                <ShowcaseItem
                                    key={card._id}
                                >
                                    <a href={`${basePath}/share/${card.app}/${card._id}/${card.title?.replaceAll(' ', '-')}`} target='_blank'>
                                        {card.title || '未命名作品'}
                                    </a>
                                </ShowcaseItem>
                            ))}
                        </ShowcaseContainer>
                    )
                )
            }

            {
            // hasMore && (
                <VStack ref={ref} pt={4} pb={4}>
                    {
                        ![collection].includes('showcase') &&
                        <Button onClick={fetchCards} isLoading={inView && (app === APP_TYPE.graphics || page === 0)}>
                            Load More
                        </Button>
                    }
                    {
                        [collection].includes('showcase') &&
                        <Link color={'dodgerblue'} href={`${basePath}/collections/${app}`} isExternal>More</Link>
                    }
                </VStack>
            // )
            }
        </Box>
    );
};

export default Artifacts;