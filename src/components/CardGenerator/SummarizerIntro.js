import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon, Button, useMediaQuery } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaYoutube, FaBrain, FaLightbulb, FaClock, FaSearch, FaQuestionCircle, FaRobot, FaChrome, FaExternalLinkAlt } from 'react-icons/fa'
import { MdCheckCircle, MdAutoAwesome, MdSpeed, MdTrendingUp } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon, iconColor }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color={iconColor || "dodgerblue"} />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const SummarizerIntro = () => {
    const { t } = useTranslation('summarizer')
    const [isMobile] = useMediaQuery("(max-width: 768px)")

    const keyFeatures = [
        {
            icon: FaYoutube,
            title: t('summarizer_feature_1_title'),
            text: t('summarizer_feature_1_text')
        },
        {
            icon: FaLightbulb,
            title: t('summarizer_feature_2_title'),
            text: t('summarizer_feature_2_text')
        },
        {
            icon: FaSearch,
            title: t('summarizer_feature_3_title'),
            text: t('summarizer_feature_3_text')
        },
        {
            icon: FaBrain,
            title: t('summarizer_feature_4_title'),
            text: t('summarizer_feature_4_text')
        }
    ]

    const benefits = [
        {
            icon: MdSpeed,
            title: t('summarizer_benefit_1_title'),
            text: t('summarizer_benefit_1_text')
        },
        {
            icon: MdAutoAwesome,
            title: t('summarizer_benefit_2_title'),
            text: t('summarizer_benefit_2_text')
        },
        {
            icon: MdTrendingUp,
            title: t('summarizer_benefit_3_title'),
            text: t('summarizer_benefit_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            <Section bg="gray.50">
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8}>
                    {keyFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('summarizer_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {benefits.map((benefit, index) => (
                        <Feature
                            iconColor={'purple.500'}
                            key={index}
                            icon={benefit.icon}
                            title={benefit.title}
                            text={benefit.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="gray.50" title={t('summarizer_how_it_works_title')}>
                <VStack spacing={8} maxW="3xl" mx="auto">
                    <Text fontSize="lg" textAlign="center" color="gray.600">
                        {t('summarizer_how_it_works_description')}
                    </Text>
                    <Box 
                        p={6} 
                        bg="white" 
                        rounded="xl" 
                        shadow="md" 
                        borderWidth="1px"
                        width="100%"
                    >
                        <VStack spacing={4} align="start">
                            <Heading size="md" color="purple.500">
                                {t('summarizer_browser_extension_title')}
                            </Heading>
                            <Text color="gray.600">
                                {t('summarizer_browser_extension_description')}
                            </Text>
                            <Button
                                leftIcon={<FaChrome />}
                                rightIcon={<FaExternalLinkAlt />}
                                colorScheme="blue"
                                onClick={() => {
                                    const isEdge = /Edg/.test(navigator.userAgent);
                                    const url = isEdge 
                                        ? 'https://microsoftedge.microsoft.com/addons/detail/funblocks-ai-your-ultim/lmmlojdklhcdiefaniakpkhhdmamnigk' 
                                        : 'https://chromewebstore.google.com/detail/funblocks-ai-your-ultimat/coodnehmocjfaandkbeknihiagfccoid';
                                    window.open(url, '_blank');
                                }}
                            >
                                {t('install_chrome_extension')}
                            </Button>
                        </VStack>
                    </Box>
                </VStack>
            </Section>

            <HowToUse namespace="video" withTips={false} />

            <Section bg="white">
                <Heading size="lg" mb={8} textAlign="center">
                    {t('summarizer_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`summarizer_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`summarizer_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default SummarizerIntro 