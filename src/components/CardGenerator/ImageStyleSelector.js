import { <PERSON><PERSON><PERSON><PERSON>, H<PERSON>tack, Select } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { useEffect } from 'react';
import { APP_TYPE } from '../../utils/constants';

const styleOptions = [
  { id: 'avatar_styles_realistic_title', type: 'title' },
  { id: 'avatar_style_realistic_1', type: 'option' },
  { id: 'avatar_style_realistic_2', type: 'option' },
  { id: 'avatar_style_realistic_3', type: 'option' },
  { id: 'avatar_style_realistic_4', type: 'option' },
  { id: 'avatar_style_realistic_5', type: 'option' },
  { id: 'avatar_style_realistic_6', type: 'option' },

  { id: 'avatar_styles_anime_title', type: 'title' },
  { id: 'avatar_style_anime_1', type: 'option' },
  { id: 'avatar_style_anime_2', type: 'option' },
  { id: 'avatar_style_anime_3', type: 'option' },
  { id: 'avatar_style_anime_4', type: 'option' },
  { id: 'avatar_style_anime_5', type: 'option' },
  { id: 'avatar_style_anime_6', type: 'option' },
  { id: 'avatar_style_anime_7', type: 'option' },

  { id: 'avatar_styles_fantasy_title', type: 'title' },
  { id: 'avatar_style_fantasy_1', type: 'option' },
  { id: 'avatar_style_fantasy_2', type: 'option' },
  { id: 'avatar_style_fantasy_3', type: 'option' },
  { id: 'avatar_style_fantasy_4', type: 'option' },
  { id: 'avatar_style_fantasy_5', type: 'option' },

  { id: 'avatar_styles_retro_title', type: 'title' },
  { id: 'avatar_style_retro_1', type: 'option' },
  { id: 'avatar_style_retro_2', type: 'option' },
  { id: 'avatar_style_retro_3', type: 'option' },
  { id: 'avatar_style_retro_4', type: 'option' },
  { id: 'avatar_style_retro_5', type: 'option' },

  { id: 'avatar_styles_trendy_title', type: 'title' },
  { id: 'avatar_style_trendy_1', type: 'option' },
  { id: 'avatar_style_trendy_2', type: 'option' },
  { id: 'avatar_style_trendy_3', type: 'option' },
  { id: 'avatar_style_trendy_4', type: 'option' },
  { id: 'avatar_style_trendy_5', type: 'option' },

  { id: 'avatar_styles_costume_title', type: 'title' },
  { id: 'avatar_style_costume_1', type: 'option' },
  { id: 'avatar_style_costume_2', type: 'option' },
  { id: 'avatar_style_costume_3', type: 'option' },
  { id: 'avatar_style_costume_4', type: 'option' },
  { id: 'avatar_style_costume_5', type: 'option' }
];

const ImageStyleSelector = ({ onSelect, value, mode }) => {
  const { t } = useTranslation(APP_TYPE.avatar.toLowerCase());

  useEffect(() => {
    if(!value && styleOptions?.length) {
      onSelect(t(styleOptions[1].id))
    }
  }, [value])

  return (
    <HStack width={'100%'}>
      <FormLabel whiteSpace="nowrap" m={0} marginRight={0}>{t('select_image_style')}</FormLabel>
      <Select
        bg='white'
        variant={'ghost'}
        onChange={(e) => onSelect(e.target.value)}
        value={value}
        isRequired
      >
        {styleOptions.map((option) => {
          return (
            <option key={option.id} value={t(option.id)} disabled={option.type==='title'}>
              {t(option.id)}
            </option>
          )
        })}
      </Select>
    </HStack>
  )
}

export default ImageStyleSelector
