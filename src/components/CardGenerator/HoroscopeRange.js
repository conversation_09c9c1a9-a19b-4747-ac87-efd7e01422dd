import { <PERSON><PERSON><PERSON>l, HStack, Select, Input, Flex, useBreakpointValue } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { useEffect, useMemo } from 'react';

const HoroscopeRange = ({ onChange, value }) => {
  const { t } = useTranslation('common');
  const isMobile = useBreakpointValue({ base: true, md: false })
  
  const Genders = useMemo(() => [{ value: 'today', label: t('horoscope_today') }, { value: 'all', label: t('horoscope_all') }], [t])

  return (
    <Flex width={'100%'} columnGap={4} rowGap={3} flexDirection={isMobile? 'column': 'row'}>
      <HStack width={'100%'}>
        <FormLabel whiteSpace="nowrap" m={0} marginRight={0}>{t('horoscope_range')}</FormLabel>
        <Select
          bg='white'
          variant={'ghost'}
          onChange={(e) => {
            if (!e.target.value) return;
            onChange(e.target.value)
          }}
          value={value || 'today'}
          isRequired
        >
          {Genders?.map((type) => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </Select>
      </HStack>
    </Flex>
  )
}

export default HoroscopeRange
