import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaTasks, FaBrain, FaLightbulb, FaProjectDiagram, FaChartLine, FaUsers, FaClock, FaCheckCircle } from 'react-icons/fa'
import { MdCheckCircle } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="dodgerblue" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const PlannerIntro = () => {
    const { t } = useTranslation('planner')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaTasks,
            title: t('planner_feature_1_title'),
            text: t('planner_feature_1_text')
        },
        {
            icon: FaBrain,
            title: t('planner_feature_2_title'),
            text: t('planner_feature_2_text')
        },
        {
            icon: FaProjectDiagram,
            title: t('planner_feature_3_title'),
            text: t('planner_feature_3_text')
        },
        {
            icon: FaLightbulb,
            title: t('planner_feature_4_title'),
            text: t('planner_feature_4_text')
        }
    ]

    const benefits = [
        {
            icon: FaChartLine,
            title: t('planner_benefit_1_title'),
            text: t('planner_benefit_1_text')
        },
        {
            icon: FaClock,
            title: t('planner_benefit_2_title'),
            text: t('planner_benefit_2_text')
        },
        {
            icon: FaCheckCircle,
            title: t('planner_benefit_3_title'),
            text: t('planner_benefit_3_text')
        },
        {
            icon: FaUsers,
            title: t('planner_benefit_4_title'),
            text: t('planner_benefit_4_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                {/* <VStack spacing={isMobile ? 4 : 8} mb={isMobile ? 0 : 10}>
                    <Heading
                        fontSize={isMobile ? '2xl' : '3xl'}
                        textAlign="center"
                        bgGradient="linear(to-r, purple.400, pink.400)"
                        bgClip="text"
                    >
                        {t('planner_intro_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                    >
                        {t('planner_intro_description')}
                    </Text>
                </VStack> */}

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('planner_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {benefits.map((benefit, index) => (
                        <Feature
                            key={index}
                            icon={benefit.icon}
                            title={benefit.title}
                            text={benefit.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Use Cases Section */}
            <Section bg="gray.50" title={t('planner_use_cases_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl">
                        <Heading size="md" color="purple.500">{t('planner_business_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`planner_business_use_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl">
                        <Heading size="md" color="purple.500">{t('planner_personal_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`planner_personal_use_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={8} textAlign="center">
                    {t('planner_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`planner_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`planner_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default PlannerIntro 