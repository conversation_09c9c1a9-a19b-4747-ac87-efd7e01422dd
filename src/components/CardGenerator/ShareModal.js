import React, { useState, useEffect } from 'react';
import { useShareModal } from '../../contexts/ShareModalContext';
import {
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalCloseButton,
    Button,
    VStack,
    useToast,
    Box,
    Image,
    Flex,
    useBreakpointValue,
} from '@chakra-ui/react';
import {
    TwitterShareButton,
    FacebookShareButton,
    LinkedinShareButton,
    WhatsappShareButton,
    EmailShareButton,
    TwitterIcon,
    FacebookIcon,
    LinkedinIcon,
    WhatsappIcon,
    EmailIcon,
} from 'react-share';
import { FaInstagram } from 'react-icons/fa';
import { useTranslation } from 'next-i18next';
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
const ShareModal = ({ app }) => {
    const { t } = useTranslation('common');
    const { isOpen, artifactId, title = '', imageDataUrl, closeShareModal, tool } = useShareModal();
    const [shareUrl, setShareUrl] = useState('');
    const toast = useToast();
    const isMobile = useBreakpointValue({ base: true, md: false });
    const { basePath } = getConfig().publicRuntimeConfig;
    useEffect(() => {
        if (typeof window !== 'undefined') {
            setShareUrl(`${window.location.origin}${basePath}/share/${app || tool}/${artifactId}/${title?.replaceAll(' ', '-') || ''}`);
        }
    }, [artifactId]);

    const copyToClipboard = async () => {
        try {
            const textToCopy = shareUrl;
            await navigator.clipboard.writeText(textToCopy);
            toast({
                title: t('title_and_link_copied'),
                status: "success",
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            console.error('Failed to copy:', error);
            toast({
                title: t('copy_failed'),
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        }
    };

    const shareToInstagram = () => {
        copyToClipboard();
        toast({
            title: t('title_and_link_copied'),
            description: t('save_image_and_paste_to_instagram'),
            status: "success",
            duration: 3000,
            isClosable: true,
        });
    };

    const ShareButton = ({ children }) => (
        <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            gap="10px"
            cursor="pointer"
            p={2}
            borderRadius="md"
            transition="background-color 0.2s"
            _hover={{ backgroundColor: 'gray.100' }}
            width="100%"
        >
            {children}
        </Box>
    );

    return (
        <Modal isOpen={isOpen} onClose={closeShareModal} size={isMobile ? "full" : "2xl"}>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader>{t('share_to_social_platforms')}</ModalHeader>
                <ModalCloseButton />
                <ModalBody>
                    <Flex direction={(isMobile || [APP_TYPE.mindmap].includes(app)) ? "column" : "row"} gap={4}>
                        <Box flex={1}>
                            <Image src={imageDataUrl} alt="Card preview" w="100%" h="auto" mb={4} borderRadius="md" />
                        </Box>
                        <Box
                            align="stretch"
                            flex={1}
                            style={{ 
                                display: [APP_TYPE.mindmap].includes(app) && !isMobile ? "flow" : "flex",
                                flexDirection: [APP_TYPE.mindmap].includes(app) && !isMobile ? "row" : "column",
                             }}
                        >
                            <TwitterShareButton url={shareUrl} title={title}>
                                <ShareButton>
                                    <TwitterIcon size={32} round />
                                    <span>{t('share_to_twitter')}</span>
                                </ShareButton>
                            </TwitterShareButton>
                            <FacebookShareButton url={shareUrl} quote={`${title}\n${shareUrl}`}>
                                <ShareButton>
                                    <FacebookIcon size={32} round />
                                    <span>{t('share_to_facebook')}</span>
                                </ShareButton>
                            </FacebookShareButton>
                            <LinkedinShareButton url={shareUrl} title={title}>
                                <ShareButton>
                                    <LinkedinIcon size={32} round />
                                    <span>{t('share_to_linkedin')}</span>
                                </ShareButton>
                            </LinkedinShareButton>
                            <WhatsappShareButton url={shareUrl} title={title}>
                                <ShareButton>
                                    <WhatsappIcon size={32} round />
                                    <span>{t('share_to_whatsapp')}</span>
                                </ShareButton>
                            </WhatsappShareButton>
                            {/* <ShareButton> */}
                            <Box as="button" onClick={shareToInstagram} gap="10px">
                                <ShareButton>
                                    <FaInstagram size={32} />
                                    <span>{t('share_to_instagram')}</span>
                                </ShareButton>
                            </Box>
                            {/* </ShareButton> */}
                            <EmailShareButton url={shareUrl} subject={title} body={`${title}\n\n${shareUrl}`}>
                                <ShareButton>
                                    <EmailIcon size={32} round />
                                    <span>{t('share_via_email')}</span>
                                </ShareButton>
                            </EmailShareButton>
                            <Button onClick={copyToClipboard} width="100%" style={{ marginRight: 10, marginTop: 6 }}>
                                {t('copy_title_and_link')}
                            </Button>
                            <Button as="a" href={imageDataUrl} download="card.png" width="100%" style={{ marginRight: 10, marginTop: 6 }}>
                                {t('download_image')}
                            </Button>
                        </Box>
                    </Flex>
                </ModalBody>
            </ModalContent>
        </Modal>
    );
};

export default ShareModal;