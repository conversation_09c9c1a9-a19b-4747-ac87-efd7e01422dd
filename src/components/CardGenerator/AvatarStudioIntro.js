import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaCamera, FaImages, FaPaintBrush, FaMagic, FaUserAstronaut, FaDragon, FaRobot, FaUserNinja, FaShare, FaUser } from 'react-icons/fa'
import { MdCheckCircle, MdPhotoLibrary, MdStyle, MdAutoFixHigh, MdOutlinePrivacyTip, MdSpeed, MdPeopleAlt, MdPhoto } from 'react-icons/md'
import { GiNinjaHeroicStance, GiCyberEye, GiSpellBook, GiOilDrum, GiHumanPyramid } from 'react-icons/gi'
import Section from '../common/Section'
import { APP_TYPE } from '../../utils/constants'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const StyleCategory = ({ title, styles, icon }) => {
    return (
        <VStack
            align="start"
            spacing={4}
            p={6}
            bg="gray.50"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            width="100%"
        >
            <Box display="flex" alignItems="center">
                <Icon as={icon} w={5} h={5} color="blue.500" mr={2} />
                <Heading size="md" color="blue.500">{title}</Heading>
            </Box>
            <List spacing={2} width="100%">
                {styles.map((style, index) => {
                    const parts = style.split(' – ');
                    return (
                        <ListItem
                            key={index}
                            // py={2}
                            // px={3}
                            borderRadius="md"
                            _hover={{ bg: "blue.50" }}
                            transition="all 0.2s"
                        >
                          <span style={{fontWeight: 'bold'}}>{parts[0]}</span> - {parts[1]}
                        </ListItem>
                    )
                })}
            </List>
        </VStack>
    )
}

const AvatarStudioIntro = () => {
    const { t } = useTranslation(APP_TYPE.avatar.toLowerCase())
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaMagic,
            title: t('avatar_feature_1_title'),
            text: t('avatar_feature_1_text')
        },
        {
            icon: FaPaintBrush,
            title: t('avatar_feature_2_title'),
            text: t('avatar_feature_2_text')
        },
        {
            icon: FaImages,
            title: t('avatar_feature_3_title'),
            text: t('avatar_feature_3_text')
        }
    ]

    const targetAudiences = [
        {
            icon: FaUser,
            title: t('avatar_audience_4_title'),
            text: t('avatar_audience_4_text')
        },
        {
            icon: MdPeopleAlt,
            title: t('avatar_audience_1_title'),
            text: t('avatar_audience_1_text')
        },
        {
            icon: FaUserNinja,
            title: t('avatar_audience_2_title'),
            text: t('avatar_audience_2_text')
        },
        {
            icon: GiHumanPyramid,
            title: t('avatar_audience_3_title'),
            text: t('avatar_audience_3_text')
        }
    ]

    const realisticStyles = [1, 2, 3, 4, 5, 6].map(i => t(`avatar_style_realistic_${i}`))
    const animeStyles = [1, 2, 3, 4, 5, 6, 7].map(i => t(`avatar_style_anime_${i}`))
    const fantasyStyles = [1, 2, 3, 4, 5].map(i => t(`avatar_style_fantasy_${i}`))
    const retroStyles = [1, 2, 3, 4, 5].map(i => t(`avatar_style_retro_${i}`))
    const trendyStyles = [1, 2, 3, 4, 5].map(i => t(`avatar_style_trendy_${i}`))
    const costumeStyles = [1, 2, 3, 4, 5].map(i => t(`avatar_style_costume_${i}`))

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('avatar_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Style Showcase Section */}
            <Section bg="white" title={t('avatar_styles_title')}>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 2 }} spacing={8} width="100%">
                    <StyleCategory
                        title={t('avatar_styles_realistic_title')}
                        styles={realisticStyles}
                        icon={MdPhoto} // Changed icon to represent realistic style
                    />
                    <StyleCategory
                        title={t('avatar_styles_anime_title')}
                        styles={animeStyles}
                        icon={GiNinjaHeroicStance}
                    />
                    <StyleCategory
                        title={t('avatar_styles_fantasy_title')}
                        styles={fantasyStyles}
                        icon={FaDragon}
                    />
                    <StyleCategory
                        title={t('avatar_styles_retro_title')}
                        styles={retroStyles}
                        icon={GiOilDrum}
                    />
                    <StyleCategory
                        title={t('avatar_styles_trendy_title')}
                        styles={trendyStyles}
                        icon={MdStyle}
                    />
                    <StyleCategory
                        title={t('avatar_styles_costume_title')}
                        styles={costumeStyles}
                        icon={FaUserAstronaut}
                    />
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="gray.50" title={t('avatar_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('avatar_benefits_personal_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`avatar_benefit_personal_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('avatar_benefits_professional_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`avatar_benefit_professional_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Target Audience Section */}
            <Section bg="white" title={t('avatar_audience_title')}>
                <SimpleGrid columns={{ base: 1, md: 4 }} spacing={8}>
                    {targetAudiences.map((audience, index) => (
                        <Feature
                            key={index}
                            icon={audience.icon}
                            title={audience.title}
                            text={audience.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Technical Advantages & Unique Features */}
            <Section bg="gray.50"  title={t('avatar_advantages_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('avatar_tech_advantages_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3].map((i) => (
                                <ListItem key={i}>
                                    <Box display="flex" alignItems="center" mb={1}>
                                        <Icon
                                            as={i === 1 ? MdAutoFixHigh : i === 2 ? GiCyberEye : i === 3 ? MdSpeed : MdOutlinePrivacyTip}
                                            color="blue.500"
                                            mr={2}
                                        />
                                        <Text fontWeight="bold">{t(`avatar_tech_advantage_${i}_title`)}</Text>
                                    </Box>
                                    <Text color="gray.600">{t(`avatar_tech_advantage_${i}_text`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                        <Heading size="md" color="blue.500">{t('avatar_unique_features_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3].map((i) => (
                                <ListItem key={i}>
                                    <Box display="flex" alignItems="center" mb={1}>
                                        <Icon
                                            as={i === 1 ? FaPaintBrush : i === 2 ? FaCamera : i === 3 ? MdStyle : FaImages}
                                            color="blue.500"
                                            mr={2}
                                        />
                                        <Text fontWeight="bold">{t(`avatar_unique_feature_${i}_title`)}</Text>
                                    </Box>
                                    <Text color="gray.600">{t(`avatar_unique_feature_${i}_text`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <HowToUse namespace="photo" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading
                    size="lg"
                    mb={isMobile ? 4 : 8}
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.400)"
                    bgClip="text"
                >
                    {t('avatar_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`avatar_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`avatar_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default AvatarStudioIntro 