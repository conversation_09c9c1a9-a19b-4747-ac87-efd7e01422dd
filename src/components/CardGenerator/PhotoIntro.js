import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaCamera, FaChartLine, FaGraduationCap, FaLightbulb, FaImage, FaBook, FaUserGraduate, FaUsers } from 'react-icons/fa'
import { MdCheckCircle } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="teal.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const PhotoIntro = () => {
    const { t } = useTranslation('photo')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const mainFeatures = [
        {
            icon: FaCamera,
            title: t('photo_feature_1_title'),
            text: t('photo_feature_1_text')
        },
        {
            icon: FaChartLine,
            title: t('photo_feature_2_title'),
            text: t('photo_feature_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('photo_feature_3_title'),
            text: t('photo_feature_3_text')
        }
    ]

    const userTypes = [
        {
            icon: FaUserGraduate,
            title: t('photo_user_1_title'),
            text: t('photo_user_1_text')
        },
        {
            icon: FaUsers,
            title: t('photo_user_2_title'), 
            text: t('photo_user_2_text')
        },
        {
            icon: FaGraduationCap,
            title: t('photo_user_3_title'),
            text: t('photo_user_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('photo_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {mainFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('photo_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="teal.500">{t('photo_learning_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`photo_learning_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="teal.500">{t('photo_technical_benefits_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`photo_technical_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <Section bg="gray.50" title={t('photo_perfect_for_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {userTypes.map((userType, index) => (
                        <Feature
                            key={index}
                            icon={userType.icon}
                            title={userType.title}
                            text={userType.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <HowToUse namespace="photo" />

            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('photo_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`photo_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`photo_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default PhotoIntro 