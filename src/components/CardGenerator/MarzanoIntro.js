import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaGraduationCap, FaChalkboardTeacher, FaUserGraduate, FaUsers, FaLightbulb, FaChartLine, FaLayerGroup, FaMapMarkedAlt, FaTasks, FaRocket } from 'react-icons/fa'
import { MdCheckCircle, MdSchool, MdTimeline, MdAutoGraph, MdPsychology, MdOutlineAssignment, MdSettings, MdOutlineStairs } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const MarzanoIntro = () => {
    const { t } = useTranslation('marzano')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaBrain,
            title: t('marzano_feature_1_title'),
            text: t('marzano_feature_1_text')
        },
        {
            icon: FaLayerGroup,
            title: t('marzano_feature_2_title'),
            text: t('marzano_feature_2_text')
        },
        {
            icon: FaMapMarkedAlt,
            title: t('marzano_feature_3_title'),
            text: t('marzano_feature_3_text')
        }
    ]

    const targetAudiences = [
        {
            icon: FaChalkboardTeacher,
            title: t('marzano_audience_1_title'),
            text: t('marzano_audience_1_text')
        },
        {
            icon: MdOutlineAssignment,
            title: t('marzano_audience_2_title'),
            text: t('marzano_audience_2_text')
        },
        {
            icon: FaUserGraduate,
            title: t('marzano_audience_3_title'),
            text: t('marzano_audience_3_text')
        },
        {
            icon: FaRocket,
            title: t('marzano_audience_4_title'),
            text: t('marzano_audience_4_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={2}
                >
                    {t('marzano_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white" title={t('marzano_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('marzano_benefits_edu_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`marzano_benefit_edu_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('marzano_benefits_learner_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`marzano_benefit_learner_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Target Audience Section */}
            <Section bg="gray.50" title={t('marzano_audience_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {targetAudiences.map((audience, index) => (
                        <Feature
                            key={index}
                            icon={audience.icon}
                            title={audience.title}
                            text={audience.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Marzano Taxonomy Framework */}
            <Section bg="white">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('marzano_framework_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('marzano_framework_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('marzano_systems_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`marzano_system_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`marzano_system_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('marzano_levels_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4, 5, 6].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`marzano_level_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`marzano_level_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Advantages Section */}
            <Section bg="gray.50">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('marzano_advantages_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('marzano_advantages_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 1 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                            <List spacing={3}>
                                {[1, 2, 3, 4, 5].map((i) => (
                                    <ListItem key={i} display="flex" alignItems="center">
                                        <ListIcon as={MdCheckCircle} color="green.500" />
                                        <Text fontWeight="bold">{t(`marzano_advantage_${i}`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="mindmap" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('marzano_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`marzano_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`marzano_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default MarzanoIntro 