import React, { useRef, useEffect, useState, useCallback, useLayoutEffect } from 'react';
import { Box, Button, VStack, Tooltip, HStack, useToast, Text, IconButton, useMediaQuery } from '@chakra-ui/react';
import { Fullscreen, FullscreenExit, Save, ZoomIn, ZoomOut } from '@styled-icons/material';
import { useTranslation } from 'next-i18next';
import { getSvgDimensions, preprocessSvg } from '../../utils/svgUtils';
import ReactMarkdown from 'react-markdown';
import { useShareModal } from '../../contexts/ShareModalContext';
import MermaidRenderer from './MermaidRenderer';
import { APP_TYPE } from '../../utils/constants';

const ArtifactDisplay = ({ artifact, svgRef = useRef(null), shadow, app }) => {
  const { t } = useTranslation('common');
  const toast = useToast();
  const [svgContent, setSvgContent] = useState('');
  const [error, setError] = useState(null);
  const [isHovered, setIsHovered] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const fullscreenContainerRef = useRef(null);
  const [isSvgRendered, setIsSvgRendered] = useState(false);
  const containerRef = useRef(null);
  const { openShareModal } = useShareModal();
  const [scale, setScale] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const [isMobile] = useMediaQuery("(max-width: 768px)")

  const handleFullscreen = () => {
    if (!isFullscreen) {
      const elem = fullscreenContainerRef.current;
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.webkitRequestFullscreen) { /* Safari */
        elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) { /* IE11 */
        elem.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) { /* Safari */
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) { /* IE11 */
        document.msExitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      const isEnteringFullscreen = !!document.fullscreenElement;
      setIsFullscreen(isEnteringFullscreen);
      setScale(1);
      setPosition({ x: 0, y: 0 });

      const svgElement = svgRef.current.querySelector('svg');
      if (!svgElement) return;

      // 重置变换
      svgElement.style.transform = 'translate(0px, 0px) scale(1)';

      if (isEnteringFullscreen) {
        svgElement.parentElement.style.display = 'flex';
        svgElement.parentElement.style.justifyContent = 'center';
        svgElement.parentElement.style.alignItems = 'center';

        svgElement.parentElement.style.width = '100%';
        svgElement.parentElement.style.height = '100vh';

        svgRef.current.style.width = '100%';
      } else {
        // 退出全屏时重置所有样式
        svgElement.style.width = '100%';
        svgElement.style.height = '100%';
        svgElement.parentElement.style.margin = '0';
        svgElement.parentElement.style.width = '100%';
        svgElement.parentElement.style.height = '-webkit-fill-available';
        svgRef.current.style.height = '100%';

        // 重新调用调整高度的函数
        adjustSvgContainerHeight();
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [svgRef]);

  useEffect(() => {
    if (artifact?.type === 'svg' && artifact?.content) {
      try {
        setSvgContent(preprocessSvg(artifact.content));
        setError(null);
      } catch (err) {
        console.error('Error setting initial SVG:', err);
        setError('无法加载SVG内容');
      }
    }
  }, [artifact]);

  const adjustSvgContainerHeight = () => {
    const svgElement = svgRef?.current?.querySelector('svg');

    if (svgElement) {
      if (isFullscreen) {
        svgRef.current.style.height = '100%';
        return;
      }

      const svgDimensions = getSvgDimensions(svgContent);
      const viewBox = svgElement.getAttribute('viewBox');

      const parentMaxWidth = parseInt(svgRef.current.parentElement.style.maxWidth?.replace('px', '')) || 0;
      const maxWidth = Math.min(window.innerWidth, parentMaxWidth);

      console.log('max width............', maxWidth, window.innerWidth, parentMaxWidth)

      let width = svgDimensions?.width || viewBox.split(' ')[2];
      let height = svgDimensions?.height || viewBox.split(' ')[3];

      if (width) {
        let aspectRatio = height / width;

        if (width > maxWidth * 0.9) {
          width = maxWidth * 0.9;
          svgRef.current.style.width = `${width}px`;
        }

        height = width * aspectRatio;
        svgRef.current.style.height = `${height}px`;
      } else {
        svgRef.current.style.height = 'fit-content';
      }

      svgElement.style.width = '-webkit-fill-available';

      // console.log('svgRef.current.style.height..............', svgRef.current.style.height, svgDimensions, viewBox);
    }
  };

  useEffect(() => {
    if (!svgRef?.current?.querySelector('svg')) return;

    adjustSvgContainerHeight();
  }, [isFullscreen]);

  const onSvgRendered = useCallback(() => {
    adjustSvgContainerHeight();
    setIsSvgRendered(true);
    // 在这里可以添加其他需要在 SVG 渲染后执行的操作
  }, []);

  useEffect(() => {
    if (svgContent) {
      const checkSvgRendered = () => {
        const svgElement = svgRef.current?.querySelector('svg');
        if (svgElement) {
          onSvgRendered();
        } else {
          requestAnimationFrame(checkSvgRendered);
        }
      };

      checkSvgRendered();
    }
  }, [svgContent, onSvgRendered]);

  const getWittyInsightsStyle = () => {
    if (artifact?.promptId === 'witty_insights') {
      return {
        background: 'linear-gradient(180deg, #009fff 0%, #f0f8ff 100%)',
        borderRadius: '2px',
        padding: '2rem',
        margin: 6,
        boxShadow: '0 8px 16px rgba(31, 38, 135, 0.15)',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        _hover: {
          transform: 'translateY(-4px)',
          boxShadow: '0 12px 24px rgba(31, 38, 135, 0.2)',
        }
      };
    }
    return {};
  };

  const handleZoom = (type) => {
    const svgElement = svgRef.current.querySelector('svg');
    if (!svgElement) return;

    let newScale;
    if (type === 'in') {
      newScale = scale + 0.1;
    } else {
      newScale = Math.max(0.1, scale - 0.1);
    }

    svgElement.style.transform = `translate(${position.x}px, ${position.y}px) scale(${newScale})`;
    setScale(newScale);
  };

  const handleMouseDown = (e) => {
    if (!isFullscreen) return;

    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !isFullscreen) return;

    const newX = e.clientX - dragStart.x;
    const newY = e.clientY - dragStart.y;

    setPosition({ x: newX, y: newY });

    const svgElement = svgRef.current.querySelector('svg');
    if (svgElement) {
      svgElement.style.transform = `translate(${newX}px, ${newY}px) scale(${scale})`;
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  if (error) {
    return <Text color="red.500">{t('error_loading_svg')}</Text>;
  }

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.01  // 降低阈值，使其更容易触发
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          adjustSvgContainerHeight();
        }
      });
    }, options);

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, []);

  return (
    <VStack spacing={4} align="center" width="100%" ref={containerRef}>
      <Box
        ref={fullscreenContainerRef}
        width="100%"
        display="flex"
        justifyContent="center"
        alignItems="center"
        boxShadow={shadow ? "0 6px 8px rgba(0, 0, 0, 0.15)" : 'none'}
        borderRadius={"lg"}
        overflow="hidden"
        backgroundColor="white"
        padding={2}
        style={{
          position: 'relative',
          maxWidth: isMobile ? window.innerWidth : 800
        }}
        id={`artifact-content-${artifact?._id}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div
          ref={svgRef}
          style={{
            width: '100%',
            height: 'fit-content',
            minWidth: 300,
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'center',
            flexDirection: 'column',
            overflow: 'hidden',
            cursor: isFullscreen ? (isDragging ? 'grabbing' : 'grab') : 'default',
            ...getWittyInsightsStyle()
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {
            artifact.type === 'mermaid' && !isFullscreen &&
            !!artifact.title &&
            <Text height={'-moz-min-content'} width={'100%'} fontWeight={'500'} color={'#555'}>
              {artifact.title}
            </Text>
          }
          {
            artifact.imageUrl &&
            <img
              src={artifact.imageUrl}
              alt={artifact.title || 'Generated image'}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain',
                borderRadius: 16
              }}
            />
          }
          {
            artifact.type === 'svg' && (svgContent ? (
              <div
                dangerouslySetInnerHTML={{ __html: svgContent }}
                style={{
                  width: '100%',
                  height: '-webkit-fill-available',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              />
            ) : (
              <Text>{t('loading')}</Text>
            ))
          }
          {
            artifact.type === 'markdown' &&
            <div
              style={{
                width: '100%',
                padding: '4px',
                overflow: 'auto',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start'
              }}
              className="markdown-content"
            >
              <ReactMarkdown>
                {artifact.content}
              </ReactMarkdown>
            </div>
          }
          {
            artifact.type === 'mermaid' &&
            <MermaidRenderer id={'mermaid-chart-' + artifact._id} source={artifact.content} />
          }
        </div>
        {isHovered && (
          <HStack
            position="absolute"
            top={2}
            right={2}
            spacing={1}
          >
            {isFullscreen && !isMobile && (
              <>
                <Tooltip label={t('zoom_out')} placement="bottom">
                  <IconButton
                    icon={<ZoomOut size={24} />}
                    aria-label={t('zoom_out')}
                    size="sm"
                    onClick={() => handleZoom('out')}
                  />
                </Tooltip>
                <Tooltip label={t('zoom_in')} placement="bottom">
                  <IconButton
                    icon={<ZoomIn size={24} />}
                    aria-label={t('zoom_in')}
                    size="sm"
                    onClick={() => handleZoom('in')}
                  />
                </Tooltip>
              </>
            )}
            <Tooltip label={t(isFullscreen ? 'exit_fullscreen_tooltip' : 'fullscreen_tooltip')} placement={isFullscreen ? "bottom" : "top"}>
              <IconButton
                icon={isFullscreen ? <FullscreenExit size={28} /> : <Fullscreen size={28} />}
                aria-label={t('fullscreen_display')}
                size="sm"
                onClick={handleFullscreen}
              />
            </Tooltip>
          </HStack>
        )}
      </Box>
    </VStack>
  );
};

export default ArtifactDisplay;
