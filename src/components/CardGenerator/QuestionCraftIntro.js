import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaSearch, FaChartLine, FaGraduationCap, FaUserTie, FaChalkboardTeacher, FaQuestionCircle } from 'react-icons/fa'
import { MdCheckCircle, MdAutoGraph, MdPsychology, MdOutlineQuestionAnswer } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const QuestionCraftIntro = () => {
    const { t } = useTranslation('question')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const mainFeatures = [
        {
            icon: FaBrain,
            title: t('questioncraft_feature_1_title'),
            text: t('questioncraft_feature_1_text')
        },
        {
            icon: MdAutoGraph,
            title: t('questioncraft_feature_2_title'),
            text: t('questioncraft_feature_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('questioncraft_feature_3_title'),
            text: t('questioncraft_feature_3_text')
        },
        {
            icon: MdOutlineQuestionAnswer,
            title: t('questioncraft_feature_4_title'),
            text: t('questioncraft_feature_4_text')
        }
    ]

    const userGroups = [
        {
            icon: FaSearch,
            title: t('questioncraft_user_1_title'),
            text: t('questioncraft_user_1_text')
        },
        {
            icon: FaGraduationCap,
            title: t('questioncraft_user_2_title'),
            text: t('questioncraft_user_2_text')
        },
        {
            icon: FaUserTie,
            title: t('questioncraft_user_3_title'),
            text: t('questioncraft_user_3_text')
        },
        {
            icon: FaChalkboardTeacher,
            title: t('questioncraft_user_4_title'),
            text: t('questioncraft_user_4_text')
        }
    ]

    const benefits = [
        {
            icon: MdPsychology,
            title: t('questioncraft_benefit_1_title'),
            text: t('questioncraft_benefit_1_text')
        },
        {
            icon: FaChartLine,
            title: t('questioncraft_benefit_2_title'),
            text: t('questioncraft_benefit_2_text')
        },
        {
            icon: FaQuestionCircle,
            title: t('questioncraft_benefit_3_title'),
            text: t('questioncraft_benefit_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            <Section bg="gray.50">
                <VStack spacing={isMobile ? 4 : 8} mb={6}>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                    >
                        {t('questioncraft_feature_summary')}
                    </Text>
                </VStack>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {mainFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('questioncraft_perfect_for_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {userGroups.map((group, index) => (
                        <Feature
                            key={index}
                            icon={group.icon}
                            title={group.title}
                            text={group.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="gray.50" title={t('questioncraft_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {benefits.map((benefit, index) => (
                        <Feature
                            key={index}
                            icon={benefit.icon}
                            title={benefit.title}
                            text={benefit.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            <Section bg="white" title={t('questioncraft_use_cases_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('questioncraft_academic_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`questioncraft_academic_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="blue.500">{t('questioncraft_professional_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`questioncraft_professional_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindmap" />

            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`questioncraft_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`questioncraft_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default QuestionCraftIntro 