import React from 'react'
import {
    Box,
    Button,
    Flex,
    Grid,
    GridItem,
    Heading,
    Image,
    List,
    ListIcon,
    ListItem,
    SimpleGrid,
    Text,
    VStack,
    useBreakpointValue,
    Badge,
    HStack,
    Icon,
    Divider,
    Accordion,
    AccordionItem,
    AccordionButton,
    AccordionPanel,
    AccordionIcon
} from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import {
    FaArrowRight,
    FaLightbulb,
    FaLayerGroup,
    FaMountain,
    FaChalkboardTeacher,
    FaGraduationCap,
    FaBriefcase,
    FaFlask,
    FaBook,
    FaQuestionCircle,
    FaVideo,
    FaUsers,
    FaChartBar,
    FaLaptopCode,
    FaBrain,
    FaChartLine,
    FaRocket,
    FaNetworkWired,
    FaRegObjectGroup,
    FaUserGraduate,
    FaRegLightbulb,
    FaSitemap,
    FaRegComments,
    FaRegClock
} from 'react-icons/fa'
import { MdAutoAwesome, MdInsights, MdOutlinePersonalInjury } from 'react-icons/md'
import Link from 'next/link'
import HowToUse from '../common/HowToUse'
import ComparisonTable from '../common/ComparisonTable'
import ResearchBacked from '../common/ResearchBacked'
import Testimonials from '../common/Testimonials'

const appname = 'AI MindLadder';

// Section component for consistent styling
const Section = ({ children, bg = "white", title, id, ...props }) => {
    return (
        <Box width="100%" bg={bg} py={10} id={id} {...props}>
            <Box maxW="7xl" mx="auto" px={{ base: 4, md: 8 }}>
                {title && (
                    <Heading size="xl" mb={8} textAlign="center">
                        {title}
                    </Heading>
                )}
                {children}
            </Box>
        </Box>
    )
}

// Benefit component for the benefits section
const Benefit = ({ icon, title, description }) => {
    return (
        <VStack
            align="start"
            p={5}
            bg="white"
            borderRadius="lg"
            borderWidth="1px"
            borderColor="gray.200"
            shadow="md"
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
            height="100%"
        >
            <Icon as={icon} color="blue.500" boxSize={8} />
            <Heading size="md" mt={2}>{title}</Heading>
            <Text color="gray.600">{description}</Text>
        </VStack>
    )
}

// Step component for the how it works section
const Step = ({ number, title, description, icon }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })

    return (
        <HStack align="start" spacing={4} width="100%">
            <Flex
                align="center"
                justify="center"
                borderRadius="full"
                bg="blue.500"
                color="white"
                boxSize={isMobile ? "40px" : "50px"}
                fontWeight="bold"
                fontSize={isMobile ? "lg" : "xl"}
                flexShrink={0}
            >
                {number}
            </Flex>
            <VStack align="start" spacing={1}>
                <Heading size="md">{title}</Heading>
                <Text color="gray.600">{description}</Text>
            </VStack>
        </HStack>
    )
}

// Feature component for the features section
const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
            height="100%"
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

// Showcase component for examples
const Showcase = ({ title, description, imageSrc, altText }) => {
    return (
        <Box
            borderWidth="1px"
            borderRadius="lg"
            overflow="hidden"
            bg="white"
            shadow="md"
            style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
            }}
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
        >
            <Image
                src={imageSrc || "https://www.funblocks.net/img/portfolio/fullsize/mindladder_example.png"}
                alt={altText || "MindLadder Example"}
                width="100%"
                style={{
                    padding: 6
                }}
                objectFit="contain"
            />
            <Box p={5}>
                <Heading size="md" mb={2}>{title}</Heading>
                <Text color="gray.600">{description}</Text>
            </Box>
        </Box>
    )
}

const LayeredExplanationIntro = () => {
    const { t } = useTranslation('layered')
    const isMobile = useBreakpointValue({ base: true, md: false })

    // Hero features (short list for the hero section)
    const heroFeatures = [
        {
            icon: FaLayerGroup,
            title: t('layered_feature_1_title', '8-Tier Learning Ascent'),
            text: t('layered_feature_1_text', 'Journey through our unique progressive system—starting with simple analogies and climbing to sophisticated interconnections')
        },
        {
            icon: FaLightbulb,
            title: t('layered_feature_2_title', 'Layer-by-Layer Exploration'),
            text: t('layered_feature_2_text', 'Click any node to generate detailed explanations or expand branches for deeper understanding')
        },
        {
            icon: FaMountain,
            title: t('layered_feature_3_title', 'Summit Insights'),
            text: t('layered_feature_3_text', 'Capture the most important takeaways waiting at the highest levels of understanding')
        }
    ]

    // Expanded features for the dedicated features section
    const expandedFeatures = [
        {
            icon: FaLayerGroup,
            title: t('layered_expanded_feature_1_title', '8-Tier Progressive Learning'),
            text: t('layered_expanded_feature_1_text', 'Our unique 8-tier system guides you from simple analogies to expert insights, building knowledge systematically in layers that match how your brain naturally learns.')
        },
        {
            icon: FaRegLightbulb,
            title: t('layered_expanded_feature_2_title', 'Intuitive Visual Mapping'),
            text: t('layered_expanded_feature_2_text', 'Complex topics become clear through visual knowledge maps that show relationships between concepts, enhancing memory retention and comprehension.')
        },
        {
            icon: FaRocket,
            title: t('layered_expanded_feature_3_title', 'One-Click Generation'),
            text: t('layered_expanded_feature_3_text', 'Enter any concept and watch as AI instantly builds your complete knowledge ladder—no manual mapping or research required.')
        },
        {
            icon: FaNetworkWired,
            title: t('layered_expanded_feature_4_title', 'Interactive Exploration'),
            text: t('layered_expanded_feature_4_text', 'Click on any node to dive deeper, expand branches, or generate additional explanations—creating a personalized learning journey.')
        },
        {
            icon: FaRegObjectGroup,
            title: t('layered_expanded_feature_5_title', 'Multiple Perspectives'),
            text: t('layered_expanded_feature_5_text', 'See concepts from different angles with our multi-dimensional approach that presents varied viewpoints and frameworks for comprehensive understanding.')
        },
        {
            icon: FaUserGraduate,
            title: t('layered_expanded_feature_6_title', 'Expert Insights Access'),
            text: t('layered_expanded_feature_6_text', 'Reach cutting-edge understanding at the highest tiers, with expert-level insights and future directions normally reserved for specialists.')
        },
        {
            icon: MdInsights,
            title: t('layered_expanded_feature_7_title', 'Counterintuitive Revelations'),
            text: t('layered_expanded_feature_7_text', 'Discover surprising paradoxes and unexpected aspects of concepts that challenge assumptions and deepen your understanding.')
        },
        {
            icon: FaSitemap,
            title: t('layered_expanded_feature_8_title', 'Cross-Concept Connections'),
            text: t('layered_expanded_feature_8_text', 'Explore relationships between different topics with our interconnected knowledge system that reveals hidden links between seemingly unrelated ideas.')
        },
        {
            icon: FaRegComments,
            title: t('layered_expanded_feature_9_title', 'Plain Language Explanations'),
            text: t('layered_expanded_feature_9_text', 'Complex topics explained in clear, accessible language at lower tiers, with technical terminology introduced gradually as you ascend.')
        },
        {
            icon: FaRegClock,
            title: t('layered_expanded_feature_10_title', 'Time-Efficient Learning'),
            text: t('layered_expanded_feature_10_text', 'Reduce study time by up to 40% with our structured approach that eliminates confusion and builds solid foundations before tackling complex details.')
        },
        {
            icon: MdAutoAwesome,
            title: t('layered_expanded_feature_11_title', 'AI-Powered Adaptability'),
            text: t('layered_expanded_feature_11_text', 'Our advanced AI engine adapts to any subject matter, creating appropriate knowledge structures whether you are exploring quantum physics or literary analysis.')
        },
        {
            icon: MdOutlinePersonalInjury,
            title: t('layered_expanded_feature_12_title', 'Accessibility-Focused Design'),
            text: t('layered_expanded_feature_12_text', 'Designed for all learners with screen reader compatibility, keyboard navigation, color blindness considerations, and cognitive accessibility features.')
        }
    ]

    const learningBenefits = [
        t('layered_benefit_learning_1', 'Progressive disclosure builds knowledge systematically'),
        t('layered_benefit_learning_2', 'Multiple perspectives ensure comprehensive understanding'),
        t('layered_benefit_learning_3', 'Visual mapping improves memory and concept association'),
        t('layered_benefit_learning_4', 'Challenge-based learning through counterintuitive aspects')
    ]

    const practicalBenefits = [
        {
            icon: FaGraduationCap,
            title: t('layered_benefit_practical_1', 'Students scaling challenging academic concepts')
        },
        {
            icon: FaBriefcase,
            title: t('layered_benefit_practical_2', 'Professionals stepping through new industry knowledge')
        },
        {
            icon: FaChalkboardTeacher,
            title: t('layered_benefit_practical_3', 'Teachers building comprehension ladders for students')
        },
        {
            icon: FaFlask,
            title: t('layered_benefit_practical_4', 'Researchers exploring layered relationships between ideas')
        }
    ]

    const howItWorksSteps = [
        {
            number: 1,
            title: t('layered_how_it_works_step_1_title', 'Enter any concept'),
            description: t('layered_how_it_works_step_1_text', 'From quantum physics to financial markets—any topic can be transformed')
        },
        {
            number: 2,
            title: t('layered_how_it_works_step_2_title', 'AI constructs your knowledge ladder'),
            description: t('layered_how_it_works_step_2_text', 'Watch as 8 levels of progressive understanding are built before your eyes')
        },
        {
            number: 3,
            title: t('layered_how_it_works_step_3_title', 'Climb interactively'),
            description: t('layered_how_it_works_step_3_text', 'Click on any node to explore deeper explanations and branch into related concepts')
        },
        {
            number: 4,
            title: t('layered_how_it_works_step_4_title', 'Ascend to mastery'),
            description: t('layered_how_it_works_step_4_text', 'Reach complete understanding through branching options like "Breakdown," "Examples," or "Brainstorm"')
        }
    ]

    const faqItems = [
        {
            question: t('layered_faq_1_question', 'What makes MindLadder different from other learning tools?'),
            answer: t('layered_faq_1_answer', 'MindLadder\'s unique 8-tier progressive learning system sets it apart from traditional learning tools. While most educational resources present information in a linear fashion, MindLadder builds knowledge in layers—starting with intuitive analogies and gradually ascending to sophisticated interconnections. This structured approach mimics how the human brain naturally builds understanding, making complex topics more accessible and memorable.')
        },
        {
            question: t('layered_faq_2_question', 'Can MindLadder help with any subject?'),
            answer: t('layered_faq_2_answer', 'Yes! MindLadder is designed to work with virtually any concept or subject matter. Whether you\'re tackling quantum physics, financial markets, historical events, literary analysis, or programming concepts, our AI engine adapts to create appropriate knowledge ladders. The system excels at breaking down complex topics into manageable layers regardless of the field.')
        },
        {
            question: t('layered_faq_3_question', 'How does the 8-tier system work?'),
            answer: t('layered_faq_3_answer', 'Our 8-tier system creates a progressive journey through any concept: 1) Simple Analogy - relates the concept to everyday experiences, 2) Basic Definition - provides clear fundamental explanation, 3) Key Components - breaks down main elements, 4) Functional Relationships - shows how components interact, 5) Contextual Applications - demonstrates real-world usage, 6) Advanced Interconnections - reveals links to related concepts, 7) Counterintuitive Aspects - explores surprising or paradoxical elements, and 8) Expert Insights - presents cutting-edge understanding and future directions.')
        },
        {
            question: t('layered_faq_4_question', 'Is MindLadder suitable for students?'),
            answer: t('layered_faq_4_answer', 'Absolutely! MindLadder is an excellent tool for students at all levels. For K-12 students, it helps build foundational understanding of challenging subjects. College students can use it to master complex theories and concepts. Graduate students find it valuable for exploring interconnections between advanced topics. The progressive nature of the knowledge ladders makes it adaptable to any educational level.')
        },
        {
            question: t('layered_faq_5_question', 'Can teachers use MindLadder in their curriculum?'),
            answer: t('layered_faq_5_answer', 'Yes, MindLadder is a powerful resource for educators. Teachers can use it to create comprehensive lesson plans that naturally progress from basic to advanced understanding. It\'s excellent for designing differentiated instruction that meets students at their current level while providing clear pathways to deeper knowledge. The visual nature of the mind maps also makes complex topics more engaging and accessible for diverse learning styles.')
        },
        {
            question: t('layered_faq_6_question', 'How accurate is the information provided by MindLadder?'),
            answer: t('layered_faq_6_answer', 'MindLadder is powered by advanced AI that draws on extensive knowledge bases to create accurate, up-to-date information. Our system is designed to present well-established facts and theories while clearly indicating areas of ongoing research or debate. However, as with any AI tool, we recommend verifying critical information with authoritative sources, especially for specialized professional or academic applications.')
        },
        {
            question: t('layered_faq_7_question', 'How does MindLadder compare to traditional mind mapping tools?'),
            answer: t('layered_faq_7_answer', 'Unlike traditional mind mapping tools that require you to manually create connections, MindLadder uses AI to automatically generate comprehensive knowledge structures. Traditional tools typically create flat, single-level maps, while MindLadder builds multi-dimensional ladders with 8 distinct cognitive layers. Our system also dynamically expands based on your interests—each node can be explored further with additional ladders, creating a truly interactive learning experience that traditional static mind maps cannot match.')
        },
        {
            question: t('layered_faq_8_question', 'Can MindLadder help with exam preparation and study efficiency?'),
            answer: t('layered_faq_8_answer', 'Absolutely! MindLadder is an excellent exam preparation tool that significantly improves study efficiency. By organizing information in progressive layers, it helps you build a solid foundation before tackling complex details—exactly what\'s needed for comprehensive exam preparation. The visual nature of the knowledge ladders enhances memory retention, while the interactive exploration helps identify and strengthen weak areas in your understanding. Many students report cutting their study time by 30-40% while achieving better comprehension using our structured approach.')
        },
        {
            question: t('layered_faq_9_question', 'Does MindLadder work for visual learners and those with different learning styles?'),
            answer: t('layered_faq_9_answer', 'MindLadder is designed to accommodate multiple learning styles, making it particularly effective for visual learners. The graphical representation of knowledge ladders provides clear visual pathways through complex topics. For auditory learners, the detailed explanations at each level can be read aloud using screen readers. Kinesthetic learners benefit from the interactive nature of clicking through and expanding different branches based on their interests. This multi-modal approach ensures that regardless of your preferred learning style, MindLadder offers an engaging and effective educational experience.')
        },
        {
            question: t('layered_faq_10_question', 'How does MindLadder help with knowledge retention and long-term memory?'),
            answer: t('layered_faq_10_answer', 'MindLadder significantly enhances knowledge retention through several cognitive science principles. First, the progressive disclosure approach aligns with how our brains naturally build understanding, creating stronger neural connections. Second, the visual mapping of concepts creates spatial memory associations that improve recall. Third, the interactive exploration engages active learning, which is proven to enhance retention compared to passive reading. Finally, by connecting new information to existing knowledge through analogies and relationships, MindLadder leverages the power of associative memory, making complex concepts stick for the long term.')
        },
        {
            question: t('layered_faq_11_question', 'Can MindLadder be used for professional development and workplace learning?'),
            answer: t('layered_faq_11_answer', 'MindLadder is an invaluable tool for professional development and workplace learning. Organizations use it to onboard new employees by creating comprehensive knowledge ladders for company processes, products, and industry concepts. Teams use it to break down complex projects into manageable components with clear relationships. For individual professional development, MindLadder helps quickly master new skills and industry trends by providing both high-level overviews and detailed deep dives. The ability to share knowledge ladders also facilitates knowledge transfer between team members and departments.')
        },
        {
            question: t('layered_faq_12_question', 'Is MindLadder accessible for users with disabilities?'),
            answer: t('layered_faq_12_answer', 'Yes, accessibility is a core priority for MindLadder. Our interface is designed to be compatible with screen readers for visually impaired users, with all nodes and connections properly labeled for audio navigation. We support keyboard navigation for users with motor limitations. The color schemes are tested for color blindness compatibility, and we offer high-contrast modes. The progressive nature of our knowledge ladders is particularly helpful for users with cognitive disabilities, as it breaks complex topics into manageable chunks. We continuously work with accessibility experts to ensure our platform remains inclusive for all users.')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section with Screenshot */}
            <Section bg="gray.50">
                <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={8} width="100%">
                    <GridItem>
                        <VStack align="start" spacing={6}>
                            <HStack flexWrap="wrap" spacing={2}>
                                <Badge colorScheme="blue" fontSize="md" px={3} py={1} borderRadius="full" mb={2}>
                                    AI-Powered
                                </Badge>
                                <Badge colorScheme="blue" fontSize="md" px={3} py={1} borderRadius="full" mb={2}>
                                    Feynman Technique
                                </Badge>
                                <Badge colorScheme="blue" fontSize="md" px={3} py={1} borderRadius="full" mb={2}>
                                    Interactive Learning
                                </Badge>
                                <Badge colorScheme="blue" fontSize="md" px={3} py={1} borderRadius="full" mb={2}>
                                    Mindmap Generator
                                </Badge>
                            </HStack>
                            {/* <Heading size="xl" bgGradient="linear(to-r, blue.400, purple.500)" bgClip="text"> */}
                            <Text fontSize="lg" color="gray.700">
                                {t('layered_intro_description', 'AI MindLadder is a revolutionary learning tool that transforms how you understand complex concepts. With just a single input, our advanced AI engine creates comprehensive, multi-layered mind maps that guide you through any topic—from intuitive basics to profound insights.')}
                            </Text>
                            <List spacing={3}>
                                {heroFeatures.map((feature, idx) => (
                                    <ListItem key={idx} display="flex" alignItems="center">
                                        <ListIcon as={feature.icon} color="purple.500" />
                                        <Text fontWeight="medium">{feature.title}</Text>
                                    </ListItem>
                                ))}
                            </List>
                            <HStack spacing={4} mt={4}>
                                <Link href="#features">
                                    <Button colorScheme="purple" size="lg">
                                        {t('layered_explore_features', 'Explore Features')}
                                    </Button>
                                </Link>
                                <Link href="#how-it-works">
                                    <Button rightIcon={<FaArrowRight />} colorScheme="blue" size="lg" variant="outline">
                                        {t('layered_how_it_works_title', 'See How It Works')}
                                    </Button>
                                </Link>
                            </HStack>
                        </VStack>
                    </GridItem>
                    <GridItem display="flex" justifyContent="center" alignItems="center">
                        <Image
                            backgroundColor={'white'}
                            padding={2}
                            src="https://www.funblocks.net/img/portfolio/thumbnails/aitools_mindladder.png"
                            alt="MindLadder Interface"
                            borderRadius="lg"
                            shadow="xl"
                            fallbackSrc="https://via.placeholder.com/500x300?text=MindLadder+Screenshot"
                        />
                    </GridItem>
                </Grid>
            </Section>

            {/* Features Section */}
            <Section bg="white" title={t('layered_features_title', 'Powerful Features for Enhanced Learning')} id="features">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mx="auto"
                    mb={10}
                >
                    {t('layered_features_description', 'MindLadder combines cutting-edge AI with cognitive science principles to deliver a revolutionary learning experience through these powerful features:')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8}>
                    {expandedFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="gray.50" title={t('layered_benefits_title', 'The Science-Backed Learning Approach')} id="benefits">
                <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={8}>
                    <GridItem>
                        <VStack align="start" spacing={4}>
                            <Heading size="md" color="blue.600">
                                {t('layered_benefits_learning_title', 'Enhanced Learning')}
                            </Heading>
                            <List spacing={3}>
                                {learningBenefits.map((benefit, idx) => (
                                    <ListItem key={idx} display="flex" alignItems="center">
                                        <ListIcon as={FaLightbulb} color="blue.500" />
                                        <Text>{benefit}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </GridItem>
                    <GridItem>
                        <VStack align="start" spacing={4}>
                            <Heading size="md" color="purple.600">
                                {t('layered_benefits_practical_title', 'Practical Applications')}
                            </Heading>
                            <List spacing={3}>
                                {practicalBenefits.map((benefit, idx) => (
                                    <ListItem key={idx} display="flex" alignItems="center">
                                        <ListIcon as={benefit.icon} color="purple.500" />
                                        <Text>{benefit.title}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </GridItem>
                </Grid>
            </Section>

            {/* How It Works Section */}
            <Section bg="white" title={t('layered_how_it_works_title', 'How It Works')} id="how-it-works">
                <VStack spacing={8} width="100%">
                    {howItWorksSteps.map((step, idx) => (
                        <Step
                            key={idx}
                            number={step.number}
                            title={step.title}
                            description={step.description}
                        />
                    ))}
                </VStack>
            </Section>

            {/* Feynman Technique Section */}
            <Section bg="blue.50">
                <VStack spacing={6} textAlign="center" maxW="3xl" mx="auto">
                    <Icon as={FaBook} boxSize={12} color="blue.500" />
                    <Heading size="lg">{t('layered_feynman_title', 'The Feynman Technique, Elevated')}</Heading>
                    <Text fontSize="lg" color="gray.700">
                        {t('layered_feynman_description', 'Named after the legendary physicist Richard Feynman—known for his ability to explain complex topics simply—our app embodies his educational philosophy. We\'ve enhanced this approach with cutting-edge AI to create a tool that guides you through progressive levels of comprehension.')}
                    </Text>
                </VStack>
            </Section>

            {/* Examples Section */}
            <Section bg="white" id="examples">
                <VStack spacing={8} mb={12}>
                    <Heading
                        as="h2"
                        size="xl"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.500)"
                        bgClip="text"
                    >
                        {t('case_studies_title', 'Success Stories: MindLadder in Action')}
                    </Heading>
                    <Text fontSize="lg" textAlign="center" maxW="3xl">
                        {t('case_studies_description', 'See how organizations and individuals are using MindLadder to transform learning experiences and achieve breakthrough results across different domains.')}
                    </Text>

                    <HStack spacing={6} justify="center" wrap="wrap">
                        <Flex align="center" bg="blue.50" px={4} py={2} borderRadius="full">
                            <Icon as={FaGraduationCap} color="blue.500" mr={2} />
                            <Text fontWeight="medium" fontSize="sm">{t('case_studies_category_education', 'Education')}</Text>
                        </Flex>
                        <Flex align="center" bg="purple.50" px={4} py={2} borderRadius="full">
                            <Icon as={FaBriefcase} color="purple.500" mr={2} />
                            <Text fontWeight="medium" fontSize="sm">{t('case_studies_category_professional', 'Professional')}</Text>
                        </Flex>
                        <Flex align="center" bg="green.50" px={4} py={2} borderRadius="full">
                            <Icon as={FaLaptopCode} color="green.500" mr={2} />
                            <Text fontWeight="medium" fontSize="sm">{t('case_studies_category_research', 'Research')}</Text>
                        </Flex>
                    </HStack>
                </VStack>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <Showcase
                        title={t('layered_example_1_title', 'Quantum Physics Ladder')}
                        description={t('layered_example_1_description', 'From basic particle behavior to quantum field theory, climb through increasingly sophisticated explanations of quantum mechanics')}
                        imageSrc="https://www.funblocks.net/img/portfolio/fullsize/aitools_mindladder_quantum.png"
                        altText="Quantum Physics MindLadder Example"
                    />
                    <Showcase
                        title={t('layered_example_2_title', 'Financial Markets Explained')}
                        description={t('layered_example_2_description', 'Ascend from simple supply and demand concepts to complex market dynamics and algorithmic trading strategies')}
                        imageSrc="https://www.funblocks.net/img/portfolio/fullsize/aitools_mindladder_financial_markets.png"
                        altText="Financial Markets MindLadder Example"
                    />
                </SimpleGrid>
            </Section>

            {/* Comparison Table Section */}
            <ComparisonTable
                namespace="layered"
                title={t("comparison_title")}
                description={t("comparison_description")}
                bg="blue.50"
                productName={appname}
                columns={[
                    { key: 'mindladder', label: t('comparison_mindladder', 'MindLadder'), highlight: true },
                    { key: 'traditional', label: t('comparison_traditional', 'Traditional Textbooks') },
                    { key: 'mindmap', label: t('comparison_mindmap', 'Standard Mind Maps') },
                    { key: 'online', label: t('comparison_online', 'Online Courses') }
                ]}
                features={[
                    {
                        name: t('comparison_feature_1', 'AI-Generated Content'),
                        tooltip: t('comparison_feature_1_tooltip', 'Content is automatically generated by artificial intelligence'),
                        mindladder: true,
                        traditional: false,
                        mindmap: false,
                        online: true,
                    },
                    {
                        name: t('comparison_feature_2', 'Progressive Complexity Layers'),
                        tooltip: t('comparison_feature_2_tooltip', 'Content is organized in increasing levels of complexity'),
                        mindladder: true,
                        traditional: false,
                        mindmap: false,
                        online: false,
                    },
                    {
                        name: t('comparison_feature_3', 'Interactive Exploration'),
                        tooltip: t('comparison_feature_3_tooltip', 'Users can click to explore concepts in more detail'),
                        mindladder: true,
                        traditional: false,
                        mindmap: true,
                        online: false,
                    },
                    {
                        name: t('comparison_feature_4', 'Visual Knowledge Mapping'),
                        tooltip: t('comparison_feature_4_tooltip', 'Information is presented in visual, connected format'),
                        mindladder: true,
                        traditional: false,
                        mindmap: true,
                        online: false,
                    },
                    {
                        name: t('comparison_feature_5', 'Personalized Learning Paths'),
                        tooltip: t('comparison_feature_5_tooltip', 'Content adapts based on user interests and interactions'),
                        mindladder: true,
                        traditional: false,
                        mindmap: false,
                        online: true,
                    },
                    {
                        name: t('comparison_feature_6', 'Multiple Perspectives'),
                        tooltip: t('comparison_feature_6_tooltip', 'Presents different viewpoints on the same topic'),
                        mindladder: true,
                        traditional: false,
                        mindmap: false,
                        online: false,
                    },
                    {
                        name: t('comparison_feature_7', 'Counterintuitive Insights'),
                        tooltip: t('comparison_feature_7_tooltip', 'Highlights surprising or paradoxical aspects of concepts'),
                        mindladder: true,
                        traditional: false,
                        mindmap: false,
                        online: false,
                    },
                    {
                        name: t('comparison_feature_8', 'Expert-Level Content'),
                        tooltip: t('comparison_feature_8_tooltip', 'Provides advanced, cutting-edge information'),
                        mindladder: true,
                        traditional: true,
                        mindmap: false,
                        online: true,
                    },
                    {
                        name: t('comparison_feature_9', 'Cross-Concept Connections'),
                        tooltip: t('comparison_feature_9_tooltip', 'Shows relationships between different concepts'),
                        mindladder: true,
                        traditional: false,
                        mindmap: true,
                        online: false,
                    },
                    {
                        name: t('comparison_feature_10', 'One-Click Generation'),
                        tooltip: t('comparison_feature_10_tooltip', 'Creates complete learning resources with a single input'),
                        mindladder: true,
                        traditional: false,
                        mindmap: false,
                        online: false,
                    },
                ]}
            />

            {/* Research Backed Section */}
            <ResearchBacked
                namespace="layered"
                title={t("research_title")}
                description={t("research_description")}
                bg="gray.50"
                productName={appname}
                buttons={[
                    { label: t('research_button_cognitive', 'Cognitive Science'), icon: FaBrain, colorScheme: 'blue' },
                    { label: t('research_button_educational', 'Educational Psychology'), icon: FaLightbulb, colorScheme: 'purple' },
                    { label: t('research_button_neuroscience', 'Learning Neuroscience'), icon: FaChartLine, colorScheme: 'green' }
                ]}
                researchAreas={[
                    {
                        title: t('research_area_1_title', 'Research Area 1'),
                        description: t('research_area_1_description', `Our product is built on solid research foundations in this area, providing significant benefits to users.`),
                        icon: FaBrain,
                        color: 'blue',
                        citations: [
                            {
                                text: 'Sweller, J. (2011). Cognitive load theory. Psychology of Learning and Motivation, 55, 37-76.',
                                url: 'https://doi.org/10.1016/B978-0-12-387691-1.00002-8'
                            },
                            {
                                text: 'Paas, F., & Ayres, P. (2014). Cognitive load theory: A broader view on the role of memory in learning and education. Educational Psychology Review, 26(2), 191-195.',
                                url: 'https://doi.org/10.1007/s10648-014-9263-5'
                            }
                        ]
                    },
                    {
                        title: t('research_area_2_title', 'Dual Coding Theory'),
                        description: t('research_area_2_description', 'By combining visual mind maps with textual explanations, MindLadder leverages dual coding theory to engage multiple cognitive channels, enhancing memory formation and recall.'),
                        icon: FaLightbulb,
                        color: 'purple',
                        citations: [
                            {
                                text: 'Paivio, A. (2014). Mind and its evolution: A dual coding theoretical approach. Psychology Press.',
                                url: 'https://doi.org/10.4324/9781315785233'
                            },
                            {
                                text: 'Clark, J. M., & Paivio, A. (1991). Dual coding theory and education. Educational Psychology Review, 3(3), 149-210.',
                                url: 'https://doi.org/10.1007/**********'
                            }
                        ]
                    },
                    {
                        title: t('research_area_3_title', 'Spaced Repetition'),
                        description: t('research_area_3_description', 'MindLadder\'s layered approach naturally implements spaced repetition principles, revisiting core concepts at increasing levels of complexity to optimize long-term retention.'),
                        icon: FaChartLine,
                        color: 'green',
                        citations: [
                            {
                                text: 'Kang, S. H. (2016). Spaced repetition promotes efficient and effective learning: Policy implications for instruction. Policy Insights from the Behavioral and Brain Sciences, 3(1), 12-19.',
                                url: 'https://doi.org/10.1177/2372732215624708'
                            },
                            {
                                text: 'Dunlosky, J., et al. (2013). Improving students\' learning with effective learning techniques: Promising directions from cognitive and educational psychology. Psychological Science in the Public Interest, 14(1), 4-58.',
                                url: 'https://doi.org/10.1177/1529100612453266'
                            }
                        ]
                    },
                    {
                        title: t('research_area_4_title', 'Desirable Difficulties'),
                        description: t('research_area_4_description', 'The counterintuitive aspects in MindLadder\'s higher tiers create "desirable difficulties" that enhance learning by requiring deeper processing and more effortful retrieval.'),
                        icon: FaBook,
                        color: 'orange',
                        citations: [
                            {
                                text: 'Bjork, R. A., & Bjork, E. L. (2020). Desirable difficulties in theory and practice. Journal of Applied Research in Memory and Cognition, 9(4), 475-479.',
                                url: 'https://doi.org/10.1016/j.jarmac.2020.09.003'
                            },
                            {
                                text: 'Soderstrom, N. C., & Bjork, R. A. (2015). Learning versus performance: An integrative review. Perspectives on Psychological Science, 10(2), 176-199.',
                                url: 'https://doi.org/10.1177/1745691615569000'
                            }
                        ]
                    }
                ]}
            />

            {/* Testimonials Section */}
            <Testimonials
                appname={appname}
                users={'12,000'}
                rating={4.8}
                bg="white"
                testimonials={[
                    {
                      content: t('testimonial_1_content', 'MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I\'ve cut my study time by 30% while improving my grades!'),
                      author: 'Emily Johnson',
                      role: t('testimonial_1_role', 'Medical Student'),
                      organization: 'Stanford University',
                      rating: 5,
                      image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                      content: t('testimonial_2_content', 'As a high school physics teacher, I\'ve been using MindLadder to create learning materials for my students. The 8-tier system is brilliant for gradually building understanding of difficult concepts like quantum mechanics. My students\' test scores have improved by 27% since implementing these knowledge ladders.'),
                      author: 'David Martinez',
                      role: t('testimonial_2_role', 'Physics Teacher'),
                      organization: 'Westlake High School',
                      rating: 5,
                      image: 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                      content: t('testimonial_3_content', 'Our engineering team uses MindLadder to onboard new hires to our complex software architecture. The visual nature combined with progressive complexity layers has reduced our onboarding time by 40%. It\'s now our standard tool for knowledge transfer across departments.'),
                      author: 'Sarah Chen',
                      role: t('testimonial_3_role', 'Engineering Director'),
                      organization: 'Tesla',
                      rating: 4.5,
                      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                      content: t('testimonial_4_content', 'As someone with ADHD, traditional learning resources often overwhelm me. MindLadder\'s visual approach and ability to explore concepts at my own pace has been a game-changer. For the first time, I can see how different ideas connect and build on each other.'),
                      author: 'Michael Rodriguez',
                      role: t('testimonial_4_role', 'Software Developer'),
                      organization: 'Freelance',
                      rating: 5,
                      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                      content: t('testimonial_5_content', 'I\'ve been using MindLadder to prepare for my MBA courses. The way it connects business concepts through multiple layers of understanding has given me an edge in class discussions. I especially appreciate how it highlights counterintuitive aspects that challenge my assumptions.'),
                      author: 'Jennifer Park',
                      role: t('testimonial_5_role', 'MBA Student'),
                      organization: 'Harvard Business School',
                      rating: 4.5,
                      image: 'https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    },
                    {
                      content: t('testimonial_6_content', 'Our research team uses MindLadder to map interdisciplinary connections between AI ethics and policy. The ability to start with simple analogies and build to expert-level insights has helped us communicate complex ideas to stakeholders from diverse backgrounds.'),
                      author: 'Dr. James Wilson',
                      role: t('testimonial_6_role', 'Research Director'),
                      organization: 'MIT Media Lab',
                      rating: 5,
                      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                    }
                  ]}
            />

            {/* How To Use Section */}
            <HowToUse />

            {/* FAQ Section */}
            <Section bg="white" title={t('layered_faq_title', 'Frequently Asked Questions')} id="faq">
                <Box maxW="4xl" mx="auto" width="100%">
                    <Accordion allowToggle>
                        {faqItems.map((item, idx) => (
                            <AccordionItem key={idx} borderWidth="1px" borderRadius="md" mb={4} borderColor="gray.200" overflow="hidden">
                                <h2>
                                    <AccordionButton py={4} _expanded={{ bg: 'blue.50' }}>
                                        <HStack flex="1" textAlign="left" spacing={4}>
                                            <Icon as={FaQuestionCircle} color="blue.500" />
                                            <Text fontWeight="medium" fontSize="lg">{item.question}</Text>
                                        </HStack>
                                        <AccordionIcon />
                                    </AccordionButton>
                                </h2>
                                <AccordionPanel pb={4} pt={2} bg="gray.50">
                                    <Text color="gray.700">{item.answer}</Text>
                                </AccordionPanel>
                            </AccordionItem>
                        ))}
                    </Accordion>
                </Box>
            </Section>

            {/* CTA Section */}
            <Section bg="blue.50">
                <VStack spacing={6} py={10}>
                    <Heading textAlign="center" size="xl">{t('layered_cta_title', 'Start Your Learning Revolution Today')}</Heading>
                    <Text fontSize="lg" textAlign="center" maxW="2xl">
                        {t('layered_cta_description', 'Transform how you learn with AI MindLadder—turning intimidating topics into manageable steps that build to complete understanding.')}
                    </Text>
                    <Button
                        size="lg"
                        colorScheme="blue"
                        height="60px"
                        px={10}
                        fontSize="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('layered_cta_button', 'Try AI MindLadder Now')}
                    </Button>
                    <Text fontSize="sm" color="gray.600">{t('layered_cta_no_card', 'No credit card required')}</Text>
                </VStack>
            </Section>
        </VStack>
    )
}

export default LayeredExplanationIntro
