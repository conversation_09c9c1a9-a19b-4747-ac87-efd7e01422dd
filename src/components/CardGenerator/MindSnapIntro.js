import React from 'react'
import { VStack, Heading, Text, SimpleGrid, Box, Flex, Button, Image, Badge, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, Icon, HStack, Divider, Link, Avatar, Stat, StatLabel, StatNumber, StatHelpText, Grid, GridItem, Center } from '@chakra-ui/react'
import { FaMagic, FaPalette, FaRegObjectGroup, FaLightbulb, FaBrain, FaChartLine, FaGraduationCap, FaUsers, FaUserTie, FaRegLightbulb, FaQuoteLeft, FaArrowRight, FaBook, FaVideo, FaFileAlt, FaCheck, FaTimes, FaChevronRight } from 'react-icons/fa'
import { MdAutoAwesome, MdCompareArrows } from 'react-icons/md'
import { useTranslation } from 'next-i18next'
import HowToUse from '../common/HowToUse'
import Section from '../common/Section'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

// Example showcase card component
const ExampleCard = ({ title, description, imageSrc, modelName, topic }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })

    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            overflow="hidden"
            h="100%"
            style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
            }}
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
        >
            <Image
                src={imageSrc}
                alt={title}
                // height={isMobile ? "240px" : "420px"}
                width="100%"
                style={{
                    padding: 6
                }}
                objectFit="contain"
                loading="lazy"
                sizes="(max-width: 768px) 100vw, 50vw"
            />
            <Box p={5}>
                <Flex justify="space-between" align="center" mb={2}>
                    <Heading size="md">{title}</Heading>
                    <Badge colorScheme="purple" px={2} py={1} borderRadius="full">
                        {modelName}
                    </Badge>
                </Flex>
                <Text color="gray.500" fontSize="sm" mb={2}>
                    Topic: {topic}
                </Text>
                <Text color="gray.600">{description}</Text>
            </Box>
        </Box>
    )
}

// Case Study Card Component
const CaseStudyCard = ({ name, role, company, text, result }) => {
    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            p={6}
            position="relative"
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
            display={'flex'}
            flexDirection={'column'}
        >
            <Icon as={FaQuoteLeft} w={8} h={8} color="blue.100" position="absolute" top={4} left={4} opacity={0.5} />
            <VStack align="start" spacing={4} pt={6} justifyContent={'space-between'} flex={1}>
                <Text fontSize="lg" fontStyle="italic" color="gray.700" pl={6}>
                    "{text}"
                </Text>
                <Flex width="100%" justify="space-between" align="center" wrap="wrap">
                    <Box>
                        <Flex align="center">
                            <Avatar size="md" name={name} bg="blue.500" color="white" mr={3} />
                            <Box>
                                <Text fontWeight="bold">{name}</Text>
                                <Text fontSize="sm" color="gray.600">{role}, {company}</Text>
                            </Box>
                        </Flex>
                    </Box>
                    {/* <Badge colorScheme="green" px={3} py={2} borderRadius="full" mt={{ base: 4, md: 0 }}>
                        {result}
                    </Badge> */}
                </Flex>
            </VStack>
        </Box>
    )
}

// Comparison Item Component
const ComparisonItem = ({ text, isPositive }) => {
    return (
        <Flex align="center" mb={3}>
            <Flex
                w={6}
                h={6}
                align="center"
                justify="center"
                rounded="full"
                bg={isPositive ? "green.100" : "red.100"}
                color={isPositive ? "green.500" : "red.500"}
                mr={3}
                flexShrink={0}
            >
                <Icon as={isPositive ? FaCheck : FaTimes} w={3} h={3} />
            </Flex>
            <Text>{text}</Text>
        </Flex>
    )
}

// Resource Card Component
const ResourceCard = ({ title, description, icon, linkText, linkUrl }) => {
    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            p={5}
            transition="all 0.3s"
            _hover={{ transform: 'translateY(-3px)', shadow: 'md', borderColor: 'blue.300' }}
        >
            <Flex direction="column" height="100%">
                <Flex align="center" mb={3}>
                    <Flex
                        w={10}
                        h={10}
                        align="center"
                        justify="center"
                        rounded="full"
                        bg="blue.50"
                        color="blue.500"
                        mr={3}
                    >
                        <Icon as={icon} w={5} h={5} />
                    </Flex>
                    <Heading size="sm">{title}</Heading>
                </Flex>
                <Text color="gray.600" mb={4} flex="1">
                    {description}
                </Text>
                <Button
                    rightIcon={<FaChevronRight />}
                    variant="outline"
                    colorScheme="blue"
                    size="sm"
                    alignSelf="flex-start"
                    onClick={() => window.open(linkUrl, '_blank')}
                >
                    {linkText}
                </Button>
            </Flex>
        </Box>
    )
}

const MindSnapIntro = () => {
    const { t } = useTranslation('mindsnap')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaMagic,
            title: t('mindsnap_feature_1_title'),
            text: t('mindsnap_feature_1_text')
        },
        {
            icon: MdAutoAwesome,
            title: t('mindsnap_feature_2_title'),
            text: t('mindsnap_feature_2_text')
        },
        {
            icon: FaPalette,
            title: t('mindsnap_feature_3_title'),
            text: t('mindsnap_feature_3_text')
        },
        {
            icon: FaRegObjectGroup,
            title: t('mindsnap_feature_4_title'),
            text: t('mindsnap_feature_4_text')
        }
    ]

    const benefits = [
        {
            icon: FaBrain,
            title: t('mindsnap_benefit_1'),
            text: t('mindsnap_benefit_1_text')
        },
        {
            icon: FaChartLine,
            title: t('mindsnap_benefit_2'),
            text: t('mindsnap_benefit_2_text')
        },
        {
            icon: FaLightbulb,
            title: t('mindsnap_benefit_3'),
            text: t('mindsnap_benefit_3_text')
        }
    ]

    const useCases = [
        {
            icon: FaGraduationCap,
            title: t('use_case_1_title'),
            text: t('use_case_1_text')
        },
        {
            icon: FaUserTie,
            title: t('use_case_2_title'),
            text: t('use_case_2_text')
        },
        {
            icon: FaRegLightbulb,
            title: t('use_case_3_title'),
            text: t('use_case_3_text')
        },
        {
            icon: FaUsers,
            title: t('use_case_4_title'),
            text: t('use_case_4_text')
        }
    ]

    // Examples data
    const examples = [
        {
            title: t('mindsnap_example_1_title'),
            description: t('mindsnap_example_1_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_swot.png",
            modelName: "SWOT Analysis",
            topic: t('mindsnap_example_1_topic')
        },
        {
            title: t('mindsnap_example_2_title'),
            description: t('mindsnap_example_2_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_eisenhower_matrix.png",
            modelName: "Eisenhower Matrix",
            topic: t('mindsnap_example_2_topic')
        },
        {
            title: t('mindsnap_example_3_title'),
            description: t('mindsnap_example_3_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_six_hats.png",
            modelName: "Six Thinking Hats",
            topic: t('mindsnap_example_3_topic')
        },
        {
            title: t('mindsnap_example_4_title'),
            description: t('mindsnap_example_4_description'),
            imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_first_principle.png",
            modelName: "First Principles",
            topic: t('mindsnap_example_4_topic')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={isMobile ? 6 : 16} position="relative">
            {/* Mobile Sticky CTA - Only visible on mobile */}
            {isMobile && (
                <Box
                    position="fixed"
                    bottom="0"
                    left="0"
                    right="0"
                    zIndex="999"
                    bg="white"
                    p={3}
                    borderTopWidth="1px"
                    borderTopColor="gray.200"
                    shadow="lg"
                >
                    <Flex justify="center" gap={3}>
                        <Button
                            colorScheme="blue"
                            size="md"
                            flex="1"
                            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        >
                            {t('cta_try_now')}
                        </Button>
                        <Button
                            variant="outline"
                            colorScheme="blue"
                            size="md"
                            flex="1"
                            onClick={() => {
                                const examplesSection = document.getElementById('examples');
                                if (examplesSection) {
                                    examplesSection.scrollIntoView({ behavior: 'smooth' });
                                }
                            }}
                        >
                            {t('cta_see_examples')}
                        </Button>
                    </Flex>
                </Box>
            )}

            {/* Hero 部分 */}
            <Box
                w="100%"
                bgGradient="linear(to-br, blue.50, purple.50)"
                py={isMobile ? 12 : 16}
                px={4}
                borderBottomWidth="1px"
                borderBottomColor="gray.200"
            >
                <VStack spacing={8} maxW="4xl" mx="auto" textAlign="center">
                    <Badge colorScheme="blue" fontSize={isMobile ? "sm" : "md"} px={3} py={1} borderRadius="full">
                        {t('hero_badge')}
                    </Badge>

                    <Heading
                        fontSize={isMobile ? "3xl" : "5xl"}
                        fontWeight="extrabold"
                        bgGradient="linear(to-r, blue.400, purple.500)"
                        bgClip="text"
                        lineHeight="1.2"
                    >
                        {t('hero_heading')}
                    </Heading>

                    <Text fontSize={isMobile ? "lg" : "xl"} color="gray.600" maxW="2xl">
                        {t('hero_description')}
                    </Text>

                    <Flex
                        direction={isMobile ? "column" : "row"}
                        gap={4}
                        w={isMobile ? "100%" : "auto"}
                    >
                        <Button
                            colorScheme="blue"
                            size="lg"
                            height="60px"
                            px={8}
                            fontSize="md"
                            fontWeight="bold"
                            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        >
                            {t('cta_try_now')}
                        </Button>
                        <Button
                            variant="outline"
                            colorScheme="blue"
                            size="lg"
                            height="60px"
                            px={8}
                            fontSize="md"
                            fontWeight="bold"
                            onClick={() => {
                                const examplesSection = document.getElementById('examples');
                                if (examplesSection) {
                                    examplesSection.scrollIntoView({ behavior: 'smooth' });
                                }
                            }}
                        >
                            {t('cta_see_examples')}
                        </Button>
                        <Button
                            variant="ghost"
                            colorScheme="purple"
                            size="lg"
                            height="60px"
                            px={8}
                            fontSize="md"
                            fontWeight="bold"
                            onClick={() => {
                                const modelsSection = document.getElementById('mental-models');
                                if (modelsSection) {
                                    modelsSection.scrollIntoView({ behavior: 'smooth' });
                                }
                            }}
                        >
                            {t('cta_learn_more')}
                        </Button>
                    </Flex>

                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} w="100%" pt={6}>
                        <VStack>
                            <Heading fontSize="4xl" color="blue.500" fontWeight="bold">
                                {t('hero_metric_1_number')}
                            </Heading>
                            <Text color="gray.600">{t('hero_metric_1_text')}</Text>
                        </VStack>
                        <VStack>
                            <Heading fontSize="4xl" color="blue.500" fontWeight="bold">
                                {t('hero_metric_2_number')}
                            </Heading>
                            <Text color="gray.600">{t('hero_metric_2_text')}</Text>
                        </VStack>
                        <VStack>
                            <Heading fontSize="4xl" color="blue.500" fontWeight="bold">
                                {t('hero_metric_3_number')}
                            </Heading>
                            <Text color="gray.600">{t('hero_metric_3_text')}</Text>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Box>

            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('mindsnap_intro_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('mindsnap_benefits_title')}
                </Heading>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={10}>
                    {benefits.map((benefit, index) => (
                        <VStack key={index} align="start" spacing={4}>
                            <Flex
                                w={12}
                                h={12}
                                align="center"
                                justify="center"
                                color="white"
                                rounded="full"
                                bg="blue.500"
                            >
                                <Icon as={benefit.icon} boxSize={6} />
                            </Flex>
                            <Heading size="md">{benefit.title}</Heading>
                            <Text color="gray.600">{benefit.text}</Text>
                        </VStack>
                    ))}
                </SimpleGrid>
            </Section>

            {/* Use Cases */}
            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('mindsnap_use_cases_title')}
                </Heading>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {useCases.map((useCase, index) => (
                        <Box
                            key={index}
                            p={6}
                            borderRadius="lg"
                            bg="white"
                            boxShadow="md"
                            _hover={{ boxShadow: 'lg' }}
                            transition="all 0.3s"
                        >
                            <Flex align="center" mb={4}>
                                <Flex
                                    w={10}
                                    h={10}
                                    align="center"
                                    justify="center"
                                    color="white"
                                    rounded="full"
                                    bg="purple.500"
                                    mr={4}
                                >
                                    <Icon as={useCase.icon} boxSize={5} />
                                </Flex>
                                <Heading size="md">{useCase.title}</Heading>
                            </Flex>
                            <Text color="gray.600">{useCase.text}</Text>
                        </Box>
                    ))}
                </SimpleGrid>
            </Section>

            {/* Mental Models Section */}
            <Section bg="white" id="mental-models">
                <Heading
                    size="lg"
                    mb={isMobile ? 4 : 8}
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.500)"
                    bgClip="text"
                >
                    {t('mental_models_title')}
                </Heading>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('mental_models_description')}
                </Text>

                {/* Mental Model Categories */}
                <Box mb={8}>
                    <HStack spacing={4} justify="center" mb={6} wrap="wrap">
                        {['All', 'Decision Making', 'Problem Solving', 'Strategic Thinking', 'Creative Thinking', 'Systems Thinking'].map((category) => (
                            <Badge
                                key={category}
                                px={3}
                                py={2}
                                borderRadius="full"
                                colorScheme={category === 'All' ? "blue" : "gray"}
                                cursor="pointer"
                                _hover={{ bg: category === 'All' ? "blue.500" : "gray.300" }}
                                fontSize="sm"
                                fontWeight="medium"
                            >
                                {category}
                            </Badge>
                        ))}
                    </HStack>
                </Box>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                        <Box
                            key={i}
                            p={5}
                            borderRadius="lg"
                            bg="gray.50"
                            borderWidth="1px"
                            borderColor="gray.200"
                            _hover={{ borderColor: "blue.300", transform: "translateY(-2px)" }}
                            transition="all 0.3s"
                        >
                            <Heading size="sm" mb={2} color="blue.600">
                                {t(`mental_model_${i}_name`)}
                            </Heading>
                            <Text fontSize="sm" color="gray.600">
                                {t(`mental_model_${i}_description`)}
                            </Text>
                        </Box>
                    ))}
                </SimpleGrid>

                <Flex justify="center" mt={8} gap={4} wrap="wrap">
                    <Button
                        colorScheme="blue"
                        variant="solid"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        size="lg"
                    >
                        {t('try_with_mental_models')}
                    </Button>
                    <Button
                        colorScheme="blue"
                        variant="outline"
                        rightIcon={<FaChevronRight />}
                        size="lg"
                        onClick={() => window.open('https://www.funblocks.net/thinking-matters/category/classic-mental-models', '_blank')}
                    >
                        View All 1000+ Models
                    </Button>
                </Flex>

                <Box
                    mt={10}
                    p={6}
                    bg="blue.50"
                    borderRadius="xl"
                    borderWidth="1px"
                    borderColor="blue.100"
                    maxW="3xl"
                    mx="auto"
                >
                    <Flex align="center" mb={3}>
                        <Icon as={FaLightbulb} color="blue.500" mr={3} w={6} h={6} />
                        <Heading size="sm" color="blue.700">Did you know?</Heading>
                    </Flex>
                    <Text color="gray.700">
                        Mental models are frameworks that help us understand the world and make decisions.
                        By applying different mental models to the same problem, you can gain multiple perspectives
                        and discover insights you might otherwise miss. MindSnap makes these powerful thinking tools
                        accessible to everyone, no expertise required!
                    </Text>
                </Box>
            </Section>

            {/* Examples showcase */}
            <Section bg="gray.50" id="examples">
                <Heading
                    size="lg"
                    mb={8}
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.500)"
                    bgClip="text"
                >
                    {t('mindsnap_examples_title')}
                </Heading>

                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('mindsnap_examples_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    {examples.map((example, index) => (
                        <ExampleCard
                            key={index}
                            title={example.title}
                            description={example.description}
                            imageSrc={example.imageSrc}
                            modelName={example.modelName}
                            topic={example.topic}
                        />
                    ))}
                </SimpleGrid>

                <Box textAlign="center" mt={10}>
                    <Text fontSize="lg" fontWeight="medium" mb={4}>
                        {t('examples_subtitle')}
                    </Text>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('examples_button')}
                    </Button>
                </Box>
            </Section>

            {/* Case Studies Section */}
            <Section bg="white">
                <Heading
                    size="lg"
                    mb={4}
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, purple.500)"
                    bgClip="text"
                >
                    {t('case_studies_title')}
                </Heading>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('case_studies_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} width="100%">
                    <CaseStudyCard
                        name={t('case_study_1_name')}
                        role={t('case_study_1_role')}
                        company={t('case_study_1_company')}
                        text={t('case_study_1_text')}
                        result={t('case_study_1_result')}
                    />
                    <CaseStudyCard
                        name={t('case_study_2_name')}
                        role={t('case_study_2_role')}
                        company={t('case_study_2_company')}
                        text={t('case_study_2_text')}
                        result={t('case_study_2_result')}
                    />
                    <CaseStudyCard
                        name={t('case_study_3_name')}
                        role={t('case_study_3_role')}
                        company={t('case_study_3_company')}
                        text={t('case_study_3_text')}
                        result={t('case_study_3_result')}
                    />
                </SimpleGrid>

                <Box textAlign="center" mt={10}>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('cta_try_now')}
                    </Button>
                </Box>
            </Section>

            {/* Comparison Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={4} textAlign="center">
                    {t('comparison_title')}
                </Heading>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('comparison_description')}
                </Text>

                <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={8} maxW="4xl" mx="auto">
                    <GridItem>
                        <Box
                            bg="white"
                            rounded="xl"
                            shadow="md"
                            borderWidth="1px"
                            p={6}
                            height="100%"
                        >
                            <Heading size="md" mb={4} color="red.500">
                                {t('comparison_traditional_title')}
                            </Heading>
                            <Divider mb={4} />
                            <VStack align="stretch" spacing={2}>
                                <ComparisonItem text={t('comparison_traditional_1')} isPositive={false} />
                                <ComparisonItem text={t('comparison_traditional_2')} isPositive={false} />
                                <ComparisonItem text={t('comparison_traditional_3')} isPositive={false} />
                                <ComparisonItem text={t('comparison_traditional_4')} isPositive={false} />
                                <ComparisonItem text={t('comparison_traditional_5')} isPositive={false} />
                            </VStack>
                        </Box>
                    </GridItem>
                    <GridItem>
                        <Box
                            bg="white"
                            rounded="xl"
                            shadow="md"
                            borderWidth="1px"
                            p={6}
                            height="100%"
                            borderColor="blue.200"
                        >
                            <Heading size="md" mb={4} color="blue.500">
                                {t('comparison_mindsnap_title')}
                            </Heading>
                            <Divider mb={4} />
                            <VStack align="stretch" spacing={2}>
                                <ComparisonItem text={t('comparison_mindsnap_1')} isPositive={true} />
                                <ComparisonItem text={t('comparison_mindsnap_2')} isPositive={true} />
                                <ComparisonItem text={t('comparison_mindsnap_3')} isPositive={true} />
                                <ComparisonItem text={t('comparison_mindsnap_4')} isPositive={true} />
                                <ComparisonItem text={t('comparison_mindsnap_5')} isPositive={true} />
                            </VStack>
                        </Box>
                    </GridItem>
                </Grid>

                <Center mt={10}>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('cta_try_now')}
                    </Button>
                </Center>
            </Section>

            {/* Social Proof Section */}
            <Section bg="white">
                <Heading size="lg" mb={4} textAlign="center">
                    {t('social_proof_title')}
                </Heading>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('social_proof_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} maxW="3xl" mx="auto">
                    <Stat
                        px={4}
                        py={6}
                        bg="white"
                        shadow="md"
                        rounded="lg"
                        borderWidth="1px"
                        textAlign="center"
                    >
                        <StatNumber fontSize="4xl" fontWeight="bold" color="blue.500">
                            {t('social_proof_stat_1')}
                        </StatNumber>
                        <StatLabel fontSize="lg" mt={2}>
                            {t('social_proof_stat_1_label')}
                        </StatLabel>
                    </Stat>
                    <Stat
                        px={4}
                        py={6}
                        bg="white"
                        shadow="md"
                        rounded="lg"
                        borderWidth="1px"
                        textAlign="center"
                    >
                        <StatNumber fontSize="4xl" fontWeight="bold" color="blue.500">
                            {t('social_proof_stat_2')}
                        </StatNumber>
                        <StatLabel fontSize="lg" mt={2}>
                            {t('social_proof_stat_2_label')}
                        </StatLabel>
                    </Stat>
                    <Stat
                        px={4}
                        py={6}
                        bg="white"
                        shadow="md"
                        rounded="lg"
                        borderWidth="1px"
                        textAlign="center"
                    >
                        <StatNumber fontSize="4xl" fontWeight="bold" color="blue.500">
                            {t('social_proof_stat_3')}
                        </StatNumber>
                        <StatLabel fontSize="lg" mt={2}>
                            {t('social_proof_stat_3_label')}
                        </StatLabel>
                    </Stat>
                </SimpleGrid>
            </Section>

            {/* Resources Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={4} textAlign="center">
                    {t('resources_title')}
                </Heading>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={8}
                >
                    {t('resources_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6} width="100%">
                    <ResourceCard
                        title={t('resource_1_title')}
                        description={t('resource_1_description')}
                        icon={FaBook}
                        linkText={t('resource_1_link')}
                        linkUrl={'https://www.funblocks.net/thinking-matters/category/classic-mental-models'}
                    />
                    <ResourceCard
                        title={t('resource_2_title')}
                        description={t('resource_2_description')}
                        icon={FaFileAlt}
                        linkText={t('resource_2_link')}
                        linkUrl={'https://www.funblocks.net/thinking-matters/category/thinking-toolkit'}
                    />
                    <ResourceCard
                        title={t('resource_3_title')}
                        description={t('resource_3_description')}
                        icon={FaVideo}
                        linkText={t('resource_3_link')}
                        linkUrl={'https://youtu.be/lPfKL8mxgHQ'}
                    />
                </SimpleGrid>
            </Section>

            <HowToUse namespace="mindsnap" />

            {/* 常见问题 */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('mindsnap_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`mindsnap_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`mindsnap_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default MindSnapIntro
