import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon, Image, Button, Flex, Badge, HStack, Link } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaLightbulb, FaMagic, FaBook, FaYoutube, FaGlobe, FaUsers, FaHandPointer, FaGraduationCap, FaChartLine, FaBriefcase, FaLaptopCode, FaUserCheck, FaStar, FaArrowRight, FaUniversity, FaSchool, FaBuilding, FaRobot, FaCode, FaWandMagicSparkles, FaChrome } from 'react-icons/fa'
import { MdCheckCircle, MdCompare, MdPsychology, MdOutlineDesignServices, MdAutoFixHigh } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'
import Testimonials from '../common/Testimonials'
import CaseStudies from '../common/CaseStudies'
import ComparisonTable from '../common/ComparisonTable'
import ResearchBacked from '../common/ResearchBacked'
import NextLink from 'next/link'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="dodgerblue" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const PromptOptimizerIntro = () => {
    const { t } = useTranslation('prompt-optimizer')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const aiFeatures = [
        {
            icon: FaRobot,
            title: t('prompt_feature_1_title', 'AI-Powered Analysis'),
            text: t('prompt_feature_1_text', 'Advanced LLM technology analyzes your prompts to identify areas for improvement and optimization')
        },
        {
            icon: FaWandMagicSparkles,
            title: t('prompt_feature_2_title', 'Smart Optimization'),
            text: t('prompt_feature_2_text', 'Automatically enhances prompts with clear structure, specific requirements, and effective examples')
        },
        {
            icon: FaCode,
            title: t('prompt_feature_3_title', 'Best Practices'),
            text: t('prompt_feature_3_text', 'Incorporates proven prompt engineering techniques and patterns for optimal results')
        }
    ]

    const useCases = [
        {
            icon: FaGraduationCap,
            title: t('use_case_1_title', 'Education'),
            text: t('use_case_1_text', 'Help students and educators create effective prompts for learning and teaching')
        },
        {
            icon: FaChartLine,
            title: t('use_case_2_title', 'Business'),
            text: t('use_case_2_text', 'Optimize prompts for business analysis, market research, and decision-making')
        },
        {
            icon: FaBriefcase,
            title: t('use_case_3_title', 'Professional'),
            text: t('use_case_3_text', 'Enhance prompts for content creation, research, and professional writing')
        },
        {
            icon: FaLaptopCode,
            title: t('use_case_4_title', 'Development'),
            text: t('use_case_4_text', 'Improve prompts for coding, debugging, and technical documentation')
        }
    ]

    const caseStudies = [
        // {
        //     title: t('case_study_1_title', 'Educational Content Creation'),
        //     challenge: t('case_study_1_challenge', 'A university department struggled with creating effective prompts for AI-assisted course material development.'),
        //     solution: t('case_study_1_solution', 'Implemented Prompt Optimizer to enhance their prompts with clear learning objectives and structured requirements.'),
        //     results: t('case_study_1_results', 'Generated 40% more accurate and relevant course materials, reducing revision time by 60%.'),
        //     industry: t('case_study_1_industry', 'Education')
        // },
        {
            title: t('case_study_2_title', 'Market Research Enhancement'),
            challenge: t('case_study_2_challenge', 'A market research team needed to improve their AI prompts for better data analysis and insights.'),
            solution: t('case_study_2_solution', 'Used Prompt Optimizer to refine their prompts with specific analysis frameworks and output requirements.'),
            results: t('case_study_2_results', 'Achieved 35% more detailed market insights and reduced analysis time by 45%.'),
            industry: t('case_study_2_industry', 'Market Research')
        },
        {
            title: t('case_study_3_title', 'Content Creation Workflow'),
            challenge: t('case_study_3_challenge', 'A content creation agency needed to standardize their AI prompt process across multiple writers.'),
            solution: t('case_study_3_solution', 'Adopted Prompt Optimizer to create consistent, high-quality prompts for all team members.'),
            results: t('case_study_3_results', 'Improved content quality by 50% and reduced editing time by 40%.'),
            industry: t('case_study_3_industry', 'Content Creation')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <VStack spacing={isMobile ? 4 : 8} mb={6}>
                    <Heading
                        fontSize={isMobile ? '2xl' : '3xl'}
                        textAlign="center"
                        bgGradient="linear(to-l, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('prompt_intro_title', 'AI Prompt Optimizer: Enhance Your AI Interactions')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                    >
                        {t('prompt_intro_description', 'Transform your AI interactions with our advanced prompt optimization tool. Get clearer, more effective, and better-structured prompts for optimal results.')}
                    </Text>
                </VStack>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {aiFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Main Hero CTA Section */}
            <Section bg="blue.50">
                <VStack spacing={6} align="center" maxW="800px" mx="auto" py={4}>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        rounded="full"
                        px={8}
                        fontSize="lg"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('try_prompt_optimizer', 'Try Prompt Optimizer Now')} <Icon as={FaArrowRight} ml={2} />
                    </Button>
                    <Text textAlign="center" fontWeight="medium">{t('free_trial_offer', 'Start with a free trial')}</Text>
                </VStack>
            </Section>

            {/* How It Works Section */}
            <Section bg="white" title={t('how_it_works_title', 'How It Works')}>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={isMobile ? 4 : 8}
                >
                    {t('how_it_works_description', 'Our AI-powered prompt optimization process follows a systematic approach to enhance your prompts:')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="purple.500">{t('optimization_steps_title', 'Optimization Steps')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`optimization_step_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl">
                        <Heading size="md" color="purple.500">{t('optimization_principles_title', 'Key Principles')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`optimization_principle_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            {/* Use Cases Section */}
            <Section bg="gray.50" title={t('use_cases_title', 'Use Cases')}>
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={isMobile ? 4 : 8}
                >
                    {t('use_cases_description', 'Discover how Prompt Optimizer can enhance your AI interactions across various domains:')}
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8}>
                    {useCases.map((useCase, index) => (
                        <Feature
                            key={index}
                            icon={useCase.icon}
                            title={useCase.title}
                            text={useCase.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Comparison Table */}
            <ComparisonTable
                title={t("comparison_title", "Prompt Optimizer vs. Traditional Methods")}
                description={t("comparison_description", "See how our AI-powered prompt optimization compares to traditional methods.")}
                bg="white"
                productName="Prompt Optimizer"
                columns={[
                    { key: 'prompt_optimizer', label: t('comparison_optimizer', 'Prompt Optimizer'), highlight: true },
                    { key: 'manual', label: t('comparison_manual', 'Manual Optimization') },
                    { key: 'templates', label: t('comparison_templates', 'Basic Templates') }
                ]}
                features={[
                    {
                        name: t('comparison_feature_1', 'Analysis Depth'),
                        tooltip: t('comparison_feature_1_tooltip', 'Level of prompt analysis and optimization'),
                        prompt_optimizer: true,
                        manual: true,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_2', 'Best Practices'),
                        tooltip: t('comparison_feature_2_tooltip', 'Integration of prompt engineering best practices'),
                        prompt_optimizer: true,
                        manual: false,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_3', 'Structure'),
                        tooltip: t('comparison_feature_3_tooltip', 'Clear organization and structure'),
                        prompt_optimizer: true,
                        manual: true,
                        templates: true
                    },
                    {
                        name: t('comparison_feature_4', 'Examples'),
                        tooltip: t('comparison_feature_4_tooltip', 'Inclusion of relevant examples'),
                        prompt_optimizer: true,
                        manual: false,
                        templates: false
                    },
                    {
                        name: t('comparison_feature_5', 'Time Efficiency'),
                        tooltip: t('comparison_feature_5_tooltip', 'Time required for optimization'),
                        prompt_optimizer: true,
                        manual: false,
                        templates: true
                    }
                ]}
                id="comparison"
            />

            {/* Case Studies */}
            <CaseStudies
                caseStudies={caseStudies}
                description={t('case_studies_description', 'See how organizations have transformed their AI interactions with Prompt Optimizer.')}
                appname="Prompt Optimizer"
            />

            {/* Testimonials */}
            <Testimonials
                appname="Prompt Optimizer"
                rating="4.9"
                users="5,000+"
                testimonials={[
                    {
                        content: t('testimonial_1_quote', 'Prompt Optimizer has revolutionized how we interact with AI. The quality of our outputs has improved dramatically.'),
                        author: t('testimonial_1_author', 'Dr. Sarah Chen'),
                        role: t('testimonial_1_position', 'Research Director'),
                        organization: t('testimonial_1_company', 'Tech Research Institute'),
                        rating: 5
                    },
                    {
                        content: t('testimonial_2_quote', 'The structured approach to prompt optimization has made our AI interactions much more effective and efficient.'),
                        author: t('testimonial_2_author', 'Michael Johnson'),
                        role: t('testimonial_2_position', 'AI Integration Lead'),
                        organization: t('testimonial_2_company', 'Innovation Corp'),
                        rating: 5
                    },
                    {
                        content: t('testimonial_3_quote', 'As an educator, I\'ve seen significant improvements in how students interact with AI tools using optimized prompts.'),
                        author: t('testimonial_3_author', 'Prof. Emily Rodriguez'),
                        role: t('testimonial_3_position', 'University Professor'),
                        organization: t('testimonial_3_company', 'Global University'),
                        rating: 4.5
                    }
                ]}
                bg="white"
                id="testimonials"
            />

            {/* Research-Backed Section */}
            <ResearchBacked
                title={t("research_title", "Research-Backed Prompt Optimization")}
                description={t("research_description", "Our prompt optimization approach is built on solid research in AI interaction and prompt engineering.")}
                bg="gray.50"
                id="research"
                productName="Prompt Optimizer"
                researchAreas={[
                    {
                        title: t('research_area_1_title', 'Prompt Engineering'),
                        description: t('research_area_1_description', 'Research shows that well-structured prompts significantly improve AI output quality and relevance.'),
                        icon: FaRobot,
                        color: 'blue',
                        citations: [
                            {
                                text: 'Liu, P., et al. (2023). Pre-train, Prompt, and Predict: A Systematic Survey of Prompting Methods in Natural Language Processing.',
                                url: 'https://arxiv.org/abs/2107.13586'
                            }
                        ]
                    },
                    {
                        title: t('research_area_2_title', 'AI Interaction Design'),
                        description: t('research_area_2_description', 'Studies demonstrate that clear communication patterns lead to more effective human-AI collaboration.'),
                        icon: FaUsers,
                        color: 'purple',
                        citations: [
                            {
                                text: 'Amershi, S., et al. (2019). Guidelines for Human-AI Interaction.',
                                url: 'https://www.microsoft.com/en-us/research/publication/guidelines-for-human-ai-interaction/'
                            }
                        ]
                    }
                ]}
            />

            {/* How To Use Section */}
            <HowToUse namespace="prompt-optimizer" />

            {/* Chrome Extension Section */}
            <Section bg="white">
                <VStack spacing={6} align="center" maxW="4xl" mx="auto" py={8}>
                    <Heading
                        fontSize={isMobile ? '2xl' : '3xl'}
                        textAlign="center"
                        bgGradient="linear(to-l, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('chrome_extension_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                    >
                        {t('chrome_extension_description')}
                    </Text>
                    <Button
                        as="a"
                        href="https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh"
                        target="_blank"
                        rel="noopener noreferrer"
                        colorScheme="blue"
                        size="lg"
                        rounded="full"
                        px={8}
                        fontSize="lg"
                        leftIcon={<Icon as={FaChrome} />}
                    >
                        {t('chrome_extension_install')}
                    </Button>
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} width="100%" mt={4}>
                        {t('chrome_extension_features', { returnObjects: true }).map((feature, index) => (
                            <VStack
                                key={index}
                                align="start"
                                p={4}
                                bg="gray.50"
                                rounded="xl"
                                shadow="sm"
                                borderWidth="1px"
                                spacing={2}
                            >
                                <Icon as={MdCheckCircle} color="green.500" />
                                <Text color="gray.600">{feature}</Text>
                            </VStack>
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* FAQ Section */}
            <Section bg="gray.50">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`prompt_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`prompt_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>

            {/* Final CTA Section */}
            <Section bg="blue.50">
                <VStack spacing={6} align="center" maxW="800px" mx="auto" py={6}>
                    <Heading size="lg" textAlign="center" color="blue.600">
                        {t('ready_to_optimize', 'Ready to Optimize Your AI Interactions?')}
                    </Heading>
                    <Text textAlign="center" fontSize="lg">{t('final_cta_description', 'Start creating better prompts with Prompt Optimizer today.')}</Text>
                    <Button
                        colorScheme="blue"
                        size="lg"
                        rounded="full"
                        px={10}
                        fontSize="xl"
                        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                    >
                        {t('start_optimizing_now', 'Start Optimizing Now')} <Icon as={FaArrowRight} ml={2} />
                    </Button>
                    <HStack spacing={4} pt={2}>
                        <Icon as={FaUserCheck} color="green.500" />
                        <Text fontSize="sm" color="gray.600">{t('free_trial_message', 'Start with a free trial')}</Text>
                    </HStack>
                </VStack>
            </Section>
        </VStack>
    )
}

export default PromptOptimizerIntro 