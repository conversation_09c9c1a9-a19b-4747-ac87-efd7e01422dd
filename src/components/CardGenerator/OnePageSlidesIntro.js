import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaRocket, FaMagic, FaChartBar, FaCloud, FaPalette, FaBrain, FaShareAlt, FaLightbulb, FaClock, FaRegFileAlt } from 'react-icons/fa'
import { MdCheckCircle, MdPresentToAll, MdAutoAwesome, MdTimer, MdDesignServices, MdTrendingUp } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const OnePageSlidesIntro = () => {
    const { t } = useTranslation('oneslide')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const mainFeatures = [
        {
            icon: FaBrain,
            title: t('one_slides_ai_analysis_title'),
            text: t('one_slides_ai_analysis_text')
        },
        {
            icon: MdDesignServices,
            title: t('one_slides_design_title'),
            text: t('one_slides_design_text')
        },
        {
            icon: FaChartBar,
            title: t('one_slides_visualization_title'),
            text: t('one_slides_visualization_text')
        },
        {
            icon: FaClock,
            title: t('one_slides_efficiency_title'),
            text: t('one_slides_efficiency_text')
        }
    ]

    const useCases = [
        {
            icon: FaRegFileAlt,
            title: t('one_slides_usecase_1_title'),
            text: t('one_slides_usecase_1_text')
        },
        {
            icon: MdPresentToAll,
            title: t('one_slides_usecase_2_title'),
            text: t('one_slides_usecase_2_text')
        },
        {
            icon: MdTrendingUp,
            title: t('one_slides_usecase_3_title'),
            text: t('one_slides_usecase_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50" title={t('one_slides_hero_description')}>
                {/* <VStack spacing={isMobile ? 4 : 8} mb={isMobile ? 8 : 12}>
                    <Heading
                        fontSize={isMobile ? '2xl' : '3xl'}
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('one_slides_hero_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                    >
                        {t('one_slides_hero_description')}
                    </Text>
                </VStack> */}

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {mainFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Use Cases */}
            <Section bg="white" title={t('one_slides_usecases_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {useCases.map((useCase, index) => (
                        <Feature
                            key={index}
                            icon={useCase.icon}
                            title={useCase.title}
                            text={useCase.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits Section */}
            <Section bg="gray.50" title={t('one_slides_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl">
                        <Heading size="md" color="blue.500">{t('one_slides_benefits_pro_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`one_slides_pro_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                    <VStack align="start" spacing={4} p={6} bg="white" rounded="xl">
                        <Heading size="md" color="blue.500">{t('one_slides_benefits_business_title')}</Heading>
                        <List spacing={3}>
                            {[1, 2, 3, 4].map((i) => (
                                <ListItem key={i} display="flex" alignItems="center">
                                    <ListIcon as={MdCheckCircle} color="green.500" />
                                    <Text>{t(`one_slides_business_benefit_${i}`)}</Text>
                                </ListItem>
                            ))}
                        </List>
                    </VStack>
                </SimpleGrid>
            </Section>

            <HowToUse namespace="slides" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('one_slides_faq_title')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`one_slides_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`one_slides_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default OnePageSlidesIntro 