import { style } from '@mui/system';
import { useDrop } from 'react-dnd';
import { ItemTypes } from './ItemTypes';

export const DroppableContainer = ({ style, children, onDrop, dragOverColor, dragOverView }) => {
    const [{ canDrop, isOver }, drop] = useDrop(() => ({
        accept: ItemTypes.CARD,
        drop: (item, monitor) => {
            const didDrop = monitor.didDrop();
            if (didDrop) {
                return;
            }
            onDrop(item);
        },
        collect: (monitor) => ({
            isOver: monitor.isOver(),
            canDrop: monitor.canDrop(),
        }),
    }), [onDrop]);

    return (<div ref={drop} style={{ ...style,  backgroundColor: isOver ? dragOverColor :'transparent' }}>
        {isOver && dragOverView}
        {(!isOver || !dragOverView) && children}
    </div>);
};
