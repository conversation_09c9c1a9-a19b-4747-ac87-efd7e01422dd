import React from 'react';
import {
  Box,
  Heading,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Container,
  VStack,
  HStack,
  Badge,
  Icon,
  useColorModeValue,
  Tooltip,
  Flex
} from '@chakra-ui/react';
import { FaCheck, FaTimes, FaInfoCircle } from 'react-icons/fa';
import { useTranslation } from 'next-i18next';
import Section from './Section';

/**
 * Reusable ComparisonTable component
 * @param {Object} props - Component props
 * @param {string} props.namespace - Translation namespace (default: 'common')
 * @param {string} props.titleKey - Translation key for title (default: 'comparison_title')
 * @param {string} props.descriptionKey - Translation key for description (default: 'comparison_description')
 * @param {Array} props.features - Array of feature objects (optional, will use default if not provided)
 * @param {Array} props.columns - Array of column objects (optional, will use default if not provided)
 * @param {string} props.bg - Background color (default: 'white')
 * @param {string} props.id - Section ID for anchor links
 * @param {string} props.highlightColumn - Key of the column to highlight (default: first column)
 * @param {string} props.productName - Product name to use in default description (default: 'our product')
 */
const ComparisonTable = ({
  namespace = 'common',
  title = 'comparison_title',
  description = 'comparison_description',
  features: customFeatures,
  columns: customColumns,
  bg = 'white',
  id,
  highlightColumn,
  productName = 'our product'
}) => {
  const { t } = useTranslation(namespace);
  const headerBg = useColorModeValue('purple.100', 'purple.900');
  const highlightBg = useColorModeValue('purple.50', 'purple.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Default columns if none provided
  const defaultColumns = [
    { key: 'product', label: t('comparison_product', productName), highlight: true },
    { key: 'competitor1', label: t('comparison_competitor1', 'Competitor A') },
    { key: 'competitor2', label: t('comparison_competitor2', 'Competitor B') },
    { key: 'competitor3', label: t('comparison_competitor3', 'Competitor C') }
  ];

  // Default features if none provided
  const defaultFeatures = [
    {
      name: t('comparison_feature_1', 'AI-Generated Content'),
      tooltip: t('comparison_feature_1_tooltip', 'Content is automatically generated by artificial intelligence'),
      mindladder: true,
      traditional: false,
      mindmap: false,
      online: true,
    },
    {
      name: t('comparison_feature_2', 'Progressive Complexity Layers'),
      tooltip: t('comparison_feature_2_tooltip', 'Content is organized in increasing levels of complexity'),
      mindladder: true,
      traditional: false,
      mindmap: false,
      online: false,
    },
    {
      name: t('comparison_feature_3', 'Interactive Exploration'),
      tooltip: t('comparison_feature_3_tooltip', 'Users can click to explore concepts in more detail'),
      mindladder: true,
      traditional: false,
      mindmap: true,
      online: false,
    },
    {
      name: t('comparison_feature_4', 'Visual Knowledge Mapping'),
      tooltip: t('comparison_feature_4_tooltip', 'Information is presented in visual, connected format'),
      mindladder: true,
      traditional: false,
      mindmap: true,
      online: false,
    },
    {
      name: t('comparison_feature_5', 'Personalized Learning Paths'),
      tooltip: t('comparison_feature_5_tooltip', 'Content adapts based on user interests and interactions'),
      mindladder: true,
      traditional: false,
      mindmap: false,
      online: true,
    },
    {
      name: t('comparison_feature_6', 'Multiple Perspectives'),
      tooltip: t('comparison_feature_6_tooltip', 'Presents different viewpoints on the same topic'),
      mindladder: true,
      traditional: false,
      mindmap: false,
      online: false,
    },
    {
      name: t('comparison_feature_7', 'Counterintuitive Insights'),
      tooltip: t('comparison_feature_7_tooltip', 'Highlights surprising or paradoxical aspects of concepts'),
      mindladder: true,
      traditional: false,
      mindmap: false,
      online: false,
    },
    {
      name: t('comparison_feature_8', 'Expert-Level Content'),
      tooltip: t('comparison_feature_8_tooltip', 'Provides advanced, cutting-edge information'),
      mindladder: true,
      traditional: true,
      mindmap: false,
      online: true,
    },
    {
      name: t('comparison_feature_9', 'Cross-Concept Connections'),
      tooltip: t('comparison_feature_9_tooltip', 'Shows relationships between different concepts'),
      mindladder: true,
      traditional: false,
      mindmap: true,
      online: false,
    },
    {
      name: t('comparison_feature_10', 'One-Click Generation'),
      tooltip: t('comparison_feature_10_tooltip', 'Creates complete learning resources with a single input'),
      mindladder: true,
      traditional: false,
      mindmap: false,
      online: false,
    },
  ];

  // Use provided features/columns or fall back to defaults
  const featuresList = customFeatures || defaultFeatures;
  const columnsList = customColumns || defaultColumns;

  // Determine which column to highlight
  const highlightedColumn = highlightColumn || (columnsList.find(col => col.highlight)?.key || columnsList[0].key);

  return (
    <Section bg={bg} id={id}>
      <Container maxW="container.xl" px={{ base: 3, md: 6 }}>
        <VStack spacing={{ base: 6, md: 8 }} mb={{ base: 8, md: 10 }}>
          <Heading
            as="h2"
            size={{ base: "lg", md: "xl" }}
            textAlign="center"
            bgGradient="linear(to-r, blue.400, purple.500)"
            bgClip="text"
            px={{ base: 2, md: 0 }}
            lineHeight={{ base: "1.3", md: "1.2" }}
          >
            {title}
          </Heading>
          <Text
            fontSize={{ base: "sm", md: "lg" }}
            textAlign="center"
            maxW="3xl"
            px={{ base: 2, md: 0 }}
            color="gray.600"
            lineHeight="1.6"
          >
            {description}
          </Text>

          <Flex
            gap={{ base: 3, md: 4 }}
            flexWrap="wrap"
            justify="center"
            width="100%"
          >
            <Badge
              colorScheme="purple"
              p={{ base: 1.5, md: 2 }}
              borderRadius="md"
              fontSize={{ base: "xs", md: "md" }}
              textAlign="center"
            >
              {t('comparison_winner', 'Winner in Key Categories')}
            </Badge>
            <Badge
              colorScheme="blue"
              p={{ base: 1.5, md: 2 }}
              borderRadius="md"
              fontSize={{ base: "xs", md: "md" }}
              textAlign="center"
            >
              {t('comparison_value', 'Best Value')}
            </Badge>
          </Flex>
        </VStack>

        <Box
          overflowX="auto"
          width="100%"
          sx={{
            WebkitOverflowScrolling: 'touch',
            scrollbarWidth: 'thin',
            '&::-webkit-scrollbar': {
              width: '6px',
              height: '6px',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(0,0,0,0.2)',
              borderRadius: '3px',
            }
          }}
        >
          <Table
            variant="simple"
            size={{ base: "sm", md: "md" }}
            borderWidth="1px"
            borderColor={borderColor}
            borderRadius="lg"
            style={{ minWidth: '650px' }}
          >
            <Thead>
              <Tr bg={headerBg}>
                <Th
                  borderBottomWidth="2px"
                  fontSize={{ base: "xs", md: "sm" }}
                  py={{ base: 3, md: 4 }}
                  px={{ base: 2, md: 4 }}
                >
                  {t('comparison_feature', 'Feature')}
                </Th>
                {columnsList.map((column) => (
                  <Th
                    key={column.key}
                    borderBottomWidth="2px"
                    bg={column.key === highlightedColumn ? 'purple.100' : undefined}
                    color={column.key === highlightedColumn ? 'purple.800' : undefined}
                    fontSize={{ base: "xs", md: "sm" }}
                    py={{ base: 3, md: 4 }}
                    px={{ base: 2, md: 4 }}
                    textAlign="center"
                  >
                    {column.label}
                  </Th>
                ))}
              </Tr>
            </Thead>
            <Tbody>
              {featuresList.map((feature, idx) => (
                <Tr
                  key={idx}
                  bg={idx % 2 === 0 ? 'white' : 'gray.50'}
                  _hover={{ bg: 'gray.100' }}
                  transition="background 0.2s"
                >
                  <Td
                    fontWeight="medium"
                    py={{ base: 2, md: 3 }}
                    px={{ base: 2, md: 4 }}
                    fontSize={{ base: "xs", md: "sm" }}
                  >
                    <HStack spacing={{ base: 1, md: 2 }}>
                      <Text>{feature.name}</Text>
                      {feature.tooltip && (
                        <Tooltip
                          label={feature.tooltip}
                          placement="top"
                          fontSize={{ base: "xs", md: "sm" }}
                          hasArrow
                        >
                          <span><Icon as={FaInfoCircle} color="gray.400" boxSize={{ base: 3, md: 4 }} /></span>
                        </Tooltip>
                      )}
                    </HStack>
                  </Td>
                  {columnsList.map((column) => (
                    <Td
                      key={column.key}
                      bg={column.key === highlightedColumn ? highlightBg : undefined}
                      color={column.key === highlightedColumn ? 'purple.800' : undefined}
                      py={{ base: 2, md: 3 }}
                      px={{ base: 2, md: 4 }}
                      textAlign="center"
                    >
                      {feature[column.key] ?
                        <Icon as={FaCheck} color="green.500" boxSize={{ base: 4, md: 5 }} /> :
                        <Icon as={FaTimes} color="red.500" boxSize={{ base: 4, md: 5 }} />
                      }
                    </Td>
                  ))}
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      </Container>
    </Section>
  );
};

export default ComparisonTable;
