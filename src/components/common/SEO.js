import Head from 'next/head';

const SEO = ({ title, description, image, url, keywords, canonical }) => {
    return (
        <Head>
            <title>{title}</title>
            {
                description &&
                <meta name="description" content={description} />
            }
            {
                keywords &&
                <meta name="keywords" content={keywords} />
            }
            {/* Open Graph tags for social sharing */}
            <meta property="og:type" content="website" />
            <meta property="og:title" content={title} />
            {
                description &&
                <meta property="og:description" content={description} />
            }
            {
                image &&
                <meta property="og:image" content={image} />
            }
            {
                url &&
                <meta property="og:url" content={url} />
            }
            <meta property="og:site_name" content="FunBlocks AI" />

            {/* Twitter Card tags */}
            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:title" content={title} />
            {
                description &&
                <meta name="twitter:description" content={description} />
            }
            {
                image &&
                <meta name="twitter:image" content={image} />
            }

            {/* Canonical URL to prevent duplicate content issues */}
            {
                canonical &&
                <link rel="canonical" href={canonical} />
            }

            {/* Additional meta tags */}
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
            <meta name="robots" content="index, follow" />
        </Head>
    );
};

export default SEO;