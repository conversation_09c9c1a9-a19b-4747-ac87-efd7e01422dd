import { Box, Container, Heading, useBreakpointValue, VStack } from "@chakra-ui/react";

{/* <Heading
                    fontSize={isMobile ? '2xl' : '3xl'}
                    mb={isMobile ? 4 : 8}
                    textAlign="center"
                    bgGradient="linear(to-l, blue.400, purple.400)"
                    bgClip="text"
                ></Heading> */}

const Section = ({ title, children, bg = "white", gap, id, bgGradient }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })
    return (
        <Box id={id} bg={bg} py={'4rem'} alignItems={'center'} width={'100%'} bgGradient={bgGradient}>
            <VStack className="container" gap={gap} alignItems={'center'}>
                {
                    title &&
                    <Heading
                        fontSize={isMobile ? '2xl' : '3xl'}
                        mb={isMobile ? 4 : 8}
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {title}
                    </Heading>
                }

                {children}
            </VStack>
        </Box>
    )
}

export default Section;