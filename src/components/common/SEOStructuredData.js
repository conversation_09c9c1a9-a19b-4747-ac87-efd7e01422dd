import React from 'react';
import { useTranslation } from 'next-i18next';
import getConfig from 'next/config';

const SEOStructuredData = ({ faqs }) => {
  const { t } = useTranslation('common');
  const { basePath } = getConfig().publicRuntimeConfig;
  
  // Organization schema
  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    'name': 'FunBlocks AI',
    'url': 'https://www.funblocks.net',
    'logo': `${basePath}/icon.png`,
    'description': t('platform_description'),
    'sameAs': [
      'https://twitter.com/funblocks_ai',
      // 'https://www.linkedin.com/company/funblocksai'
    ]
  };

  // FAQ schema
  const faqSchema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    'mainEntity': faqs.map(faq => ({
      '@type': 'Question',
      'name': faq.question,
      'acceptedAnswer': {
        '@type': 'Answer',
        'text': faq.answer
      }
    }))
  };

  // Website schema
  const websiteSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    'url': 'https://www.funblocks.net/aitools',
    'name': 'FunBlocks AI Tools',
    'description': t('platform_meta_description'),
    // 'potentialAction': {
    //   '@type': 'SearchAction',
    //   'target': 'https://www.funblocks.net/search?q={search_term_string}',
    //   'query-input': 'required name=search_term_string'
    // }
  };

  return (
    <>
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
      />
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }}
      />
      {faqs && faqs.length > 0 && (
        <script 
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
        />
      )}
    </>
  );
};

export default SEOStructuredData;
