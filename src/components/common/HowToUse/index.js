import { Box, Text, Heading, VStack, HStack, List, ListItem, ListIcon, Icon, useBreakpointValue, Flex } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaRegLightbulb, FaArrowRight } from 'react-icons/fa'
import { MdCheckCircle } from 'react-icons/md'
import Section from '../Section'

const StepCard = ({ number, title, description, isLast }) => {
    const isMobile = useBreakpointValue({ base: true, md: false })

    return (
        <Flex
            position="relative"
            width="100%"
            align="center"
            direction={isMobile ? "column" : "row"}
            mb={isMobile ? 8 : 4}
        >
            {/* Step Number Circle */}
            <Flex
                minW="60px"
                h="60px"
                bg="purple.100"
                color="purple.700"
                rounded="full"
                justify="center"
                align="center"
                fontSize="xl"
                fontWeight="bold"
                zIndex={1}
                boxShadow="md"
            >
                {number}
            </Flex>

            {/* Content Box */}
            <Box
                flex={1}
                ml={isMobile ? 0 : 4}
                mt={isMobile ? 4 : 0}
                p={6}
                bg="white"
                rounded="xl"
                shadow="md"
                borderWidth="1px"
                position="relative"
            >
                <Text fontWeight="bold" fontSize="lg" mb={2}>{title}</Text>
                <Text color="gray.600">{description}</Text>
            </Box>

            {/* Connecting Arrow */}
            {/* {!isLast && !isMobile && (
                <Flex 
                    position="absolute" 
                    right="-20px"
                    top="50%"
                    transform="translateY(-50%)"
                    zIndex={2}
                >
                    <Icon as={FaArrowRight} w={6} h={6} color="purple.400" />
                </Flex>
            )} */}

            {/* Vertical Line for Mobile */}
            {!isLast && !isMobile && (
                <Box
                    position="absolute"
                    bottom="-24px"
                    left="30px"
                    width="2px"
                    height="24px"
                    bg="purple.200"
                />
            )}
        </Flex>
    )
}

const HowToUse = ({ namespace = 'mindmap', withTips = true }) => {
    const { t } = useTranslation('common')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const steps = [
        {
            number: 1,
            title: t(namespace + '_' + 'howto_step1_title'),
            description: t(namespace + '_' + 'howto_step1_description')
        },
        {
            number: 2,
            title: t(namespace + '_' + 'howto_step2_title'),
            description: t(namespace + '_' + 'howto_step2_description')
        },
        {
            number: 3,
            title: t(namespace + '_' + 'howto_step3_title'),
            description: t(namespace + '_' + 'howto_step3_description')
        },
        {
            number: 4,
            title: t(namespace + '_' + 'howto_step4_title'),
            description: t(namespace + '_' + 'howto_step4_description')
        }
    ]

    return (
        <Section bg="blue.50" title={t(namespace + '_' + 'howto_section_title')}>
            <Text
                fontSize={isMobile ? 'md' : 'lg'}
                color="gray.600"
                textAlign="center"
                maxW="2xl"
                mb={12}
            >
                {t(namespace + '_' + 'howto_section_description')}
            </Text>

            <Box width="100%" maxW="4xl" mx="auto" position="relative" px={isMobile ? 4 : 10}>
                {steps.map((step, index) => (
                    <StepCard
                        key={step.number}
                        number={step.number}
                        title={step.title}
                        description={step.description}
                        isLast={index === steps.length - 1}
                    />
                ))}
            </Box>

            {
                withTips &&
                <Box
                    mt={12}
                    p={6}
                    bg="yellow.50"
                    rounded="xl"
                    width="100%"
                    maxW="3xl"
                    borderWidth="1px"
                    borderColor="blue.200"
                >
                    <VStack align="start" spacing={4}>
                        <HStack>
                            <Icon as={FaRegLightbulb} color="blue.500" w={5} h={5} />
                            <Heading size="sm" color="blue.700">
                                {t(namespace + '_' + 'howto_tips_title')}
                            </Heading>
                        </HStack>
                        <List spacing={3}>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t(namespace + '_' + 'howto_tip_1')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t(namespace + '_' + 'howto_tip_2')}</Text>
                            </ListItem>
                            <ListItem display="flex" alignItems="center">
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text>{t(namespace + '_' + 'howto_tip_3')}</Text>
                            </ListItem>
                        </List>
                    </VStack>
                </Box>
            }

        </Section>
    )
}

export default HowToUse 