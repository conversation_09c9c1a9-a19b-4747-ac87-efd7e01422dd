import { Box, Heading, Text, SimpleGrid, Icon, VStack, Image, Flex, Badge, Button } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaArrowRight } from 'react-icons/fa'
import Section from './Section'

const CaseStudyCard = ({ icon, title, industry, challenge, solution, results, imageSrc, onClick }) => {
    return (
        <Box
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            overflow="hidden"
            h="100%"
            transition="transform 0.3s, box-shadow 0.3s"
            _hover={{ transform: 'translateY(-5px)', shadow: 'lg' }}
            display="flex"
            flexDirection="column"
            role="group"
            touchAction="manipulation"
        >
            <Flex
                p={{ base: 4, md: 5 }}
                alignItems="center"
                borderBottomWidth="1px"
                borderBottomColor="gray.100"
                bg="blue.50"
                flexWrap="wrap"
                gap={{ base: 2, md: 0 }}
            >
                <Icon
                    as={icon}
                    w={{ base: 5, md: 6 }}
                    h={{ base: 5, md: 6 }}
                    color="blue.500"
                    mr={3}
                    flexShrink={0}
                    _groupHover={{ transform: 'scale(1.1)' }}
                    transition="transform 0.2s"
                />
                <Box>
                    <Heading
                        size={{ base: "sm", md: "md" }}
                        lineHeight="shorter"
                    >
                        {title}
                    </Heading>
                    <Badge
                        colorScheme="purple"
                        mt={1}
                        fontSize={{ base: "xs", md: "sm" }}
                        px={{ base: 1.5, md: 2 }}
                        py={{ base: 0.5, md: 1 }}
                    >
                        {industry}
                    </Badge>
                </Box>
            </Flex>

            {imageSrc && (
                <Image
                    src={imageSrc}
                    alt={title}
                    height={{ base: "160px", sm: "180px", md: "240px", lg: "360px" }}
                    width="100%"
                    objectFit="contain"
                    loading="lazy"
                />
            )}

            <Box p={{ base: 4, md: 5 }} flex="1">
                <VStack align="start" spacing={{ base: 3, md: 4 }}>
                    <Box>
                        <Text
                            fontWeight="bold"
                            color="red.500"
                            fontSize={{ base: "sm", md: "md" }}
                        >
                            Challenge:
                        </Text>
                        <Text
                            color="gray.600"
                            fontSize={{ base: "sm", md: "md" }}
                            lineHeight="1.6"
                        >
                            {challenge}
                        </Text>
                    </Box>

                    <Box>
                        <Text
                            fontWeight="bold"
                            color="blue.500"
                            fontSize={{ base: "sm", md: "md" }}
                        >
                            Solution:
                        </Text>
                        <Text
                            color="gray.600"
                            fontSize={{ base: "sm", md: "md" }}
                            lineHeight="1.6"
                        >
                            {solution}
                        </Text>
                    </Box>

                    {
                        results &&
                        <Box>
                            <Text
                                fontWeight="bold"
                                color="green.500"
                                fontSize={{ base: "sm", md: "md" }}
                            >
                                Results:
                            </Text>
                            <Text
                                color="gray.600"
                                fontSize={{ base: "sm", md: "md" }}
                                lineHeight="1.6"
                            >
                                {results}
                            </Text>
                        </Box>
                    }

                </VStack>
            </Box>

            <Box
                p={{ base: 3, md: 4 }}
                borderTopWidth="1px"
                borderTopColor="gray.100"
                bg="gray.50"
                _groupHover={{ bg: "blue.50" }}
                transition="background 0.2s"
            >
                <Flex justify="flex-end">
                    <Button
                        rightIcon={<FaArrowRight />}
                        colorScheme="blue"
                        variant="ghost"
                        size={{ base: "sm", md: "md" }}
                        onClick={onClick ? onClick : () => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        height={{ base: "36px", md: "40px" }}
                        _groupHover={{ bg: "blue.100", transform: "translateX(4px)" }}
                        transition="all 0.2s"
                    >
                        Try it yourself
                    </Button>
                </Flex>
            </Box>
        </Box>
    )
}

const CaseStudies = ({ caseStudies, description, appname, onClick }) => {
    const { t } = useTranslation('common');

    return (
        <Section bg="gray.50" id="case-studies">
            <VStack spacing={{ base: 6, md: 8 }} width="100%" px={{ base: 3, md: 0 }}>
                <Badge
                    colorScheme="green"
                    fontSize={{ base: "sm", md: "md" }}
                    px={{ base: 2, md: 3 }}
                    py={{ base: 0.5, md: 1 }}
                    borderRadius="full"
                >
                    {t('case_studies_badge', 'Real-World Applications')}
                </Badge>

                <Heading
                    size={{ base: "md", md: "lg" }}
                    textAlign="center"
                    bgGradient="linear(to-r, blue.400, green.400)"
                    bgClip="text"
                    px={{ base: 2, md: 0 }}
                    lineHeight={{ base: "1.3", md: "1.2" }}
                >
                    {t('case_studies_title', 'Success Stories: {appname} in Action', { appname })}
                </Heading>

                <Text
                    fontSize={{ base: "sm", md: "lg" }}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={{ base: 2, md: 4 }}
                    px={{ base: 2, md: 0 }}
                    lineHeight="1.6"
                >
                    {description}
                </Text>

                <SimpleGrid
                    columns={{ base: 1, md: 2 }}
                    spacing={{ base: 5, md: 8 }}
                    width="100%"
                >
                    {caseStudies.map((study, index) => (
                        <CaseStudyCard
                            key={index}
                            icon={study.icon}
                            title={study.title}
                            industry={study.industry}
                            challenge={study.challenge}
                            solution={study.solution}
                            results={study.results}
                            imageSrc={study.imageSrc}
                            onClick={onClick}
                        />
                    ))}
                </SimpleGrid>

                <Box
                    textAlign="center"
                    mt={{ base: 4, md: 6 }}
                    width="100%"
                    px={{ base: 2, md: 0 }}
                >
                    <Text
                        fontSize={{ base: "md", md: "lg" }}
                        fontWeight="medium"
                        mb={{ base: 3, md: 4 }}
                        color="gray.700"
                    >
                        {t('case_studies_cta_text', 'Ready to create your own success story?')}
                    </Text>
                    <Button
                        colorScheme="blue"
                        size={{ base: "md", md: "lg" }}
                        rightIcon={<FaArrowRight />}
                        onClick={onClick ? onClick : () => window.scrollTo({ top: 0, behavior: 'smooth' })}
                        px={{ base: 5, md: 8 }}
                        py={{ base: 6, md: 6 }}
                        height={{ base: "48px", md: "auto" }}
                        borderRadius="full"
                        _hover={{ transform: 'translateY(-2px)' }}
                        transition="all 0.3s"
                    >
                        {t('case_studies_cta_button', 'Start Creating Now')}
                    </Button>
                </Box>
            </VStack>
        </Section>
    );
}

export default CaseStudies
