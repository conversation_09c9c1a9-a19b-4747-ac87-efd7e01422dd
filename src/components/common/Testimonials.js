import React from 'react';
import {
  Box,
  Heading,
  Text,
  SimpleGrid,
  VStack,
  HStack,
  Avatar,
  Icon,
  Container,
  useColorModeValue,
  Badge,
  Flex
} from '@chakra-ui/react';
import { FaQuoteLeft, FaStar, FaStarHalfAlt } from 'react-icons/fa';
import { useTranslation } from 'next-i18next';
import Section from './Section';

const TestimonialCard = ({ content, author, role, organization, rating, image }) => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const authorColor = useColorModeValue('gray.800', 'white');
  const metaColor = useColorModeValue('gray.500', 'gray.400');

  return (
    <Box
      bg={cardBg}
      p={{ base: 4, sm: 5, md: 6 }}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      boxShadow="md"
      position="relative"
      height="100%"
      display="flex"
      flexDirection="column"
      transition="all 0.3s"
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: 'lg',
      }}
    >
      <Icon
        as={FaQuoteLeft}
        position="absolute"
        top={{ base: 3, md: 4 }}
        left={{ base: 3, md: 4 }}
        color="purple.100"
        boxSize={{ base: 6, md: 8 }}
        opacity={0.6}
      />

      <Box
        mb={{ base: 4, md: 6 }}
        pt={{ base: 5, md: 6 }}
        pl={{ base: 5, md: 6 }}
        flex="1"
      >
        <Text
          fontSize={{ base: "sm", md: "md" }}
          fontStyle="italic"
          color={textColor}
          lineHeight="1.6"
        >
          "{content}"
        </Text>
      </Box>

      <Flex
        direction={{ base: "column", sm: "row" }}
        align={{ base: "flex-start", sm: "center" }}
        spacing={{ base: 2, md: 4 }}
        wrap="wrap"
        gap={{ base: 2, md: 3 }}
      >
        <HStack spacing={{ base: 3, md: 4 }} align="center" flex="1" minW="0">
          <Avatar
            size={{ base: "sm", md: "md" }}
            name={author}
            src={image}
            boxShadow="sm"
          />
          <Box minW="0">
            <Text
              fontWeight="bold"
              fontSize={{ base: "sm", md: "md" }}
              color={authorColor}
              noOfLines={1}
            >
              {author}
            </Text>
            <Text
              fontSize={{ base: "xs", md: "sm" }}
              color={metaColor}
              noOfLines={1}
            >
              {role}
            </Text>
            <Text
              fontSize={{ base: "2xs", md: "xs" }}
              color={metaColor}
              noOfLines={1}
            >
              {organization}
            </Text>
          </Box>
        </HStack>

        <HStack
          spacing={1}
          justify={{ base: "flex-start", sm: "flex-end" }}
          alignSelf={{ base: "flex-start", sm: "center" }}
          mt={{ base: 1, sm: 0 }}
        >
          {[...Array(Math.floor(rating))].map((_, i) => (
            <Icon key={i} as={FaStar} color="yellow.400" boxSize={{ base: 3, md: 4 }} />
          ))}
          {rating % 1 !== 0 && <Icon as={FaStarHalfAlt} color="yellow.400" boxSize={{ base: 3, md: 4 }} />}
        </HStack>
      </Flex>
    </Box>
  );
};

/**
 * Reusable Testimonials component
 * @param {Object} props - Component props
 * @param {string} props.namespace - Translation namespace (default: 'common')
 * @param {string} props.titleKey - Translation key for title (default: 'testimonials_title')
 * @param {string} props.descriptionKey - Translation key for description (default: 'testimonials_description')
 * @param {string} props.ratingKey - Translation key for rating text (default: 'testimonials_rating')
 * @param {string} props.usersKey - Translation key for users count text (default: 'testimonials_users')
 * @param {Array} props.testimonials - Array of testimonial objects (optional, will use default if not provided)
 * @param {string} props.bg - Background color (default: 'white')
 * @param {string} props.id - Section ID for anchor links
 */
const Testimonials = ({
  namespace = 'common',
  appname = 'FunBlocks AI',
  rating = '4.7',
  users = '15,000',
  testimonials: customTestimonials,
  bg = 'white',
  id
}) => {
  const { t } = useTranslation(namespace);

  // Default testimonials if none provided
  const defaultTestimonials = [
    {
      content: t('testimonial_1_content', 'MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I\'ve cut my study time by 30% while improving my grades!'),
      author: 'Emily Johnson',
      role: t('testimonial_1_role', 'Medical Student'),
      organization: 'Stanford University',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
    },
    {
      content: t('testimonial_2_content', 'As a high school physics teacher, I\'ve been using MindLadder to create learning materials for my students. The 8-tier system is brilliant for gradually building understanding of difficult concepts like quantum mechanics. My students\' test scores have improved by 27% since implementing these knowledge ladders.'),
      author: 'David Martinez',
      role: t('testimonial_2_role', 'Physics Teacher'),
      organization: 'Westlake High School',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
    },
    {
      content: t('testimonial_3_content', 'Our engineering team uses MindLadder to onboard new hires to our complex software architecture. The visual nature combined with progressive complexity layers has reduced our onboarding time by 40%. It\'s now our standard tool for knowledge transfer across departments.'),
      author: 'Sarah Chen',
      role: t('testimonial_3_role', 'Engineering Director'),
      organization: 'Tesla',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
    },
    {
      content: t('testimonial_4_content', 'As someone with ADHD, traditional learning resources often overwhelm me. MindLadder\'s visual approach and ability to explore concepts at my own pace has been a game-changer. For the first time, I can see how different ideas connect and build on each other.'),
      author: 'Michael Rodriguez',
      role: t('testimonial_4_role', 'Software Developer'),
      organization: 'Freelance',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
    },
    {
      content: t('testimonial_5_content', 'I\'ve been using MindLadder to prepare for my MBA courses. The way it connects business concepts through multiple layers of understanding has given me an edge in class discussions. I especially appreciate how it highlights counterintuitive aspects that challenge my assumptions.'),
      author: 'Jennifer Park',
      role: t('testimonial_5_role', 'MBA Student'),
      organization: 'Harvard Business School',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
    },
    {
      content: t('testimonial_6_content', 'Our research team uses MindLadder to map interdisciplinary connections between AI ethics and policy. The ability to start with simple analogies and build to expert-level insights has helped us communicate complex ideas to stakeholders from diverse backgrounds.'),
      author: 'Dr. James Wilson',
      role: t('testimonial_6_role', 'Research Director'),
      organization: 'MIT Media Lab',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
    }
  ];

  // Use provided testimonials or fall back to defaults
  const testimonialsList = customTestimonials || defaultTestimonials;

  return (
    <Section bg={bg} id={id}>
      <Container maxW="container.xl" px={{ base: 3, md: 6 }}>
        <VStack spacing={{ base: 6, md: 8 }} mb={{ base: 8, md: 12 }}>
          <Heading
            as="h2"
            size={{ base: "lg", md: "xl" }}
            textAlign="center"
            bgGradient="linear(to-r, blue.400, purple.500)"
            bgClip="text"
            px={{ base: 2, md: 0 }}
            lineHeight={{ base: "1.3", md: "1.2" }}
          >
            {t('testimonials_title', 'What Our Users Say')}
          </Heading>
          <Text
            fontSize={{ base: "md", md: "lg" }}
            textAlign="center"
            maxW="3xl"
            px={{ base: 2, md: 0 }}
            color="gray.600"
          >
            {t('testimonials_description', 'Join thousands of users who have transformed their experience with {appname}.', { appname })}
          </Text>

          <Flex
            direction={{ base: "column", sm: "row" }}
            spacing={{ base: 3, md: 4 }}
            gap={{ base: 3, md: 4 }}
            width="100%"
            justify="center"
            align="center"
            flexWrap="wrap"
          >
            <Badge
              colorScheme="green"
              p={{ base: 1.5, md: 2 }}
              borderRadius="md"
              fontSize={{ base: "sm", md: "md" }}
              width={{ base: "full", sm: "auto" }}
              textAlign="center"
            >
              <HStack justify={{ base: "center", sm: "flex-start" }}>
                <Icon as={FaStar} boxSize={{ base: 3, md: 4 }} />
                <Text>
                  {t('testimonials_rating', '{rating} average rating', { rating })}
                </Text>
              </HStack>
            </Badge>
            <Badge
              colorScheme="blue"
              p={{ base: 1.5, md: 2 }}
              borderRadius="md"
              fontSize={{ base: "sm", md: "md" }}
              width={{ base: "full", sm: "auto" }}
              textAlign="center"
            >
              {t('testimonials_users', '10,000+ active users', { users })}
            </Badge>
          </Flex>
        </VStack>

        <SimpleGrid
          columns={{ base: 1, md: 2, lg: 3 }}
          spacing={{ base: 5, md: 8 }}
          width="100%"
        >
          {testimonialsList.map((testimonial, idx) => (
            <TestimonialCard key={idx} {...testimonial} />
          ))}
        </SimpleGrid>
      </Container>
    </Section>
  );
};

export default Testimonials;
