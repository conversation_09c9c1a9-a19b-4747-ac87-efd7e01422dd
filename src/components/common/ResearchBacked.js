import React from 'react';
import {
  Heading,
  Text,
  SimpleGrid,
  VStack,
  HStack,
  Flex,
  Icon,
  Container,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Link,
  useColorModeValue,
} from '@chakra-ui/react';
import { FaBrain, FaChartLine, FaLightbulb, FaBook, FaExternalLinkAlt } from 'react-icons/fa';
import { useTranslation } from 'next-i18next';
import Section from './Section';

const ResearchCard = ({ title, description, icon, citations, color }) => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const headingColor = useColorModeValue('gray.800', 'white');

  return (
    <Card
      bg={cardBg}
      borderWidth="1px"
      borderColor={borderColor}
      borderRadius="lg"
      overflow="hidden"
      boxShadow="md"
      height="100%"
      display="flex"
      flexDirection="column"
      transition="all 0.3s"
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: 'lg',
        borderColor: `${color}.200`
      }}
      role="group"
    >
      <CardHeader pb={0} pt={{ base: 4, md: 5 }} px={{ base: 4, md: 5 }}>
        <Flex
          align="center"
          mb={3}
          flexWrap={{ base: "wrap", sm: "nowrap" }}
          gap={{ base: 2, sm: 0 }}
        >
          <Flex
            w={{ base: 10, md: 12 }}
            h={{ base: 10, md: 12 }}
            align="center"
            justify="center"
            borderRadius="full"
            bg={`${color}.100`}
            color={`${color}.500`}
            mr={{ base: 3, md: 4 }}
            transition="all 0.2s"
            _groupHover={{
              transform: 'scale(1.05)',
              bg: `${color}.200`
            }}
            flexShrink={0}
          >
            <Icon as={icon} boxSize={{ base: 5, md: 6 }} />
          </Flex>
          <Heading
            size={{ base: "sm", md: "md" }}
            fontWeight="semibold"
            color={headingColor}
            lineHeight="shorter"
          >
            {title}
          </Heading>
        </Flex>
      </CardHeader>

      <CardBody
        pt={{ base: 1, md: 2 }}
        pb={{ base: 3, md: 4 }}
        px={{ base: 4, md: 5 }}
        flex="1"
      >
        <Text
          fontSize={{ base: "xs", md: "sm" }}
          color={textColor}
          lineHeight="1.6"
        >
          {description}
        </Text>
      </CardBody>

      <CardFooter
        pt={0}
        pb={{ base: 4, md: 5 }}
        px={{ base: 4, md: 5 }}
        bg={`${color}.50`}
        borderTop="1px solid"
        borderColor={`${color}.100`}
      >
        <VStack align="start" spacing={{ base: 1, md: 1.5 }} width="100%">
          <Text
            fontSize={{ base: "2xs", md: "xs" }}
            fontWeight="bold"
            color={`${color}.700`}
            textTransform="uppercase"
            letterSpacing="wider"
          >
            {citations.length > 1 ? 'Research Citations:' : 'Research Citation:'}
          </Text>
          {citations.map((citation, idx) => (
            <HStack
              key={idx}
              spacing={{ base: 1, md: 2 }}
              fontSize={{ base: "2xs", md: "xs" }}
              color={textColor}
              align="flex-start"
            >
              <Text noOfLines={1} maxW={{ base: "90%", md: "90%" }}>
                {citation.text}
              </Text>
              {citation.url && (
                <Link
                  href={citation.url}
                  isExternal
                  color={`${color}.500`}
                  _hover={{ textDecoration: 'none', color: `${color}.600` }}
                  aria-label="View research citation"
                  flexShrink={0}
                >
                  <Icon
                    as={FaExternalLinkAlt}
                    boxSize={{ base: 2.5, md: 3 }}
                    transition="transform 0.2s"
                    _hover={{ transform: 'scale(1.2)' }}
                  />
                </Link>
              )}
            </HStack>
          ))}
        </VStack>
      </CardFooter>
    </Card>
  );
};

/**
 * Reusable ResearchBacked component
 * @param {Object} props - Component props
 * @param {string} props.namespace - Translation namespace (default: 'common')
 * @param {string} props.titleKey - Translation key for title (default: 'research_title')
 * @param {string} props.descriptionKey - Translation key for description (default: 'research_description')
 * @param {Array} props.researchAreas - Array of research area objects (optional, will use default if not provided)
 * @param {string} props.bg - Background color (default: 'gray.50')
 * @param {string} props.id - Section ID for anchor links
 * @param {Array} props.buttons - Array of button objects (optional)
 * @param {string} props.productName - Product name to use in default description (default: 'our product')
 */
const ResearchBacked = ({
  namespace = 'common',
  title = 'research_title',
  description = 'research_description',
  researchAreas: customResearchAreas,
  bg = 'gray.50',
  id,
  buttons: customButtons,
  productName = 'our product'
}) => {
  const { t } = useTranslation(namespace);

  // Default buttons if none provided
  const defaultButtons = [
    {
      label: t('research_button_science', 'Science-Based'),
      icon: FaBrain,
      colorScheme: 'blue'
    },
    {
      label: t('research_button_data', 'Data-Driven'),
      icon: FaChartLine,
      colorScheme: 'green'
    }
  ];

  // Default research areas if none provided
  const defaultResearchAreas = [
    {
      title: t('research_area_1_title', 'Research Area 1'),
      description: t('research_area_1_description', `${productName} is built on solid research foundations in this area, providing significant benefits to users.`),
      icon: FaBrain,
      color: 'blue',
      citations: [
        {
          text: 'Sweller, J. (2011). Cognitive load theory. Psychology of Learning and Motivation, 55, 37-76.',
          url: 'https://doi.org/10.1016/B978-0-12-387691-1.00002-8'
        },
        {
          text: 'Paas, F., & Ayres, P. (2014). Cognitive load theory: A broader view on the role of memory in learning and education. Educational Psychology Review, 26(2), 191-195.',
          url: 'https://doi.org/10.1007/s10648-014-9263-5'
        }
      ]
    },
    {
      title: t('research_area_2_title', 'Dual Coding Theory'),
      description: t('research_area_2_description', 'By combining visual mind maps with textual explanations, MindLadder leverages dual coding theory to engage multiple cognitive channels, enhancing memory formation and recall.'),
      icon: FaLightbulb,
      color: 'purple',
      citations: [
        {
          text: 'Paivio, A. (2014). Mind and its evolution: A dual coding theoretical approach. Psychology Press.',
          url: 'https://doi.org/10.4324/9781315785233'
        },
        {
          text: 'Clark, J. M., & Paivio, A. (1991). Dual coding theory and education. Educational Psychology Review, 3(3), 149-210.',
          url: 'https://doi.org/10.1007/**********'
        }
      ]
    },
    {
      title: t('research_area_3_title', 'Spaced Repetition'),
      description: t('research_area_3_description', 'MindLadder\'s layered approach naturally implements spaced repetition principles, revisiting core concepts at increasing levels of complexity to optimize long-term retention.'),
      icon: FaChartLine,
      color: 'green',
      citations: [
        {
          text: 'Kang, S. H. (2016). Spaced repetition promotes efficient and effective learning: Policy implications for instruction. Policy Insights from the Behavioral and Brain Sciences, 3(1), 12-19.',
          url: 'https://doi.org/10.1177/2372732215624708'
        },
        {
          text: 'Dunlosky, J., et al. (2013). Improving students\' learning with effective learning techniques: Promising directions from cognitive and educational psychology. Psychological Science in the Public Interest, 14(1), 4-58.',
          url: 'https://doi.org/10.1177/1529100612453266'
        }
      ]
    },
    {
      title: t('research_area_4_title', 'Desirable Difficulties'),
      description: t('research_area_4_description', 'The counterintuitive aspects in MindLadder\'s higher tiers create "desirable difficulties" that enhance learning by requiring deeper processing and more effortful retrieval.'),
      icon: FaBook,
      color: 'orange',
      citations: [
        {
          text: 'Bjork, R. A., & Bjork, E. L. (2020). Desirable difficulties in theory and practice. Journal of Applied Research in Memory and Cognition, 9(4), 475-479.',
          url: 'https://doi.org/10.1016/j.jarmac.2020.09.003'
        },
        {
          text: 'Soderstrom, N. C., & Bjork, R. A. (2015). Learning versus performance: An integrative review. Perspectives on Psychological Science, 10(2), 176-199.',
          url: 'https://doi.org/10.1177/1745691615569000'
        }
      ]
    }
  ];

  // Use provided research areas/buttons or fall back to defaults
  const researchAreasList = customResearchAreas || defaultResearchAreas;
  const buttonsList = customButtons || defaultButtons;

  return (
    <Section bg={bg} id={id}>
      <Container maxW="container.xl" px={{ base: 3, md: 6 }}>
        <VStack spacing={{ base: 6, md: 8 }} mb={{ base: 8, md: 12 }}>
          <Heading
            as="h2"
            size={{ base: "lg", md: "xl" }}
            textAlign="center"
            bgGradient="linear(to-r, blue.400, purple.500)"
            bgClip="text"
            px={{ base: 2, md: 0 }}
            lineHeight={{ base: "1.3", md: "1.2" }}
          >
            {title}
          </Heading>
          <Text
            fontSize={{ base: "sm", md: "lg" }}
            textAlign="center"
            maxW="3xl"
            px={{ base: 2, md: 0 }}
            color="gray.600"
            lineHeight="1.6"
          >
            {description}
          </Text>

          {buttonsList.length > 0 && (
            <Flex
              gap={{ base: 2, md: 4 }}
              flexWrap="wrap"
              justify="center"
              width="100%"
              px={{ base: 2, md: 0 }}
            >
              {buttonsList.map((button, idx) => (
                <Button
                  key={idx}
                  size={{ base: "xs", md: "sm" }}
                  colorScheme={button.colorScheme || 'blue'}
                  variant="outline"
                  leftIcon={button.icon && <Icon as={button.icon} boxSize={{ base: 3, md: 4 }} />}
                  px={{ base: 2, md: 3 }}
                  py={{ base: 1, md: 2 }}
                  borderRadius="full"
                  fontWeight="medium"
                  _hover={{
                    transform: 'translateY(-2px)',
                    bg: `${button.colorScheme || 'blue'}.50`
                  }}
                  transition="all 0.2s"
                >
                  {button.label}
                </Button>
              ))}
            </Flex>
          )}
        </VStack>

        <SimpleGrid
          columns={{ base: 1, sm: 2, lg: researchAreasList.length }}
          spacing={{ base: 4, md: 6 }}
          width="100%"
        >
          {researchAreasList.map((area, idx) => (
            <ResearchCard key={idx} {...area} />
          ))}
        </SimpleGrid>
      </Container>
    </Section>
  );
};

export default ResearchBacked;
