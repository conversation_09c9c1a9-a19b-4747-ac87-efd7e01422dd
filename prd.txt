## 项目名称：wise cards
产品目标：做一个让用户可以生成自己的wise card的网站，用户可以生成自己的wise card，并且可以分享给其他人。
Wise card 是AI生成的卡片，用户选择卡片类型之后，系统会根据用户选择的卡片类型，以及用户提供的输入，由用户生成一张独一无二的卡片。

## 产品结构
产品主要分为三个部分：
1. 卡片生成器：用户可以在这里选择卡片类型，并且提供输入，生成卡片。
2. 卡片库：用户可以在这里浏览其他用户生成的卡片。
3. 卡片分享：用户可以在这里分享自己的卡片给其他人。

## 产品功能
1. 卡片生成器：
   1. 用户选择卡片类型，并提供输入。
   2. 系统根据用户选择的卡片类型，以及用户提供的输入，生成一张独一无二的卡片。
2. 卡片库：
   1. 用户可以在这里浏览其他用户生成的卡片。
   2. 用户可以在这里点赞、评论、分享其他用户生成的卡片。
3. 卡片分享：
   1. 用户可以在这里分享自己的卡片给其他人。
   2. 用户可以在这里查看其他人分享的卡片。

## 技术实现：
1. 技术栈：
   1. 前后端：React、Next.js
   2. 部署：Node.js、Express、MongoDB、Mongoose
