{"reflection_intro_description": "ReflectAI 是您的智能反思教练，通过结构化对话和分析，帮助您培养更深层的自我认知和批判性思维能力。", "reflection_feature_1_title": "智能反思", "reflection_feature_1_text": "分享您的想法和信念，AI教练引导您运用批判性思维框架进行多维度思考和反思。", "reflection_feature_2_title": "结构化分析", "reflection_feature_2_text": "自动生成思维导图，可视化展示思维的不同维度，识别潜在的认知偏见。", "reflection_feature_3_title": "多维度视角", "reflection_feature_3_text": "从不同角度审视问题，提供平衡的反馈，既指出思维盲点也肯定合理之处。", "reflection_feature_4_title": "个人成长", "reflection_feature_4_text": "在AI指导下学习批判性思维，拓展认知边界，走出信息茧房。", "reflection_benefits_title": "使用收益", "reflection_personal_benefits_title": "个人成长", "reflection_personal_benefit_1": "培养更深的自我认知", "reflection_personal_benefit_2": "提升决策能力", "reflection_personal_benefit_3": "突破认知局限", "reflection_personal_benefit_4": "建立情商智慧", "reflection_professional_benefits_title": "职业发展", "reflection_professional_benefit_1": "增强批判性思维能力", "reflection_professional_benefit_2": "提升战略决策水平", "reflection_professional_benefit_3": "改善沟通技巧", "reflection_professional_benefit_4": "提升领导潜力", "reflection_usecases_title": "应用场景", "reflection_usecase_1_title": "决策分析", "reflection_usecase_1_text": "通过结构化分析和多角度思考深入探讨重要决策。", "reflection_usecase_2_title": "信念审视", "reflection_usecase_2_text": "批判性地检验个人信念和价值观，提升自我认知。", "reflection_usecase_3_title": "职业发展", "reflection_usecase_3_text": "反思职业选择和专业成长机会。", "reflection_methodology_title": "方法论", "reflection_framework_title": "反思框架", "reflection_framework_step_1_title": "观察", "reflection_framework_step_1_text": "清晰地识别和表达您的想法和信念。", "reflection_framework_step_2_title": "分析", "reflection_framework_step_2_text": "检验假设、证据和潜在偏见。", "reflection_framework_step_3_title": "视角", "reflection_framework_step_3_text": "考虑替代观点和解释。", "reflection_framework_step_4_title": "整合", "reflection_framework_step_4_text": "将洞察转化为可行的理解。", "reflection_principles_title": "核心原则", "reflection_principle_1_title": "客观性", "reflection_principle_1_text": "在检验信念和想法时保持中立立场。", "reflection_principle_2_title": "成长思维", "reflection_principle_2_text": "将挑战视为学习和发展的机会。", "reflection_principle_3_title": "实践应用", "reflection_principle_3_text": "注重可行的洞察和具体的改进。", "reflection_faq_title": "常见问题", "reflection_faq_1_q": "ReflectAI如何帮助提升批判性思维？", "reflection_faq_1_a": "ReflectAI使用结构化框架和引导性问题，帮助您从多个角度审视思维，识别假设，发展更细致的理解。", "reflection_faq_2_q": "我的个人信息是否安全？", "reflection_faq_2_a": "是的，我们非常重视隐私。您的所有反思内容和个人信息都经过加密，并严格保密。", "reflection_faq_3_q": "多久使用一次ReflectAI比较好？", "reflection_faq_3_a": "为获得最佳效果，我们建议定期进行反思，理想情况下每周2-3次，特别是在面临重要决策或探索新想法时。", "reflection_faq_4_q": "ReflectAI能帮助决策吗？", "reflection_faq_4_a": "是的，ReflectAI帮助分解复杂决策，检验假设，考虑多个视角，从而做出更明智的选择。", "reflection_faq_5_q": "ReflectAI适合团队使用吗？", "reflection_faq_5_a": "虽然主要面向个人使用，但ReflectAI对团队领导者提升决策和沟通能力很有价值。", "reflection_faq_6_q": "ReflectAI与传统教练有什么不同？", "reflection_faq_6_a": "ReflectAI提供全天候服务、结构化分析和一致的方法论，通过AI驱动的洞察和框架补充传统教练服务。", "reflection_faq_7_q": "使用FunBlocks AI Reflection Coach 需要付费吗？我可以免费使用吗？", "reflection_faq_7_a": "FunBlocks AI Reflection Coach 为所有用户提供免费使用。新用户可以享受免费试用，所有用户每天都有10次免费的AI请求，只需登录即可。", "ai_reflection_desc": "通过一个挑战您思维的AI教练，培养更强的推理能力和更深的理解力"}