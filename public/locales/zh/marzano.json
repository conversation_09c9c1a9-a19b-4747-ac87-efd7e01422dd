{"marzano_intro_description": "MarzanoBrain是一款改变教育者和自学者课程设计方法的网络应用。利用马扎诺教育目标分类学驱动的先进人工智能，我们的平台能够为任何学科生成全面的学习路线图，以直观的思维导图格式呈现。", "marzano_feature_1_title": "主题到课程的转化", "marzano_feature_1_text": "只需输入任何感兴趣的学习主题，几秒钟内即可获得完全结构化的课程设计", "marzano_feature_2_title": "马扎诺分类学框架", "marzano_feature_2_text": "所有学习计划都建立在<PERSON>经过验证的教育框架上，确保全面的认知发展", "marzano_feature_3_title": "交互式思维导图", "marzano_feature_3_text": "在引人入胜的层次思维导图中可视化您的课程，展示概念之间的关系", "marzano_benefits_title": "MarzanoBrain的优势", "marzano_benefits_edu_title": "对于教育者", "marzano_benefit_edu_1": "在几分钟内（而非几小时）创建有研究支持的教学计划", "marzano_benefit_edu_2": "将教学活动与所有认知系统对齐", "marzano_benefit_edu_3": "设计与学生发展阶段相匹配的评估方法", "marzano_benefit_edu_4": "构建优化学生学习成果的教学体验", "marzano_benefits_learner_title": "对于学习者", "marzano_benefit_learner_1": "通过清晰的框架理解您的学习旅程", "marzano_benefit_learner_2": "确定您在掌握过程中的位置", "marzano_benefit_learner_3": "为您的教育发展设定现实的目标", "marzano_benefit_learner_4": "跟踪您从基本回忆到创造性应用的认知进步", "marzano_audience_title": "谁能从MarzanoBrain中受益", "marzano_audience_1_title": "教师和教育工作者", "marzano_audience_1_text": "创建促进全面认知发展的结构化教案和课程大纲", "marzano_audience_2_title": "课程设计师", "marzano_audience_2_text": "生成可针对特定教育环境定制的基础框架", "marzano_audience_3_title": "自学者", "marzano_audience_3_text": "为掌握新技能或学科发展具有清晰进程的结构化学习路径", "marzano_audience_4_title": "学科专家", "marzano_audience_4_text": "将专业知识转化为按认知复杂性组织的可教授框架", "marzano_framework_title": "马扎诺分类学框架", "marzano_framework_description": "马扎诺教育目标分类学提供了一个全面的框架，用于理解学习过程并设计跨多个认知系统的教学活动。", "marzano_systems_title": "马扎诺的系统和知识领域", "marzano_system_1_title": "1. 自我系统", "marzano_system_1_text": "检验决定是否参与新任务的动机和态度", "marzano_system_2_title": "2. 元认知系统", "marzano_system_2_text": "在学习活动中设定目标并监控进度", "marzano_system_3_title": "3. 认知系统", "marzano_system_3_text": "通过不同复杂度级别处理信息", "marzano_system_4_title": "4. 知识领域", "marzano_system_4_text": "正在学习或教授的特定学科内容", "marzano_levels_title": "马扎诺分类学的处理层次", "marzano_level_1_title": "1. 回忆", "marzano_level_1_text": "识别和回忆基本信息", "marzano_level_2_title": "2. 理解", "marzano_level_2_text": "理解核心概念，但不进行扩展", "marzano_level_3_title": "3. 分析", "marzano_level_3_text": "更深入地检查信息，识别模式和关系", "marzano_level_4_title": "4. 知识应用", "marzano_level_4_text": "将知识应用于现实世界的情境和问题", "marzano_level_5_title": "5. 元认知", "marzano_level_5_text": "监控和调整自己的思维过程", "marzano_level_6_title": "6. 自我系统思维", "marzano_level_6_text": "检查与学习相关的信念、动机和自我效能感", "marzano_advantages_title": "马扎诺分类学的优势", "marzano_advantages_description": "马扎诺分类学相比其他教育框架提供了几个优势，使其在全面学习设计中特别有价值。", "marzano_advantage_1": "整合认知、情感和行为领域", "marzano_advantage_2": "专注于思维策略而不仅仅是内容记忆", "marzano_advantage_3": "面向知识应用的实践导向", "marzano_advantage_4": "强调学生自我调节和自我监控", "marzano_advantage_5": "基于研究的基础，具有经过验证的教育成果", "marzano_faq_title": "常见问题", "marzano_faq_1_q": "MarzanoBrain与其他教育规划工具有何不同？", "marzano_faq_1_a": "MarzanoBrain的独特之处在于使用马扎诺分类学框架组织学习进程。与主要关注内容传递的工具不同，MarzanoBrain基于认知复杂性构建教育体验，确保学习活动与适当的发展阶段相匹配。", "marzano_faq_2_q": "使用MarzanoBrain需要了解马扎诺分类学吗？", "marzano_faq_2_a": "不需要事先了解马扎诺分类学。MarzanoBrain会自动将框架应用到您的主题。然而，随着工具的使用，您自然会熟悉马扎诺方法，增强您的教学设计技能。", "marzano_faq_3_q": "MarzanoBrain可以用于任何学科领域吗？", "marzano_faq_3_a": "是的，MarzanoBrain设计用于任何学科或主题。无论您教授数学、文学、科学、艺术还是专业技能，马扎诺框架都可以适当地构建学习进程。", "marzano_faq_4_q": "MarzanoBrain生成的教育计划有多详细？", "marzano_faq_4_a": "MarzanoBrain生成全面的计划，包括马扎诺分类学所有系统的学习目标、教学活动和评估方法。您将收到一个完整的教育路线图，可以直接使用或进一步定制。", "marzano_faq_5_q": "我可以导出或分享MarzanoBrain创建的思维导图吗？", "marzano_faq_5_a": "是的，所有思维导图都可以以各种格式导出或直接与同事、学生或其他利益相关者分享。这使得MarzanoBrain非常适合协作课程开发和教育规划。", "marzano_faq_6_q": "MarzanoBrain适合高等教育和专业培训吗？", "marzano_faq_6_a": "绝对适合。虽然马扎诺分类学有时与K-12设置相关联，但它在高等教育和专业培训环境中同样有价值。从基础理解到创造性应用和自我系统思维的进程在所有学习层次都相关。", "marzano_faq_7_q": "MarzanoBrain如何帮助学生自主学习？", "marzano_faq_7_a": "学生可以使用MarzanoBrain为想要掌握的学科创建学习路径。生成的思维导图提供清晰的里程碑和进程路径，帮助学生了解他们在学习旅程中的位置以及接下来需要采取的步骤。", "marzano_faq_8_q": "MarzanoBrain与其他教育工具和平台集成吗？", "marzano_faq_8_a": "MarzanoBrain思维导图可以导出并整合到各种学习管理系统、演示工具和教育平台中。我们不断努力扩展集成选项，使MarzanoBrain无缝融入您现有的教育工作流程。", "marzano_faq_9_q": "使用FunBlocks AI MarzanoBrain需要付费吗？我可以免费使用吗？", "marzano_faq_9_a": "FunBlocks AI MarzanoBrain为所有用户提供免费使用。新用户可以享受免费试用，所有用户每天都有10次免费的AI请求，只需登录即可。"}