{"solo_intro_description": "SOLOBrain是一款创新型网络应用，利用观察学习结果结构（SOLO）分类法将任何主题转化为全面的教育计划。教育者和学习者可以在几秒钟内生成精美的思维导图，将复杂主题分解为渐进式学习旅程。", "solo_feature_1_title": "AI驱动的课程设计", "solo_feature_1_text": "使用SOLO分类法框架为任何学科或主题生成详细的教学设计", "solo_feature_2_title": "可视化学习进程", "solo_feature_2_text": "创建思维导图，通过SOLO分类法的五个认知层次可视化学习旅程", "solo_feature_3_title": "全面的学习资源", "solo_feature_3_text": "为每个认知层次获取量身定制的学习目标、教学活动和评估方法", "solo_benefits_title": "SOLOBrain的优势", "solo_benefits_edu_title": "对于教育者", "solo_benefit_edu_1": "几秒钟内创建基于证据的教案，而非几小时", "solo_benefit_edu_2": "将教学活动与认知进程对齐", "solo_benefit_edu_3": "设计与学生发展阶段匹配的评估方法", "solo_benefit_edu_4": "构建优化学生学习成果的学习体验", "solo_benefits_learner_title": "对于学习者", "solo_benefit_learner_1": "通过清晰的阶段理解你的学习进程", "solo_benefit_learner_2": "确定你在掌握过程中的位置", "solo_benefit_learner_3": "为你的教育发展设定现实的目标", "solo_benefit_learner_4": "追踪你从基础理解到创造性应用的认知进步", "solo_audience_title": "谁能从SOLOBrain中受益", "solo_audience_1_title": "教师与教育工作者", "solo_audience_1_text": "快速创建促进认知发展的结构化教案和课程设计", "solo_audience_2_title": "教学设计师", "solo_audience_2_text": "为不同学科开发基于证据的学习进程和教育资源", "solo_audience_3_title": "学生与自学者", "solo_audience_3_text": "规划具有清晰里程碑和进程路径的自主学习旅程", "solo_framework_title": "SOLO分类法框架", "solo_framework_description": "观察学习结果结构（SOLO）分类法提供了一种基于认知复杂性对学习成果进行分类的系统方法。SOLOBrain利用这一框架创建渐进式教育计划。", "solo_cognitive_levels_title": "SOLO的五个认知层次", "solo_level_1_title": "1. 前结构阶段", "solo_level_1_text": "基本认识阶段，学习者对主题几乎没有组织性理解", "solo_level_2_title": "2. 单一结构阶段", "solo_level_2_text": "单一概念理解，学习者能识别主题的一个相关方面", "solo_level_3_title": "3. 多重结构阶段", "solo_level_3_text": "多个相互断开的概念，学习者知道几个相关方面但未能连接它们", "solo_level_4_title": "4. 关联阶段", "solo_level_4_text": "综合理解，学习者能连接多个方面形成一个连贯的整体", "solo_level_5_title": "5. 扩展抽象阶段", "solo_level_5_text": "创造性应用和迁移，学习者能将原则推广到新领域", "solo_advantages_title": "SOLO的优势", "solo_advantage_1_title": "认知进程", "solo_advantage_1_text": "基于认知复杂性构建内容，实现真正的渐进式学习体验", "solo_advantage_2_title": "支架式学习", "solo_advantage_2_text": "每个层次都建立在之前层次的基础上，创造自然的学习支架，最大化学习成果", "solo_advantage_3_title": "对齐评估", "solo_advantage_3_text": "确保评估方法与适当的认知层次匹配，准确衡量学生进步", "solo_advantage_4_title": "多用途应用", "solo_advantage_4_text": "适用于任何学科领域或教育层次，从小学到高等教育", "solo_faq_title": "常见问题", "solo_faq_1_q": "SOLOBrain与其他教育规划工具有何不同？", "solo_faq_1_a": "SOLOBrain的独特之处在于使用SOLO分类法框架组织学习进程。与专注于内容传递的工具不同，SOLOBrain基于认知复杂性构建教育体验，确保学习活动与适当的发展阶段相匹配。", "solo_faq_2_q": "使用SOLOBrain需要了解SOLO分类法吗？", "solo_faq_2_a": "不需要SOLO分类法的先验知识。SOLOBrain会帮你将框架应用到你的主题上。不过，随着使用工具，你会自然地熟悉SOLO方法，提升你的教学设计技能。", "solo_faq_3_q": "SOLOBrain可以用于任何学科领域吗？", "solo_faq_3_a": "是的，SOLOBrain设计用于处理任何学科或主题。无论你是在教授数学、文学、科学、艺术还是专业技能，SOLO框架都可以适当地构建学习进程。", "solo_faq_4_q": "SOLOBrain生成的教育计划有多详细？", "solo_faq_4_a": "SOLOBrain生成全面的计划，包括每个SOLO层次的学习目标、教学活动和评估方法。你将获得一个完整的教育路线图，可以直接使用或进一步定制。", "solo_faq_5_q": "我可以导出或分享SOLOBrain创建的思维导图吗？", "solo_faq_5_a": "是的，所有思维导图都可以以各种格式导出或直接与同事、学生或其他利益相关者分享。这使SOLOBrain成为协作课程开发和教育规划的完美工具。", "solo_faq_6_q": "SOLOBrain适合高等教育和专业培训吗？", "solo_faq_6_a": "绝对适合。虽然SOLO分类法常用于K-12教育，但在高等教育和专业培训环境中同样有价值。该框架强调从基础理解到创造性应用的进程，适用于所有学习层次。", "solo_faq_7_q": "SOLOBrain如何帮助学生的自主学习？", "solo_faq_7_a": "学生可以使用SOLOBrain为他们想要掌握的主题创建学习路径。生成的思维导图提供清晰的里程碑和进程路径，帮助学生理解他们在学习旅程中的位置以及下一步需要采取的措施。", "solo_faq_8_q": "SOLOBrain是否与其他教育工具和平台集成？", "solo_faq_8_a": "SOLOBrain思维导图可以导出并整合到各种学习管理系统、演示工具和教育平台中。我们不断努力扩展集成选项，使SOLOBrain能够无缝融入你现有的教育工作流程。", "solo_faq_9_q": "使用FunBlocks AI SOLOBrain需要付费吗？我可以免费使用吗？", "solo_faq_9_a": "FunBlocks AI SOLOBrain为所有用户提供免费使用。新用户可以享受免费试用，所有用户每天都有10次免费的AI请求，只需登录即可。"}