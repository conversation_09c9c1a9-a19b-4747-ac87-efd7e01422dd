{"decision_intro_title": "AI 决策分析助手", "decision_intro_description": "借助 AI 驱动的多维度分析，考虑多个角度、风险和结果，帮助您做出理性决策。", "decision_feature_1_title": "智能分析", "decision_feature_1_text": "运用先进 AI 从多个角度分析决策问题", "decision_feature_2_title": "全面评估", "decision_feature_2_text": "系统性评估各种选择、风险和潜在结果", "decision_feature_3_title": "平衡洞察", "decision_feature_3_text": "基于具体情况提供客观分析和建议", "decision_benefits_title": "主要优势", "decision_benefit_1": "减少决策疲劳和认知偏差", "decision_benefit_2": "获得结构化的全面分析", "decision_benefit_3": "节省决策时间", "decision_benefit_4": "做出更有把握的决定", "decision_advantages_title": "为什么选择 AI 决策分析", "decision_advantage_1": "多角度分析", "decision_advantage_2": "数据驱动的洞察", "decision_advantage_3": "系统化评估", "decision_advantage_4": "清晰的选项可视化", "decision_usecases_title": "常见使用场景", "decision_usecase_1_title": "商业决策", "decision_usecase_1_text": "战略规划、资源分配和市场进入决策", "decision_usecase_2_title": "个人选择", "decision_usecase_2_text": "职业发展、投资和生活规划决策", "decision_usecase_3_title": "团队决策", "decision_usecase_3_text": "项目优先级、招聘和运营决策", "decision_faq_1_q": "AI 决策分析是如何工作的？", "decision_faq_1_a": "我们的 AI 使用先进的框架分析您的决策场景，提供结构化的见解和建议。", "decision_faq_2_q": "它可以帮助哪些类型的决策？", "decision_faq_2_a": "从商业战略决策到个人生活选择，我们的系统都能帮助处理复杂的决策过程。", "decision_faq_3_q": "AI 分析的准确度如何？", "decision_faq_3_a": "AI 基于您的输入提供数据驱动的见解，但最终决策应该始终包含人为判断。", "decision_faq_4_q": "我可以自定义分析框架吗？", "decision_faq_4_a": "是的，您可以指定首选的决策框架和标准，获得更个性化的分析。", "decision_faq_5_q": "分析需要多长时间？", "decision_faq_5_a": "大多数分析在几分钟内完成，但复杂的决策可能需要更详细的输入。", "decision_faq_6_q": "我可以自定义和扩展生成的思维导图吗？", "decision_faq_6_a": "当然可以！虽然 AI 生成了初始结构，但您对最终结果拥有完全的控制权。您可以自定义样式、重新组织节点，并在 AI 的帮助下扩展任何主题。AI 生成与手动自定义的结合确保了效率和个性化。", "decision_faq_7_q": "它和 FunBlocks AIFlow 的关系是什么？", "decision_faq_7_a": "FunBlocks AIFlow 是一款功能齐全的 AI 白板和思维导图工具，提供完整的 AI 白板功能和创新的思维导图交互。相比之下，FunBlocks AI 决策分析器是一个上下文应用，专注于帮助用户使用经典分析框架分析他们的决策，并通过结构良好的思维导图引导他们的思考。用户可以无缝地将 AI 决策分析器生成的思维导图转移到 AIFlow 白板上，以便进行更深入的内容探索和创意开发。", "decision_faq_8_q": "使用 FunBlocks AI 决策分析器需要付费吗？我可以免费使用吗？", "decision_faq_8_a": "FunBlocks AI 决策分析器为所有用户提供免费使用。新用户可以享受免费试用，所有用户每天可以免费请求 10 次 AI 服务，只需登录即可。", "decision_tree": "决策树", "decision_matrix": "决策矩阵"}