{"reading_intro_description": "AI Reading Map is an innovative tool powered by advanced language models that helps readers create structured reading guides. Through deep analysis of book content and characteristics, it automatically generates professional mind maps, helping readers better grasp core content and improve reading efficiency.", "reading_feature_1_title": "Intelligent Analysis", "reading_feature_1_text": "Automatically identifies book categories and target audiences, analyzes writing styles, and provides category-oriented reading suggestions.", "reading_feature_2_title": "Professional Framework", "reading_feature_2_text": "Matches the most suitable analysis framework based on different book categories, from literature to technical content.", "reading_feature_3_title": "Structured Mind Maps", "reading_feature_3_text": "Creates clear, hierarchical mind maps with core themes, multi-dimensional analysis, and practical summaries.", "reading_benefits_title": "Why choose AI Reading Map?", "reading_benefits_personal_title": "Personal Benefits", "reading_benefit_personal_1": "Quickly grasp core book content", "reading_benefit_personal_2": "Build structured reading notes", "reading_benefit_personal_3": "Improve reading comprehension efficiency", "reading_benefit_personal_4": "Deepen knowledge internalization", "reading_benefits_professional_title": "Professional Benefits", "reading_benefit_professional_1": "Assist in teaching preparation", "reading_benefit_professional_2": "Guide student learning", "reading_benefit_professional_3": "Reading comprehension training", "reading_benefit_professional_4": "Knowledge system construction", "reading_scenario_title": "Perfect for Various Scenarios", "reading_scenario_1_title": "Personal Reading", "reading_scenario_1_text": "Perfect for individual readers looking to enhance their reading comprehension and note-taking abilities.", "reading_scenario_2_title": "Education & Training", "reading_scenario_2_text": "Ideal for teachers and students in educational settings for better learning outcomes.", "reading_scenario_3_title": "Knowledge Management", "reading_scenario_3_text": "Excellent for book clubs and team learning environments to share and manage knowledge effectively.", "reading_tech_advantages_title": "Technical Advantages", "reading_tech_advantage_1_title": "AI Analysis Engine", "reading_tech_advantage_1_text": "Utilizes advanced language models for deep semantic understanding", "reading_tech_advantage_2_title": "Visual Presentation", "reading_tech_advantage_2_text": "Clear hierarchical structure with multi-platform support", "reading_tech_advantage_3_title": "Knowledge Base", "reading_tech_advantage_3_text": "Rich domain frameworks and analysis templates", "reading_tech_advantage_4_title": "Adaptive Learning", "reading_tech_advantage_4_text": "Continuously enhances capabilities through user feedbacks and advancements in LLM technology", "reading_unique_features_title": "Unique Features", "reading_unique_feature_1_title": "Smart Classification", "reading_unique_feature_1_text": "Automatically categorizes content and matches appropriate analysis frameworks", "reading_unique_feature_2_title": "Multi-dimensional Analysis", "reading_unique_feature_2_text": "Provides comprehensive insights from different perspectives", "reading_unique_feature_3_title": "Practical Application", "reading_unique_feature_3_text": "Focuses on real-world usage and knowledge application", "reading_unique_feature_4_title": "In-depth Exploration", "reading_unique_feature_4_text": "Leverages the full capabilities of FunBlocks AIFlow for enhanced brainstorming and creative development.", "reading_faq_title": "Frequently Asked Questions", "reading_faq_1_q": "How does AI Reading Map work?", "reading_faq_1_a": "It uses advanced AI to analyze book content, identify key concepts, and create structured mind maps that help you understand and remember the material better.", "reading_faq_2_q": "What types of books can it analyze?", "reading_faq_2_a": "It can analyze various types including literature, technical books, business books, and academic materials, with specialized frameworks for each category.", "reading_faq_3_q": "How accurate is the AI analysis?", "reading_faq_3_a": "The AI provides highly accurate analysis based on advanced language models and professional frameworks, continuously improving through machine learning.", "reading_faq_4_q": "Can I customize the mind maps?", "reading_faq_4_a": "Yes, you can adjust the focus areas and level of detail in the mind maps to match your specific needs and preferences.", "reading_faq_5_q": "Is it suitable for students?", "reading_faq_5_a": "Absolutely! It's particularly helpful for students in improving study efficiency and understanding complex materials.", "reading_faq_6_q": "How does it compare to traditional note-taking?", "reading_faq_6_a": "While traditional note-taking is valuable, AI Reading Map provides a more structured, comprehensive, and efficient way to capture and organize key information from books.", "reading_faq_7_q": "What is the relationship with FunBlocks AIFlow?", "reading_faq_7_a": "FunBlocks AIFlow is a full-featured AI whiteboard and mind mapping tool that offers complete AI whiteboard functionality and innovative mind mapping interactions. In contrast, FunBlocks AI Reading Map is a FunBlocks AIFlow application focused on helping users quickly extract core content and viewpoints from books and guiding their learning with structured mind maps. Users can seamlessly transfer mind maps generated in AI Reading Map to the AIFlow whiteboard for deeper exploration.", "reading_faq_8_q": "Is there a fee to use FunBlocks AI Reading Map? Can I use it for free?", "reading_faq_8_a": "FunBlocks AI Reading Map offers free usage for all users. New users can enjoy a free trial, and all users have 10 free AI requests per day, simply by logging in."}