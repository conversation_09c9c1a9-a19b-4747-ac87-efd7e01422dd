"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_mermaid_dist_chunks_mermaid_core_c4Diagram-6F5ED5ID_mjs"],{

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-6F5ED5ID.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-6F5ED5ID.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"diagram\": function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-ASOPGD6M.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-ASOPGD6M.mjs\");\n/* harmony import */ var _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-7DKRZKHE.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7DKRZKHE.mjs\");\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3 */ \"./node_modules/d3/src/index.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @braintree/sanitize-url */ \"./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n\n\n// src/diagrams/c4/parser/c4Diagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 24], $V1 = [1, 25], $V2 = [1, 26], $V3 = [1, 27], $V4 = [1, 28], $V5 = [1, 63], $V6 = [1, 64], $V7 = [1, 65], $V8 = [1, 66], $V9 = [1, 67], $Va = [1, 68], $Vb = [1, 69], $Vc = [1, 29], $Vd = [1, 30], $Ve = [1, 31], $Vf = [1, 32], $Vg = [1, 33], $Vh = [1, 34], $Vi = [1, 35], $Vj = [1, 36], $Vk = [1, 37], $Vl = [1, 38], $Vm = [1, 39], $Vn = [1, 40], $Vo = [1, 41], $Vp = [1, 42], $Vq = [1, 43], $Vr = [1, 44], $Vs = [1, 45], $Vt = [1, 46], $Vu = [1, 47], $Vv = [1, 48], $Vw = [1, 50], $Vx = [1, 51], $Vy = [1, 52], $Vz = [1, 53], $VA = [1, 54], $VB = [1, 55], $VC = [1, 56], $VD = [1, 57], $VE = [1, 58], $VF = [1, 59], $VG = [1, 60], $VH = [14, 42], $VI = [14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VJ = [12, 14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VK = [1, 82], $VL = [1, 83], $VM = [1, 84], $VN = [1, 85], $VO = [12, 14, 42], $VP = [12, 14, 33, 42], $VQ = [12, 14, 33, 42, 76, 77, 79, 80], $VR = [12, 33], $VS = [34, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mermaidDoc\": 4, \"direction\": 5, \"direction_tb\": 6, \"direction_bt\": 7, \"direction_rl\": 8, \"direction_lr\": 9, \"graphConfig\": 10, \"C4_CONTEXT\": 11, \"NEWLINE\": 12, \"statements\": 13, \"EOF\": 14, \"C4_CONTAINER\": 15, \"C4_COMPONENT\": 16, \"C4_DYNAMIC\": 17, \"C4_DEPLOYMENT\": 18, \"otherStatements\": 19, \"diagramStatements\": 20, \"otherStatement\": 21, \"title\": 22, \"accDescription\": 23, \"acc_title\": 24, \"acc_title_value\": 25, \"acc_descr\": 26, \"acc_descr_value\": 27, \"acc_descr_multiline_value\": 28, \"boundaryStatement\": 29, \"boundaryStartStatement\": 30, \"boundaryStopStatement\": 31, \"boundaryStart\": 32, \"LBRACE\": 33, \"ENTERPRISE_BOUNDARY\": 34, \"attributes\": 35, \"SYSTEM_BOUNDARY\": 36, \"BOUNDARY\": 37, \"CONTAINER_BOUNDARY\": 38, \"NODE\": 39, \"NODE_L\": 40, \"NODE_R\": 41, \"RBRACE\": 42, \"diagramStatement\": 43, \"PERSON\": 44, \"PERSON_EXT\": 45, \"SYSTEM\": 46, \"SYSTEM_DB\": 47, \"SYSTEM_QUEUE\": 48, \"SYSTEM_EXT\": 49, \"SYSTEM_EXT_DB\": 50, \"SYSTEM_EXT_QUEUE\": 51, \"CONTAINER\": 52, \"CONTAINER_DB\": 53, \"CONTAINER_QUEUE\": 54, \"CONTAINER_EXT\": 55, \"CONTAINER_EXT_DB\": 56, \"CONTAINER_EXT_QUEUE\": 57, \"COMPONENT\": 58, \"COMPONENT_DB\": 59, \"COMPONENT_QUEUE\": 60, \"COMPONENT_EXT\": 61, \"COMPONENT_EXT_DB\": 62, \"COMPONENT_EXT_QUEUE\": 63, \"REL\": 64, \"BIREL\": 65, \"REL_U\": 66, \"REL_D\": 67, \"REL_L\": 68, \"REL_R\": 69, \"REL_B\": 70, \"REL_INDEX\": 71, \"UPDATE_EL_STYLE\": 72, \"UPDATE_REL_STYLE\": 73, \"UPDATE_LAYOUT_CONFIG\": 74, \"attribute\": 75, \"STR\": 76, \"STR_KEY\": 77, \"STR_VALUE\": 78, \"ATTRIBUTE\": 79, \"ATTRIBUTE_EMPTY\": 80, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"direction_tb\", 7: \"direction_bt\", 8: \"direction_rl\", 9: \"direction_lr\", 11: \"C4_CONTEXT\", 12: \"NEWLINE\", 14: \"EOF\", 15: \"C4_CONTAINER\", 16: \"C4_COMPONENT\", 17: \"C4_DYNAMIC\", 18: \"C4_DEPLOYMENT\", 22: \"title\", 23: \"accDescription\", 24: \"acc_title\", 25: \"acc_title_value\", 26: \"acc_descr\", 27: \"acc_descr_value\", 28: \"acc_descr_multiline_value\", 33: \"LBRACE\", 34: \"ENTERPRISE_BOUNDARY\", 36: \"SYSTEM_BOUNDARY\", 37: \"BOUNDARY\", 38: \"CONTAINER_BOUNDARY\", 39: \"NODE\", 40: \"NODE_L\", 41: \"NODE_R\", 42: \"RBRACE\", 44: \"PERSON\", 45: \"PERSON_EXT\", 46: \"SYSTEM\", 47: \"SYSTEM_DB\", 48: \"SYSTEM_QUEUE\", 49: \"SYSTEM_EXT\", 50: \"SYSTEM_EXT_DB\", 51: \"SYSTEM_EXT_QUEUE\", 52: \"CONTAINER\", 53: \"CONTAINER_DB\", 54: \"CONTAINER_QUEUE\", 55: \"CONTAINER_EXT\", 56: \"CONTAINER_EXT_DB\", 57: \"CONTAINER_EXT_QUEUE\", 58: \"COMPONENT\", 59: \"COMPONENT_DB\", 60: \"COMPONENT_QUEUE\", 61: \"COMPONENT_EXT\", 62: \"COMPONENT_EXT_DB\", 63: \"COMPONENT_EXT_QUEUE\", 64: \"REL\", 65: \"BIREL\", 66: \"REL_U\", 67: \"REL_D\", 68: \"REL_L\", 69: \"REL_R\", 70: \"REL_B\", 71: \"REL_INDEX\", 72: \"UPDATE_EL_STYLE\", 73: \"UPDATE_REL_STYLE\", 74: \"UPDATE_LAYOUT_CONFIG\", 76: \"STR\", 77: \"STR_KEY\", 78: \"STR_VALUE\", 79: \"ATTRIBUTE\", 80: \"ATTRIBUTE_EMPTY\" },\n    productions_: [0, [3, 1], [3, 1], [5, 1], [5, 1], [5, 1], [5, 1], [4, 1], [10, 4], [10, 4], [10, 4], [10, 4], [10, 4], [13, 1], [13, 1], [13, 2], [19, 1], [19, 2], [19, 3], [21, 1], [21, 1], [21, 2], [21, 2], [21, 1], [29, 3], [30, 3], [30, 3], [30, 4], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [31, 1], [20, 1], [20, 2], [20, 3], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 1], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [35, 1], [35, 2], [75, 1], [75, 2], [75, 1], [75, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setDirection(\"TB\");\n          break;\n        case 4:\n          yy.setDirection(\"BT\");\n          break;\n        case 5:\n          yy.setDirection(\"RL\");\n          break;\n        case 6:\n          yy.setDirection(\"LR\");\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n        case 12:\n          yy.setC4Type($$[$0 - 3]);\n          break;\n        case 19:\n          yy.setTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 20:\n          yy.setAccDescription($$[$0].substring(15));\n          this.$ = $$[$0].substring(15);\n          break;\n        case 21:\n          this.$ = $$[$0].trim();\n          yy.setTitle(this.$);\n          break;\n        case 22:\n        case 23:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 28:\n          $$[$0].splice(2, 0, \"ENTERPRISE\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 29:\n          $$[$0].splice(2, 0, \"SYSTEM\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 30:\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 31:\n          $$[$0].splice(2, 0, \"CONTAINER\");\n          yy.addContainerBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 32:\n          yy.addDeploymentNode(\"node\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 33:\n          yy.addDeploymentNode(\"nodeL\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 34:\n          yy.addDeploymentNode(\"nodeR\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 35:\n          yy.popBoundaryParseStack();\n          break;\n        case 39:\n          yy.addPersonOrSystem(\"person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 40:\n          yy.addPersonOrSystem(\"external_person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 41:\n          yy.addPersonOrSystem(\"system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 42:\n          yy.addPersonOrSystem(\"system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 43:\n          yy.addPersonOrSystem(\"system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 44:\n          yy.addPersonOrSystem(\"external_system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 45:\n          yy.addPersonOrSystem(\"external_system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 46:\n          yy.addPersonOrSystem(\"external_system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 47:\n          yy.addContainer(\"container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 48:\n          yy.addContainer(\"container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 49:\n          yy.addContainer(\"container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 50:\n          yy.addContainer(\"external_container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 51:\n          yy.addContainer(\"external_container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 52:\n          yy.addContainer(\"external_container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 53:\n          yy.addComponent(\"component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 54:\n          yy.addComponent(\"component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 55:\n          yy.addComponent(\"component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 56:\n          yy.addComponent(\"external_component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 57:\n          yy.addComponent(\"external_component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 58:\n          yy.addComponent(\"external_component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 60:\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 61:\n          yy.addRel(\"birel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 62:\n          yy.addRel(\"rel_u\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 63:\n          yy.addRel(\"rel_d\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 64:\n          yy.addRel(\"rel_l\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 65:\n          yy.addRel(\"rel_r\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 66:\n          yy.addRel(\"rel_b\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 67:\n          $$[$0].splice(0, 1);\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 68:\n          yy.updateElStyle(\"update_el_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 69:\n          yy.updateRelStyle(\"update_rel_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 70:\n          yy.updateLayoutConfig(\"update_layout_config\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 71:\n          this.$ = [$$[$0]];\n          break;\n        case 72:\n          $$[$0].unshift($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 73:\n        case 75:\n          this.$ = $$[$0].trim();\n          break;\n        case 74:\n          let kv = {};\n          kv[$$[$0 - 1].trim()] = $$[$0].trim();\n          this.$ = kv;\n          break;\n        case 76:\n          this.$ = \"\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 7: [1, 6], 8: [1, 7], 9: [1, 8], 10: 4, 11: [1, 9], 15: [1, 10], 16: [1, 11], 17: [1, 12], 18: [1, 13] }, { 1: [3] }, { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 7] }, { 1: [2, 3] }, { 1: [2, 4] }, { 1: [2, 5] }, { 1: [2, 6] }, { 12: [1, 14] }, { 12: [1, 15] }, { 12: [1, 16] }, { 12: [1, 17] }, { 12: [1, 18] }, { 13: 19, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 70, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 71, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 72, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 73, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 14: [1, 74] }, o($VH, [2, 13], { 43: 23, 29: 49, 30: 61, 32: 62, 20: 75, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VH, [2, 14]), o($VI, [2, 16], { 12: [1, 76] }), o($VH, [2, 36], { 12: [1, 77] }), o($VJ, [2, 19]), o($VJ, [2, 20]), { 25: [1, 78] }, { 27: [1, 79] }, o($VJ, [2, 23]), { 35: 80, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 86, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 87, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 88, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 89, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 90, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 91, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 92, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 93, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 94, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 95, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 96, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 97, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 98, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 99, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 100, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 101, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 102, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 103, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 104, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, o($VO, [2, 59]), { 35: 105, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 106, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 107, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 108, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 109, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 110, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 111, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 112, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 113, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 114, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 115, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 20: 116, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 12: [1, 118], 33: [1, 117] }, { 35: 119, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 120, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 121, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 122, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 123, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 124, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 125, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 14: [1, 126] }, { 14: [1, 127] }, { 14: [1, 128] }, { 14: [1, 129] }, { 1: [2, 8] }, o($VH, [2, 15]), o($VI, [2, 17], { 21: 22, 19: 130, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4 }), o($VH, [2, 37], { 19: 20, 20: 21, 21: 22, 43: 23, 29: 49, 30: 61, 32: 62, 13: 131, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VJ, [2, 21]), o($VJ, [2, 22]), o($VO, [2, 39]), o($VP, [2, 71], { 75: 81, 35: 132, 76: $VK, 77: $VL, 79: $VM, 80: $VN }), o($VQ, [2, 73]), { 78: [1, 133] }, o($VQ, [2, 75]), o($VQ, [2, 76]), o($VO, [2, 40]), o($VO, [2, 41]), o($VO, [2, 42]), o($VO, [2, 43]), o($VO, [2, 44]), o($VO, [2, 45]), o($VO, [2, 46]), o($VO, [2, 47]), o($VO, [2, 48]), o($VO, [2, 49]), o($VO, [2, 50]), o($VO, [2, 51]), o($VO, [2, 52]), o($VO, [2, 53]), o($VO, [2, 54]), o($VO, [2, 55]), o($VO, [2, 56]), o($VO, [2, 57]), o($VO, [2, 58]), o($VO, [2, 60]), o($VO, [2, 61]), o($VO, [2, 62]), o($VO, [2, 63]), o($VO, [2, 64]), o($VO, [2, 65]), o($VO, [2, 66]), o($VO, [2, 67]), o($VO, [2, 68]), o($VO, [2, 69]), o($VO, [2, 70]), { 31: 134, 42: [1, 135] }, { 12: [1, 136] }, { 33: [1, 137] }, o($VR, [2, 28]), o($VR, [2, 29]), o($VR, [2, 30]), o($VR, [2, 31]), o($VR, [2, 32]), o($VR, [2, 33]), o($VR, [2, 34]), { 1: [2, 9] }, { 1: [2, 10] }, { 1: [2, 11] }, { 1: [2, 12] }, o($VI, [2, 18]), o($VH, [2, 38]), o($VP, [2, 72]), o($VQ, [2, 74]), o($VO, [2, 24]), o($VO, [2, 35]), o($VS, [2, 25]), o($VS, [2, 26], { 12: [1, 138] }), o($VS, [2, 27])],\n    defaultActions: { 2: [2, 1], 3: [2, 2], 4: [2, 7], 5: [2, 3], 6: [2, 4], 7: [2, 5], 8: [2, 6], 74: [2, 8], 126: [2, 9], 127: [2, 10], 128: [2, 11], 129: [2, 12] },\n    parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var pre = this.pastInput();\n        var c2 = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c2 + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 6;\n            break;\n          case 1:\n            return 7;\n            break;\n          case 2:\n            return 8;\n            break;\n          case 3:\n            return 9;\n            break;\n          case 4:\n            return 22;\n            break;\n          case 5:\n            return 23;\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 24;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 26;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            break;\n          case 14:\n            c;\n            break;\n          case 15:\n            return 12;\n            break;\n          case 16:\n            break;\n          case 17:\n            return 11;\n            break;\n          case 18:\n            return 15;\n            break;\n          case 19:\n            return 16;\n            break;\n          case 20:\n            return 17;\n            break;\n          case 21:\n            return 18;\n            break;\n          case 22:\n            this.begin(\"person_ext\");\n            return 45;\n            break;\n          case 23:\n            this.begin(\"person\");\n            return 44;\n            break;\n          case 24:\n            this.begin(\"system_ext_queue\");\n            return 51;\n            break;\n          case 25:\n            this.begin(\"system_ext_db\");\n            return 50;\n            break;\n          case 26:\n            this.begin(\"system_ext\");\n            return 49;\n            break;\n          case 27:\n            this.begin(\"system_queue\");\n            return 48;\n            break;\n          case 28:\n            this.begin(\"system_db\");\n            return 47;\n            break;\n          case 29:\n            this.begin(\"system\");\n            return 46;\n            break;\n          case 30:\n            this.begin(\"boundary\");\n            return 37;\n            break;\n          case 31:\n            this.begin(\"enterprise_boundary\");\n            return 34;\n            break;\n          case 32:\n            this.begin(\"system_boundary\");\n            return 36;\n            break;\n          case 33:\n            this.begin(\"container_ext_queue\");\n            return 57;\n            break;\n          case 34:\n            this.begin(\"container_ext_db\");\n            return 56;\n            break;\n          case 35:\n            this.begin(\"container_ext\");\n            return 55;\n            break;\n          case 36:\n            this.begin(\"container_queue\");\n            return 54;\n            break;\n          case 37:\n            this.begin(\"container_db\");\n            return 53;\n            break;\n          case 38:\n            this.begin(\"container\");\n            return 52;\n            break;\n          case 39:\n            this.begin(\"container_boundary\");\n            return 38;\n            break;\n          case 40:\n            this.begin(\"component_ext_queue\");\n            return 63;\n            break;\n          case 41:\n            this.begin(\"component_ext_db\");\n            return 62;\n            break;\n          case 42:\n            this.begin(\"component_ext\");\n            return 61;\n            break;\n          case 43:\n            this.begin(\"component_queue\");\n            return 60;\n            break;\n          case 44:\n            this.begin(\"component_db\");\n            return 59;\n            break;\n          case 45:\n            this.begin(\"component\");\n            return 58;\n            break;\n          case 46:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 47:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 48:\n            this.begin(\"node_l\");\n            return 40;\n            break;\n          case 49:\n            this.begin(\"node_r\");\n            return 41;\n            break;\n          case 50:\n            this.begin(\"rel\");\n            return 64;\n            break;\n          case 51:\n            this.begin(\"birel\");\n            return 65;\n            break;\n          case 52:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 53:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 54:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 55:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 56:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 57:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 58:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 59:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 60:\n            this.begin(\"rel_b\");\n            return 70;\n            break;\n          case 61:\n            this.begin(\"rel_index\");\n            return 71;\n            break;\n          case 62:\n            this.begin(\"update_el_style\");\n            return 72;\n            break;\n          case 63:\n            this.begin(\"update_rel_style\");\n            return 73;\n            break;\n          case 64:\n            this.begin(\"update_layout_config\");\n            return 74;\n            break;\n          case 65:\n            return \"EOF_IN_STRUCT\";\n            break;\n          case 66:\n            this.begin(\"attribute\");\n            return \"ATTRIBUTE_EMPTY\";\n            break;\n          case 67:\n            this.begin(\"attribute\");\n            break;\n          case 68:\n            this.popState();\n            this.popState();\n            break;\n          case 69:\n            return 80;\n            break;\n          case 70:\n            break;\n          case 71:\n            return 80;\n            break;\n          case 72:\n            this.begin(\"string\");\n            break;\n          case 73:\n            this.popState();\n            break;\n          case 74:\n            return \"STR\";\n            break;\n          case 75:\n            this.begin(\"string_kv\");\n            break;\n          case 76:\n            this.begin(\"string_kv_key\");\n            return \"STR_KEY\";\n            break;\n          case 77:\n            this.popState();\n            this.begin(\"string_kv_value\");\n            break;\n          case 78:\n            return \"STR_VALUE\";\n            break;\n          case 79:\n            this.popState();\n            this.popState();\n            break;\n          case 80:\n            return \"STR\";\n            break;\n          case 81:\n            return \"LBRACE\";\n            break;\n          case 82:\n            return \"RBRACE\";\n            break;\n          case 83:\n            return \"SPACE\";\n            break;\n          case 84:\n            return \"EOL\";\n            break;\n          case 85:\n            return 14;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:title\\s[^#\\n;]+)/, /^(?:accDescription\\s[^#\\n;]+)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/, /^(?:%%[^\\n]*(\\r?\\n)*)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:C4Context\\b)/, /^(?:C4Container\\b)/, /^(?:C4Component\\b)/, /^(?:C4Dynamic\\b)/, /^(?:C4Deployment\\b)/, /^(?:Person_Ext\\b)/, /^(?:Person\\b)/, /^(?:SystemQueue_Ext\\b)/, /^(?:SystemDb_Ext\\b)/, /^(?:System_Ext\\b)/, /^(?:SystemQueue\\b)/, /^(?:SystemDb\\b)/, /^(?:System\\b)/, /^(?:Boundary\\b)/, /^(?:Enterprise_Boundary\\b)/, /^(?:System_Boundary\\b)/, /^(?:ContainerQueue_Ext\\b)/, /^(?:ContainerDb_Ext\\b)/, /^(?:Container_Ext\\b)/, /^(?:ContainerQueue\\b)/, /^(?:ContainerDb\\b)/, /^(?:Container\\b)/, /^(?:Container_Boundary\\b)/, /^(?:ComponentQueue_Ext\\b)/, /^(?:ComponentDb_Ext\\b)/, /^(?:Component_Ext\\b)/, /^(?:ComponentQueue\\b)/, /^(?:ComponentDb\\b)/, /^(?:Component\\b)/, /^(?:Deployment_Node\\b)/, /^(?:Node\\b)/, /^(?:Node_L\\b)/, /^(?:Node_R\\b)/, /^(?:Rel\\b)/, /^(?:BiRel\\b)/, /^(?:Rel_Up\\b)/, /^(?:Rel_U\\b)/, /^(?:Rel_Down\\b)/, /^(?:Rel_D\\b)/, /^(?:Rel_Left\\b)/, /^(?:Rel_L\\b)/, /^(?:Rel_Right\\b)/, /^(?:Rel_R\\b)/, /^(?:Rel_Back\\b)/, /^(?:RelIndex\\b)/, /^(?:UpdateElementStyle\\b)/, /^(?:UpdateRelStyle\\b)/, /^(?:UpdateLayoutConfig\\b)/, /^(?:$)/, /^(?:[(][ ]*[,])/, /^(?:[(])/, /^(?:[)])/, /^(?:,,)/, /^(?:,)/, /^(?:[ ]*[\"][\"])/, /^(?:[ ]*[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:[ ]*[\\$])/, /^(?:[^=]*)/, /^(?:[=][ ]*[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:[^,]+)/, /^(?:\\{)/, /^(?:\\})/, /^(?:[\\s]+)/, /^(?:[\\n\\r]+)/, /^(?:$)/],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"string_kv_value\": { \"rules\": [78, 79], \"inclusive\": false }, \"string_kv_key\": { \"rules\": [77], \"inclusive\": false }, \"string_kv\": { \"rules\": [76], \"inclusive\": false }, \"string\": { \"rules\": [73, 74], \"inclusive\": false }, \"attribute\": { \"rules\": [68, 69, 70, 71, 72, 75, 80], \"inclusive\": false }, \"update_layout_config\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_rel_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_el_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_b\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_d\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_u\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_bi\": { \"rules\": [], \"inclusive\": false }, \"rel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"index\": { \"rules\": [], \"inclusive\": false }, \"rel_index\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext_queue\": { \"rules\": [], \"inclusive\": false }, \"component_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"birel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"enterprise_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 81, 82, 83, 84, 85], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar c4Diagram_default = parser;\n\n// src/diagrams/c4/c4Db.js\nvar c4ShapeArray = [];\nvar boundaryParseStack = [\"\"];\nvar currentBoundaryParse = \"global\";\nvar parentBoundaryParse = \"\";\nvar boundaries = [\n  {\n    alias: \"global\",\n    label: { text: \"global\" },\n    type: { text: \"global\" },\n    tags: null,\n    link: null,\n    parentBoundary: \"\"\n  }\n];\nvar rels = [];\nvar title = \"\";\nvar wrapEnabled = false;\nvar c4ShapeInRow = 4;\nvar c4BoundaryInRow = 2;\nvar c4Type;\nvar getC4Type = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return c4Type;\n}, \"getC4Type\");\nvar setC4Type = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(c4TypeParam) {\n  let sanitizedText = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitizeText)(c4TypeParam, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)());\n  c4Type = sanitizedText;\n}, \"setC4Type\");\nvar addRel = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(type, from, to, label, techn, descr, sprite, tags, link) {\n  if (type === void 0 || type === null || from === void 0 || from === null || to === void 0 || to === null || label === void 0 || label === null) {\n    return;\n  }\n  let rel = {};\n  const old = rels.find((rel2) => rel2.from === from && rel2.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n  if (techn === void 0 || techn === null) {\n    rel.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    rel.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n}, \"addRel\");\nvar addPersonOrSystem = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, alias, label, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem2) => personOrSystem2.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n  if (label === void 0 || label === null) {\n    personOrSystem.label = { text: \"\" };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n  if (descr === void 0 || descr === null) {\n    personOrSystem.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n}, \"addPersonOrSystem\");\nvar addContainer = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let container = {};\n  const old = c4ShapeArray.find((container2) => container2.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n  if (label === void 0 || label === null) {\n    container.label = { text: \"\" };\n  } else {\n    container.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    container.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    container.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n}, \"addContainer\");\nvar addComponent = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let component = {};\n  const old = c4ShapeArray.find((component2) => component2.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n  if (label === void 0 || label === null) {\n    component.label = { text: \"\" };\n  } else {\n    component.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    component.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    component.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n}, \"addComponent\");\nvar addPersonOrSystemBoundary = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"system\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addPersonOrSystemBoundary\");\nvar addContainerBoundary = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"container\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addContainerBoundary\");\nvar addDeploymentNode = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(nodeType, alias, label, type, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"node\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    boundary.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addDeploymentNode\");\nvar popBoundaryParseStack = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"popBoundaryParseStack\");\nvar updateElStyle = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, elementName, bgColor, fontColor, borderColor, shadowing, shape, sprite, techn, legendText, legendSprite) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === void 0) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === void 0) {\n      return;\n    }\n  }\n  if (bgColor !== void 0 && bgColor !== null) {\n    if (typeof bgColor === \"object\") {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== void 0 && fontColor !== null) {\n    if (typeof fontColor === \"object\") {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== void 0 && borderColor !== null) {\n    if (typeof borderColor === \"object\") {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== void 0 && shadowing !== null) {\n    if (typeof shadowing === \"object\") {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== void 0 && shape !== null) {\n    if (typeof shape === \"object\") {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== void 0 && sprite !== null) {\n    if (typeof sprite === \"object\") {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== void 0 && techn !== null) {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== void 0 && legendText !== null) {\n    if (typeof legendText === \"object\") {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== void 0 && legendSprite !== null) {\n    if (typeof legendSprite === \"object\") {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n}, \"updateElStyle\");\nvar updateRelStyle = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, from, to, textColor, lineColor, offsetX, offsetY) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === void 0) {\n    return;\n  }\n  if (textColor !== void 0 && textColor !== null) {\n    if (typeof textColor === \"object\") {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== void 0 && lineColor !== null) {\n    if (typeof lineColor === \"object\") {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== void 0 && offsetX !== null) {\n    if (typeof offsetX === \"object\") {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== void 0 && offsetY !== null) {\n    if (typeof offsetY === \"object\") {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n}, \"updateRelStyle\");\nvar updateLayoutConfig = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow;\n  let c4BoundaryInRowValue = c4BoundaryInRow;\n  if (typeof c4ShapeInRowParam === \"object\") {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === \"object\") {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow = c4BoundaryInRowValue;\n  }\n}, \"updateLayoutConfig\");\nvar getC4ShapeInRow = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return c4ShapeInRow;\n}, \"getC4ShapeInRow\");\nvar getC4BoundaryInRow = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return c4BoundaryInRow;\n}, \"getC4BoundaryInRow\");\nvar getCurrentBoundaryParse = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return currentBoundaryParse;\n}, \"getCurrentBoundaryParse\");\nvar getParentBoundaryParse = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return parentBoundaryParse;\n}, \"getParentBoundaryParse\");\nvar getC4ShapeArray = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n}, \"getC4ShapeArray\");\nvar getC4Shape = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n}, \"getC4Shape\");\nvar getC4ShapeKeys = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n}, \"getC4ShapeKeys\");\nvar getBoundaries = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n}, \"getBoundaries\");\nvar getBoundarys = getBoundaries;\nvar getRels = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return rels;\n}, \"getRels\");\nvar getTitle = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return title;\n}, \"getTitle\");\nvar setWrap = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(wrapSetting) {\n  wrapEnabled = wrapSetting;\n}, \"setWrap\");\nvar autoWrap = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return wrapEnabled;\n}, \"autoWrap\");\nvar clear = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: \"global\",\n      label: { text: \"global\" },\n      type: { text: \"global\" },\n      tags: null,\n      link: null,\n      parentBoundary: \"\"\n    }\n  ];\n  parentBoundaryParse = \"\";\n  currentBoundaryParse = \"global\";\n  boundaryParseStack = [\"\"];\n  rels = [];\n  boundaryParseStack = [\"\"];\n  title = \"\";\n  wrapEnabled = false;\n  c4ShapeInRow = 4;\n  c4BoundaryInRow = 2;\n}, \"clear\");\nvar LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25\n};\nvar ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nvar PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nvar setTitle = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(txt) {\n  let sanitizedText = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitizeText)(txt, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)());\n  title = sanitizedText;\n}, \"setTitle\");\nvar c4Db_default = {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.setAccTitle,\n  getAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.getAccTitle,\n  getAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.getAccDescription,\n  setAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.setAccDescription,\n  getConfig: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(() => (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().c4, \"getConfig\"),\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type\n  // apply,\n};\n\n// src/diagrams/c4/c4Renderer.js\n\n\n// src/diagrams/c4/svgDraw.js\n\nvar drawRect2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, rectData) {\n  return (0,_chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__.drawRect)(elem, rectData);\n}, \"drawRect\");\nvar drawImage = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, width, height, x, y, link) {\n  const imageElem = elem.append(\"image\");\n  imageElem.attr(\"width\", width);\n  imageElem.attr(\"height\", height);\n  imageElem.attr(\"x\", x);\n  imageElem.attr(\"y\", y);\n  let sanitizedLink = link.startsWith(\"data:image/png;base64\") ? link : (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_4__.sanitizeUrl)(link);\n  imageElem.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawRels = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((elem, rels2, conf2) => {\n  const relsElem = elem.append(\"g\");\n  let i = 0;\n  for (let rel of rels2) {\n    let textColor = rel.textColor ? rel.textColor : \"#444444\";\n    let strokeColor = rel.lineColor ? rel.lineColor : \"#444444\";\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n    let url = \"\";\n    if (i === 0) {\n      let line = relsElem.append(\"line\");\n      line.attr(\"x1\", rel.startPoint.x);\n      line.attr(\"y1\", rel.startPoint.y);\n      line.attr(\"x2\", rel.endPoint.x);\n      line.attr(\"y2\", rel.endPoint.y);\n      line.attr(\"stroke-width\", \"1\");\n      line.attr(\"stroke\", strokeColor);\n      line.style(\"fill\", \"none\");\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append(\"path\");\n      line.attr(\"fill\", \"none\").attr(\"stroke-width\", \"1\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,starty Qcontrolx,controly stopx,stopy \".replaceAll(\"startx\", rel.startPoint.x).replaceAll(\"starty\", rel.startPoint.y).replaceAll(\n          \"controlx\",\n          rel.startPoint.x + (rel.endPoint.x - rel.startPoint.x) / 2 - (rel.endPoint.x - rel.startPoint.x) / 4\n        ).replaceAll(\"controly\", rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2).replaceAll(\"stopx\", rel.endPoint.x).replaceAll(\"stopy\", rel.endPoint.y)\n      );\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n    }\n    let messageConf = conf2.messageFont();\n    _drawTextCandidateFunc(conf2)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n    if (rel.techn && rel.techn.text !== \"\") {\n      messageConf = conf2.messageFont();\n      _drawTextCandidateFunc(conf2)(\n        \"[\" + rel.techn.text + \"]\",\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + conf2.messageFontSize + 5 + offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, \"font-style\": \"italic\" },\n        messageConf\n      );\n    }\n  }\n}, \"drawRels\");\nvar drawBoundary = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, boundary, conf2) {\n  const boundaryElem = elem.append(\"g\");\n  let fillColor = boundary.bgColor ? boundary.bgColor : \"none\";\n  let strokeColor = boundary.borderColor ? boundary.borderColor : \"#444444\";\n  let fontColor = boundary.fontColor ? boundary.fontColor : \"black\";\n  let attrsValue = { \"stroke-width\": 1, \"stroke-dasharray\": \"7.0,7.0\" };\n  if (boundary.nodeType) {\n    attrsValue = { \"stroke-width\": 1 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue\n  };\n  drawRect2(boundaryElem, rectData);\n  let boundaryConf = conf2.boundaryFont();\n  boundaryConf.fontWeight = \"bold\";\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: \"#444444\" },\n    boundaryConf\n  );\n  if (boundary.type && boundary.type.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n  if (boundary.descr && boundary.descr.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n}, \"drawBoundary\");\nvar drawC4Shape = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, c4Shape, conf2) {\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf2[c4Shape.typeC4Shape.text + \"_bg_color\"];\n  let strokeColor = c4Shape.borderColor ? c4Shape.borderColor : conf2[c4Shape.typeC4Shape.text + \"_border_color\"];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : \"#FFFFFF\";\n  let personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n      break;\n    case \"external_person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=\";\n      break;\n  }\n  const c4ShapeElem = elem.append(\"g\");\n  c4ShapeElem.attr(\"class\", \"person-man\");\n  const rect = (0,_chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n    case \"system\":\n    case \"external_system\":\n    case \"container\":\n    case \"external_container\":\n    case \"component\":\n    case \"external_component\":\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { \"stroke-width\": 0.5 };\n      drawRect2(c4ShapeElem, rect);\n      break;\n    case \"system_db\":\n    case \"external_system_db\":\n    case \"container_db\":\n    case \"external_container_db\":\n    case \"component_db\":\n    case \"external_component_db\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2).replaceAll(\"height\", c4Shape.height)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2)\n      );\n      break;\n    case \"system_queue\":\n    case \"external_system_queue\":\n    case \"container_queue\":\n    case \"external_container_queue\":\n    case \"component_queue\":\n    case \"external_component_queue\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"width\", c4Shape.width).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half\".replaceAll(\"startx\", c4Shape.x + c4Shape.width).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      break;\n  }\n  let c4ShapeFontConf = getC4ShapeFont(conf2, c4Shape.typeC4Shape.text);\n  c4ShapeElem.append(\"text\").attr(\"fill\", fontColor).attr(\"font-family\", c4ShapeFontConf.fontFamily).attr(\"font-size\", c4ShapeFontConf.fontSize - 2).attr(\"font-style\", \"italic\").attr(\"lengthAdjust\", \"spacing\").attr(\"textLength\", c4Shape.typeC4Shape.width).attr(\"x\", c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2).attr(\"y\", c4Shape.y + c4Shape.typeC4Shape.Y).text(\"<<\" + c4Shape.typeC4Shape.text + \">>\");\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n  let textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontWeight = \"bold\";\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n  textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontColor = fontColor;\n  if (c4Shape.techn && c4Shape.techn?.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  }\n  if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n    textFontConf = conf2.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n  return c4Shape.height;\n}, \"drawC4Shape\");\nvar insertDatabaseIcon = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n}, \"insertDatabaseIcon\");\nvar insertComputerIcon = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n}, \"insertComputerIcon\");\nvar insertClockIcon = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n}, \"insertClockIcon\");\nvar insertArrowHead = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\");\n}, \"insertArrowHead\");\nvar insertArrowEnd = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowend\").attr(\"refX\", 1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 10 0 L 0 5 L 10 10 z\");\n}, \"insertArrowEnd\");\nvar insertArrowFilledHead = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertArrowFilledHead\");\nvar insertDynamicNumber = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n}, \"insertDynamicNumber\");\nvar insertArrowCrossHead = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 16).attr(\"refY\", 4);\n  marker.append(\"path\").attr(\"fill\", \"black\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 9,2 V 6 L16,4 Z\");\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 0,1 L 6,7 M 6,1 L 0,7\");\n}, \"insertArrowCrossHead\");\nvar getC4ShapeFont = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"getC4ShapeFont\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { fontSize, fontFamily, fontWeight } = conf2;\n    const lines = content.split(_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - fontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"middle\").style(\"font-size\", fontSize).style(\"font-weight\", fontWeight).style(\"font-family\", fontFamily);\n      text.append(\"tspan\").attr(\"dy\", dy).text(lines[i]).attr(\"alignment-baseline\", \"mathematical\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawBoundary,\n  drawC4Shape,\n  drawRels,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon\n};\n\n// src/diagrams/c4/c4Renderer.js\nvar globalBoundaryMaxX = 0;\nvar globalBoundaryMaxY = 0;\nvar c4ShapeInRow2 = 4;\nvar c4BoundaryInRow2 = 2;\nparser.yy = c4Db_default;\nvar conf = {};\nvar Bounds = class {\n  static {\n    (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(this, \"Bounds\");\n  }\n  constructor(diagObj) {\n    this.name = \"\";\n    this.data = {};\n    this.data.startx = void 0;\n    this.data.stopx = void 0;\n    this.data.starty = void 0;\n    this.data.stopy = void 0;\n    this.data.widthLimit = void 0;\n    this.nextData = {};\n    this.nextData.startx = void 0;\n    this.nextData.stopx = void 0;\n    this.nextData.starty = void 0;\n    this.nextData.stopy = void 0;\n    this.nextData.cnt = 0;\n    setConf(diagObj.db.getConfig());\n  }\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx = this.nextData.startx === this.nextData.stopx ? this.nextData.stopx + c4Shape.margin : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (_startx >= this.data.widthLimit || _stopx >= this.data.widthLimit || this.nextData.cnt > c4ShapeInRow2) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n    this.updateVal(this.data, \"startx\", _startx, Math.min);\n    this.updateVal(this.data, \"starty\", _starty, Math.min);\n    this.updateVal(this.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.data, \"stopy\", _stopy, Math.max);\n    this.updateVal(this.nextData, \"startx\", _startx, Math.min);\n    this.updateVal(this.nextData, \"starty\", _starty, Math.min);\n    this.updateVal(this.nextData, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.nextData, \"stopy\", _stopy, Math.max);\n  }\n  init(diagObj) {\n    this.name = \"\";\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      widthLimit: void 0\n    };\n    this.nextData = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      cnt: 0\n    };\n    setConf(diagObj.db.getConfig());\n  }\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n};\nvar setConf = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(cnf) {\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.assignWithDepth_default)(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n}, \"setConf\");\nvar c4ShapeFont = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"c4ShapeFont\");\nvar boundaryFont = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight\n  };\n}, \"boundaryFont\");\nvar messageFont = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n}, \"messageFont\");\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.wrapLabel)(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.common_default.lineBreakRegex).length;\n      c4Shape[textType].width = textLimitWidth;\n      c4Shape[textType].height = (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextHeight)(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.common_default.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextHeight)(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n    }\n  }\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(calcC4ShapeTextWH, \"calcC4ShapeTextWH\");\nvar drawBoundary2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(diagram2, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n  boundary.label.y = conf.c4ShapeMargin - 35;\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = \"bold\";\n  let textLimitWidth = (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH(\"label\", boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n  svgDraw_default.drawBoundary(diagram2, boundary, conf);\n}, \"drawBoundary\");\nvar drawC4ShapeArray = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(currentBounds, diagram2, c4ShapeArray2, c4ShapeKeys) {\n  let Y = 0;\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray2[c4ShapeKey];\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(\n      \"\\xAB\" + c4Shape.typeC4Shape.text + \"\\xBB\",\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case \"person\":\n      case \"external_person\":\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\"label\", c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape.label.Y = Y + 8;\n    Y = c4Shape.label.Y + c4Shape.label.height;\n    if (c4Shape.type && c4Shape.type.text !== \"\") {\n      c4Shape.type.text = \"[\" + c4Shape.type.text + \"]\";\n      let c4ShapeTypeConf2 = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"type\", c4Shape, c4ShapeTextWrap, c4ShapeTypeConf2, textLimitWidth);\n      c4Shape.type.Y = Y + 5;\n      Y = c4Shape.type.Y + c4Shape.type.height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== \"\") {\n      c4Shape.techn.text = \"[\" + c4Shape.techn.text + \"]\";\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH(\"techn\", c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape.techn.Y = Y + 5;\n      Y = c4Shape.techn.Y + c4Shape.techn.height;\n    }\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n    if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"descr\", c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape.descr.Y = Y + 20;\n      Y = c4Shape.descr.Y + c4Shape.descr.height;\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape.descr.textLines * 5;\n    }\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n    currentBounds.insert(c4Shape);\n    svgDraw_default.drawC4Shape(diagram2, c4Shape, conf);\n  }\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n}, \"drawC4ShapeArray\");\nvar Point = class {\n  static {\n    (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(this, \"Point\");\n  }\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n};\nvar getIntersectPoint = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(fromNode, endPoint) {\n  let x1 = fromNode.x;\n  let y1 = fromNode.y;\n  let x2 = endPoint.x;\n  let y2 = endPoint.y;\n  let fromCenterX = x1 + fromNode.width / 2;\n  let fromCenterY = y1 + fromNode.height / 2;\n  let dx = Math.abs(x1 - x2);\n  let dy = Math.abs(y1 - y2);\n  let tanDYX = dy / dx;\n  let fromDYX = fromNode.height / fromNode.width;\n  let returnPoint = null;\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + fromNode.height / 2 * dx / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - fromNode.width / 2 * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - fromNode.height / 2 * dx / dy, y1);\n    }\n  }\n  return returnPoint;\n}, \"getIntersectPoint\");\nvar getIntersectPoints = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint, endPoint };\n}, \"getIntersectPoints\");\nvar drawRels2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(diagram2, rels2, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels2) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === \"C4Dynamic\") {\n      rel.label.text = i + \": \" + rel.label.text;\n    }\n    let textLimitWidth = (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(rel.label.text, relConf);\n    calcC4ShapeTextWH(\"label\", rel, relTextWrap, relConf, textLimitWidth);\n    if (rel.techn && rel.techn.text !== \"\") {\n      textLimitWidth = (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(rel.techn.text, relConf);\n      calcC4ShapeTextWH(\"techn\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    if (rel.descr && rel.descr.text !== \"\") {\n      textLimitWidth = (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(rel.descr.text, relConf);\n      calcC4ShapeTextWH(\"descr\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw_default.drawRels(diagram2, rels2, conf);\n}, \"drawRels\");\nfunction drawInsideBoundary(diagram2, parentBoundaryAlias, parentBounds, currentBoundaries, diagObj) {\n  let currentBounds = new Bounds(diagObj);\n  currentBounds.data.widthLimit = parentBounds.data.widthLimit / Math.min(c4BoundaryInRow2, currentBoundaries.length);\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\n      \"label\",\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary.label.Y = Y + 8;\n    Y = currentBoundary.label.Y + currentBoundary.label.height;\n    if (currentBoundary.type && currentBoundary.type.text !== \"\") {\n      currentBoundary.type.text = \"[\" + currentBoundary.type.text + \"]\";\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        \"type\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.type.Y = Y + 5;\n      Y = currentBoundary.type.Y + currentBoundary.type.height;\n    }\n    if (currentBoundary.descr && currentBoundary.descr.text !== \"\") {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        \"descr\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.descr.Y = Y + 20;\n      Y = currentBoundary.descr.Y + currentBoundary.descr.height;\n    }\n    if (i == 0 || i % c4BoundaryInRow2 === 0) {\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      let _x = currentBounds.data.stopx !== currentBounds.data.startx ? currentBounds.data.stopx + conf.diagramMarginX : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram2,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundarys(parentBoundaryAlias);\n    if (nextCurrentBoundaries.length > 0) {\n      drawInsideBoundary(\n        diagram2,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    if (currentBoundary.alias !== \"global\") {\n      drawBoundary2(diagram2, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(drawInsideBoundary, \"drawInsideBoundary\");\nvar draw = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(_text, id, _version, diagObj) {\n  conf = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().c4;\n  const securityLevel = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(\"body\");\n  let db = diagObj.db;\n  diagObj.db.setWrap(conf.wrap);\n  c4ShapeInRow2 = db.getC4ShapeInRow();\n  c4BoundaryInRow2 = db.getC4BoundaryInRow();\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.log.debug(`C:${JSON.stringify(conf, null, 2)}`);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(`[id=\"${id}\"]`);\n  svgDraw_default.insertComputerIcon(diagram2);\n  svgDraw_default.insertDatabaseIcon(diagram2);\n  svgDraw_default.insertClockIcon(diagram2);\n  let screenBounds = new Bounds(diagObj);\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n  const title2 = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundarys(\"\");\n  drawInsideBoundary(diagram2, \"\", screenBounds, currentBoundaries, diagObj);\n  svgDraw_default.insertArrowHead(diagram2);\n  svgDraw_default.insertArrowEnd(diagram2);\n  svgDraw_default.insertArrowCrossHead(diagram2);\n  svgDraw_default.insertArrowFilledHead(diagram2);\n  drawRels2(diagram2, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n  const box = screenBounds.data;\n  let boxHeight = box.stopy - box.starty;\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title2) {\n    diagram2.append(\"text\").text(title2).attr(\"x\", (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX).attr(\"y\", box.starty + conf.diagramMarginY);\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.configureSvgSize)(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title2 ? 60 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.log.debug(`models:`, box);\n}, \"draw\");\nvar c4Renderer_default = {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary: drawBoundary2,\n  setConf,\n  draw\n};\n\n// src/diagrams/c4/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((options) => `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/c4/c4Diagram.ts\nvar diagram = {\n  parser: c4Diagram_default,\n  db: c4Db_default,\n  renderer: c4Renderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(({ c4, wrap }) => {\n    c4Renderer_default.setConf(c4);\n    c4Db_default.setWrap(wrap);\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-6F5ED5ID.mjs\n"));

/***/ }),

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/chunk-ASOPGD6M.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-ASOPGD6M.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"drawBackgroundRect\": function() { return /* binding */ drawBackgroundRect; },\n/* harmony export */   \"drawEmbeddedImage\": function() { return /* binding */ drawEmbeddedImage; },\n/* harmony export */   \"drawImage\": function() { return /* binding */ drawImage; },\n/* harmony export */   \"drawRect\": function() { return /* binding */ drawRect; },\n/* harmony export */   \"drawText\": function() { return /* binding */ drawText; },\n/* harmony export */   \"getNoteRect\": function() { return /* binding */ getNoteRect; },\n/* harmony export */   \"getTextObj\": function() { return /* binding */ getTextObj; }\n/* harmony export */ });\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @braintree/sanitize-url */ \"./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n// src/diagrams/common/svgDrawCommon.ts\n\nvar drawRect = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, textData) => {\n  const nText = textData.text.replace(_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/chunk-ASOPGD6M.mjs\n"));

/***/ })

}]);