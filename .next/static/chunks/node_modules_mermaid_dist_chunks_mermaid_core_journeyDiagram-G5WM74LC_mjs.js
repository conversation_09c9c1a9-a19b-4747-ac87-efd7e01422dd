"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_mermaid_dist_chunks_mermaid_core_journeyDiagram-G5WM74LC_mjs"],{

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/chunk-ASOPGD6M.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-ASOPGD6M.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"drawBackgroundRect\": function() { return /* binding */ drawBackgroundRect; },\n/* harmony export */   \"drawEmbeddedImage\": function() { return /* binding */ drawEmbeddedImage; },\n/* harmony export */   \"drawImage\": function() { return /* binding */ drawImage; },\n/* harmony export */   \"drawRect\": function() { return /* binding */ drawRect; },\n/* harmony export */   \"drawText\": function() { return /* binding */ drawText; },\n/* harmony export */   \"getNoteRect\": function() { return /* binding */ getNoteRect; },\n/* harmony export */   \"getTextObj\": function() { return /* binding */ getTextObj; }\n/* harmony export */ });\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @braintree/sanitize-url */ \"./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n// src/diagrams/common/svgDrawCommon.ts\n\nvar drawRect = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, textData) => {\n  const nText = textData.text.replace(_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/chunk-ASOPGD6M.mjs\n"));

/***/ }),

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-G5WM74LC.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-G5WM74LC.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"diagram\": function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-ASOPGD6M.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-ASOPGD6M.mjs\");\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3 */ \"./node_modules/d3/src/index.js\");\n\n\n\n// src/diagrams/user-journey/parser/journey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 18], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 14];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"journey\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"taskName\": 18, \"taskData\": 19, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"journey\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 18: \"taskName\", 19: \"taskData\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2]],\n    performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 13:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 15, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 16] }, { 15: [1, 17] }, o($V0, [2, 11]), o($V0, [2, 12]), { 19: [1, 18] }, o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 13])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n            break;\n          case 6:\n            return 11;\n            break;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 17;\n            break;\n          case 15:\n            return 18;\n            break;\n          case 16:\n            return 19;\n            break;\n          case 17:\n            return \":\";\n            break;\n          case 18:\n            return 6;\n            break;\n          case 19:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:journey\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^#:\\n;]+)/i, /^(?:[^#:\\n;]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar journey_default = parser;\n\n// src/diagrams/user-journey/journeyDb.js\nvar currentSection = \"\";\nvar sections = [];\nvar tasks = [];\nvar rawTasks = [];\nvar clear2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.clear)();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n}, \"getTasks\");\nvar updateActors = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  const tempActors = [];\n  tasks.forEach((task) => {\n    if (task.people) {\n      tempActors.push(...task.people);\n    }\n  });\n  const unique = new Set(tempActors);\n  return [...unique].sort();\n}, \"updateActors\");\nvar addTask = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr, taskData) {\n  const pieces = taskData.substr(1).split(\":\");\n  let score = 0;\n  let peeps = [];\n  if (pieces.length === 1) {\n    score = Number(pieces[0]);\n    peeps = [];\n  } else {\n    score = Number(pieces[0]);\n    peeps = pieces[1].split(\",\");\n  }\n  const peopleList = peeps.map((s) => s.trim());\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    people: peopleList,\n    task: descr,\n    score\n  };\n  rawTasks.push(rawTask);\n}, \"addTask\");\nvar addTaskOrg = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  const compileTask = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(pos) {\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar getActors = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return updateActors();\n}, \"getActors\");\nvar journeyDb_default = {\n  getConfig: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(() => (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey, \"getConfig\"),\n  clear: clear2,\n  setDiagramTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.setDiagramTitle,\n  getDiagramTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getDiagramTitle,\n  setAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccTitle,\n  getAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccTitle,\n  setAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccDescription,\n  getAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  getActors\n};\n\n// src/diagrams/user-journey/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((options) => `.label {\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n    color: ${options.textColor};\n  }\n  .mouth {\n    stroke: #666;\n  }\n\n  line {\n    stroke: ${options.textColor}\n  }\n\n  .legend {\n    fill: ${options.textColor};\n  }\n\n  .label text {\n    fill: #333;\n  }\n  .label {\n    color: ${options.textColor}\n  }\n\n  .face {\n    ${options.faceColor ? `fill: ${options.faceColor}` : \"fill: #FFF8DC\"};\n    stroke: #999;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 1.5px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n    }\n    text-align: center;\n  }\n\n  .cluster rect {\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .task-type-0, .section-type-0  {\n    ${options.fillType0 ? `fill: ${options.fillType0}` : \"\"};\n  }\n  .task-type-1, .section-type-1  {\n    ${options.fillType0 ? `fill: ${options.fillType1}` : \"\"};\n  }\n  .task-type-2, .section-type-2  {\n    ${options.fillType0 ? `fill: ${options.fillType2}` : \"\"};\n  }\n  .task-type-3, .section-type-3  {\n    ${options.fillType0 ? `fill: ${options.fillType3}` : \"\"};\n  }\n  .task-type-4, .section-type-4  {\n    ${options.fillType0 ? `fill: ${options.fillType4}` : \"\"};\n  }\n  .task-type-5, .section-type-5  {\n    ${options.fillType0 ? `fill: ${options.fillType5}` : \"\"};\n  }\n  .task-type-6, .section-type-6  {\n    ${options.fillType0 ? `fill: ${options.fillType6}` : \"\"};\n  }\n  .task-type-7, .section-type-7  {\n    ${options.fillType0 ? `fill: ${options.fillType7}` : \"\"};\n  }\n\n  .actor-0 {\n    ${options.actor0 ? `fill: ${options.actor0}` : \"\"};\n  }\n  .actor-1 {\n    ${options.actor1 ? `fill: ${options.actor1}` : \"\"};\n  }\n  .actor-2 {\n    ${options.actor2 ? `fill: ${options.actor2}` : \"\"};\n  }\n  .actor-3 {\n    ${options.actor3 ? `fill: ${options.actor3}` : \"\"};\n  }\n  .actor-4 {\n    ${options.actor4 ? `fill: ${options.actor4}` : \"\"};\n  }\n  .actor-5 {\n    ${options.actor5 ? `fill: ${options.actor5}` : \"\"};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/user-journey/journeyRenderer.ts\n\n\n// src/diagrams/user-journey/svgDraw.js\n\nvar drawRect2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, rectData) {\n  return (0,_chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__.drawRect)(elem, rectData);\n}, \"drawRect\");\nvar drawFace = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc = (0,d3__WEBPACK_IMPORTED_MODULE_2__.arc)().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(smile, \"smile\");\n  function sad(face2) {\n    const arc = (0,d3__WEBPACK_IMPORTED_MODULE_2__.arc)().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(sad, \"sad\");\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(ambivalent, \"ambivalent\");\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n}, \"drawFace\");\nvar drawCircle = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n}, \"drawCircle\");\nvar drawText2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, textData) {\n  return (0,_chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__.drawText)(elem, textData);\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText2(elem, txtObject);\n}, \"drawLabel\");\nvar drawSection = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, section, conf2) {\n  const g = elem.append(\"g\");\n  const rect = (0,_chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf2.width * section.taskCount + // width of the tasks\n  conf2.diagramMarginX * (section.taskCount - 1);\n  rect.height = conf2.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect2(g, rect);\n  _drawTextCandidateFunc(conf2)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf2,\n    section.colour\n  );\n}, \"drawSection\");\nvar taskCount = -1;\nvar drawTask = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, task, conf2) {\n  const center = task.x + conf2.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = (0,_chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf2.width;\n  rect.height = conf2.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect2(g, rect);\n  let xPos = task.x + 14;\n  task.people.forEach((person) => {\n    const colour = task.actors[person].color;\n    const circle = {\n      cx: xPos,\n      cy: task.y,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      title: person,\n      pos: task.actors[person].position\n    };\n    drawCircle(g, circle);\n    xPos += 10;\n  });\n  _drawTextCandidateFunc(conf2)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf2,\n    task.colour\n  );\n}, \"drawTask\");\nvar drawBackgroundRect2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, bounds2) {\n  (0,_chunk_ASOPGD6M_mjs__WEBPACK_IMPORTED_MODULE_0__.drawBackgroundRect)(elem, bounds2);\n}, \"drawBackgroundRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2, colour) {\n    const { taskFontSize, taskFontFamily } = conf2;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar initGraphics = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n}, \"initGraphics\");\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawCircle,\n  drawSection,\n  drawText: drawText2,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect: drawBackgroundRect2,\n  initGraphics\n};\n\n// src/diagrams/user-journey/journeyRenderer.ts\nvar setConf = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(cnf) {\n  const keys = Object.keys(cnf);\n  keys.forEach(function(key) {\n    conf[key] = cnf[key];\n  });\n}, \"setConf\");\nvar actors = {};\nfunction drawActorLegend(diagram2) {\n  const conf2 = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\n  let yPos = 60;\n  Object.keys(actors).forEach((person) => {\n    const colour = actors[person].color;\n    const circleData = {\n      cx: 20,\n      cy: yPos,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      pos: actors[person].position\n    };\n    svgDraw_default.drawCircle(diagram2, circleData);\n    const labelData = {\n      x: 40,\n      y: yPos + 7,\n      fill: \"#666\",\n      text: person,\n      textMargin: conf2.boxTextMargin | 5\n    };\n    svgDraw_default.drawText(diagram2, labelData);\n    yPos += 20;\n  });\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawActorLegend, \"drawActorLegend\");\nvar conf = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\nvar LEFT_MARGIN = conf.leftMargin;\nvar draw = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(text, id, version, diagObj) {\n  const conf2 = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\n  const securityLevel = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_2__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_2__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_2__.select)(\"body\");\n  bounds.init();\n  const diagram2 = root.select(\"#\" + id);\n  svgDraw_default.initGraphics(diagram2);\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getDiagramTitle();\n  const actorNames = diagObj.db.getActors();\n  for (const member in actors) {\n    delete actors[member];\n  }\n  let actorPos = 0;\n  actorNames.forEach((actorName) => {\n    actors[actorName] = {\n      color: conf2.actorColours[actorPos % conf2.actorColours.length],\n      position: actorPos\n    };\n    actorPos++;\n  });\n  drawActorLegend(diagram2);\n  bounds.insert(0, 0, LEFT_MARGIN, Object.keys(actors).length * 50);\n  drawTasks(diagram2, tasks2, 0);\n  const box = bounds.getBounds();\n  if (title) {\n    diagram2.append(\"text\").text(title).attr(\"x\", LEFT_MARGIN).attr(\"font-size\", \"4ex\").attr(\"font-weight\", \"bold\").attr(\"y\", 25);\n  }\n  const height = box.stopy - box.starty + 2 * conf2.diagramMarginY;\n  const width = LEFT_MARGIN + box.stopx + 2 * conf2.diagramMarginX;\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.configureSvgSize)(diagram2, height, width, conf2.useMaxWidth);\n  diagram2.append(\"line\").attr(\"x1\", LEFT_MARGIN).attr(\"y1\", conf2.height * 4).attr(\"x2\", width - LEFT_MARGIN - 4).attr(\"y2\", conf2.height * 4).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  const extraVertForTitle = title ? 70 : 0;\n  diagram2.attr(\"viewBox\", `${box.startx} -25 ${width} ${height + extraVertForTitle}`);\n  diagram2.attr(\"preserveAspectRatio\", \"xMinYMin meet\");\n  diagram2.attr(\"height\", height + extraVertForTitle + 25);\n}, \"draw\");\nvar bounds = {\n  data: {\n    startx: void 0,\n    stopx: void 0,\n    starty: void 0,\n    stopy: void 0\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  init: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n    this.sequenceItems = [];\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0\n    };\n    this.verticalPos = 0;\n  }, \"init\"),\n  updateVal: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }, \"updateVal\"),\n  updateBounds: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startx, starty, stopx, stopy) {\n    const conf2 = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\n    const _self = this;\n    let cnt = 0;\n    function updateFn(type) {\n      return /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function updateItemBounds(item) {\n        cnt++;\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, \"starty\", starty - n * conf2.boxMargin, Math.min);\n        _self.updateVal(item, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        _self.updateVal(bounds.data, \"startx\", startx - n * conf2.boxMargin, Math.min);\n        _self.updateVal(bounds.data, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n        if (!(type === \"activation\")) {\n          _self.updateVal(item, \"startx\", startx - n * conf2.boxMargin, Math.min);\n          _self.updateVal(item, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n          _self.updateVal(bounds.data, \"starty\", starty - n * conf2.boxMargin, Math.min);\n          _self.updateVal(bounds.data, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        }\n      }, \"updateItemBounds\");\n    }\n    (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(updateFn, \"updateFn\");\n    this.sequenceItems.forEach(updateFn());\n  }, \"updateBounds\"),\n  insert: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startx, starty, stopx, stopy) {\n    const _startx = Math.min(startx, stopx);\n    const _stopx = Math.max(startx, stopx);\n    const _starty = Math.min(starty, stopy);\n    const _stopy = Math.max(starty, stopy);\n    this.updateVal(bounds.data, \"startx\", _startx, Math.min);\n    this.updateVal(bounds.data, \"starty\", _starty, Math.min);\n    this.updateVal(bounds.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(bounds.data, \"stopy\", _stopy, Math.max);\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  }, \"insert\"),\n  bumpVerticalPos: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = this.verticalPos;\n  }, \"bumpVerticalPos\"),\n  getVerticalPos: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n    return this.verticalPos;\n  }, \"getVerticalPos\"),\n  getBounds: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n    return this.data;\n  }, \"getBounds\")\n};\nvar fills = conf.sectionFills;\nvar textColours = conf.sectionColours;\nvar drawTasks = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(diagram2, tasks2, verticalPos) {\n  const conf2 = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\n  let lastSection = \"\";\n  const sectionVHeight = conf2.height * 2 + conf2.diagramMarginY;\n  const taskPos = verticalPos + sectionVHeight;\n  let sectionNumber = 0;\n  let fill = \"#CCC\";\n  let colour = \"black\";\n  let num = 0;\n  for (const [i, task] of tasks2.entries()) {\n    if (lastSection !== task.section) {\n      fill = fills[sectionNumber % fills.length];\n      num = sectionNumber % fills.length;\n      colour = textColours[sectionNumber % textColours.length];\n      let taskInSectionCount = 0;\n      const currentSection2 = task.section;\n      for (let taskIndex = i; taskIndex < tasks2.length; taskIndex++) {\n        if (tasks2[taskIndex].section == currentSection2) {\n          taskInSectionCount = taskInSectionCount + 1;\n        } else {\n          break;\n        }\n      }\n      const section = {\n        x: i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN,\n        y: 50,\n        text: task.section,\n        fill,\n        num,\n        colour,\n        taskCount: taskInSectionCount\n      };\n      svgDraw_default.drawSection(diagram2, section, conf2);\n      lastSection = task.section;\n      sectionNumber++;\n    }\n    const taskActors = task.people.reduce((acc, actorName) => {\n      if (actors[actorName]) {\n        acc[actorName] = actors[actorName];\n      }\n      return acc;\n    }, {});\n    task.x = i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN;\n    task.y = taskPos;\n    task.width = conf2.diagramMarginX;\n    task.height = conf2.diagramMarginY;\n    task.colour = colour;\n    task.fill = fill;\n    task.num = num;\n    task.actors = taskActors;\n    svgDraw_default.drawTask(diagram2, task, conf2);\n    bounds.insert(task.x, task.y, task.x + task.width + conf2.taskMargin, 300 + 5 * 30);\n  }\n}, \"drawTasks\");\nvar journeyRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/user-journey/journeyDiagram.ts\nvar diagram = {\n  parser: journey_default,\n  db: journeyDb_default,\n  renderer: journeyRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((cnf) => {\n    journeyRenderer_default.setConf(cnf.journey);\n    journeyDb_default.clear();\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-G5WM74LC.mjs\n"));

/***/ })

}]);