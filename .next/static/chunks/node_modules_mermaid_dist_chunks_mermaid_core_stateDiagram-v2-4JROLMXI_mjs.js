"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_mermaid_dist_chunks_mermaid_core_stateDiagram-v2-4JROLMXI_mjs"],{

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"getDiagramElement\": function() { return /* binding */ getDiagramElement; },\n/* harmony export */   \"setupViewPortForSVG\": function() { return /* binding */ setupViewPortForSVG; }\n/* harmony export */ });\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3 */ \"./node_modules/d3/src/index.js\");\n\n\n// src/rendering-util/insertElementsForSize.js\n\nvar getDiagramElement = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.configureSvgSize)(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs\n"));

/***/ }),

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7U56Z5CX.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7U56Z5CX.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"stateDb_default\": function() { return /* binding */ stateDb_default; },\n/* harmony export */   \"stateDiagram_default\": function() { return /* binding */ stateDiagram_default; },\n/* harmony export */   \"stateRenderer_v3_unified_default\": function() { return /* binding */ stateRenderer_v3_unified_default; },\n/* harmony export */   \"styles_default\": function() { return /* binding */ styles_default; }\n/* harmony export */ });\n/* harmony import */ var _chunk_5HRBRIJM_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-5HRBRIJM.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs\");\n/* harmony import */ var _chunk_BO7VGL7K_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-BO7VGL7K.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-BO7VGL7K.mjs\");\n/* harmony import */ var _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-7DKRZKHE.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7DKRZKHE.mjs\");\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n\n\n\n\n\n// src/diagrams/state/parser/stateDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 16], $V7 = [1, 17], $V8 = [1, 18], $V9 = [1, 19], $Va = [1, 32], $Vb = [1, 20], $Vc = [1, 21], $Vd = [1, 22], $Ve = [1, 23], $Vf = [1, 24], $Vg = [1, 26], $Vh = [1, 27], $Vi = [1, 28], $Vj = [1, 29], $Vk = [1, 30], $Vl = [1, 31], $Vm = [1, 34], $Vn = [1, 35], $Vo = [1, 36], $Vp = [1, 37], $Vq = [1, 33], $Vr = [1, 4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $Vs = [1, 4, 5, 14, 15, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $Vt = [4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NL\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"classDefStatement\": 10, \"styleStatement\": 11, \"cssClassStatement\": 12, \"idStatement\": 13, \"DESCR\": 14, \"-->\": 15, \"HIDE_EMPTY\": 16, \"scale\": 17, \"WIDTH\": 18, \"COMPOSIT_STATE\": 19, \"STRUCT_START\": 20, \"STRUCT_STOP\": 21, \"STATE_DESCR\": 22, \"AS\": 23, \"ID\": 24, \"FORK\": 25, \"JOIN\": 26, \"CHOICE\": 27, \"CONCURRENT\": 28, \"note\": 29, \"notePosition\": 30, \"NOTE_TEXT\": 31, \"direction\": 32, \"acc_title\": 33, \"acc_title_value\": 34, \"acc_descr\": 35, \"acc_descr_value\": 36, \"acc_descr_multiline_value\": 37, \"classDef\": 38, \"CLASSDEF_ID\": 39, \"CLASSDEF_STYLEOPTS\": 40, \"DEFAULT\": 41, \"style\": 42, \"STYLE_IDS\": 43, \"STYLEDEF_STYLEOPTS\": 44, \"class\": 45, \"CLASSENTITY_IDS\": 46, \"STYLECLASS\": 47, \"direction_tb\": 48, \"direction_bt\": 49, \"direction_rl\": 50, \"direction_lr\": 51, \"eol\": 52, \";\": 53, \"EDGE_STATE\": 54, \"STYLE_SEPARATOR\": 55, \"left_of\": 56, \"right_of\": 57, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NL\", 6: \"SD\", 14: \"DESCR\", 15: \"-->\", 16: \"HIDE_EMPTY\", 17: \"scale\", 18: \"WIDTH\", 19: \"COMPOSIT_STATE\", 20: \"STRUCT_START\", 21: \"STRUCT_STOP\", 22: \"STATE_DESCR\", 23: \"AS\", 24: \"ID\", 25: \"FORK\", 26: \"JOIN\", 27: \"CHOICE\", 28: \"CONCURRENT\", 29: \"note\", 31: \"NOTE_TEXT\", 33: \"acc_title\", 34: \"acc_title_value\", 35: \"acc_descr\", 36: \"acc_descr_value\", 37: \"acc_descr_multiline_value\", 38: \"classDef\", 39: \"CLASSDEF_ID\", 40: \"CLASSDEF_STYLEOPTS\", 41: \"DEFAULT\", 42: \"style\", 43: \"STYLE_IDS\", 44: \"STYLEDEF_STYLEOPTS\", 45: \"class\", 46: \"CLASSENTITY_IDS\", 47: \"STYLECLASS\", 48: \"direction_tb\", 49: \"direction_bt\", 50: \"direction_rl\", 51: \"direction_lr\", 53: \";\", 54: \"EDGE_STATE\", 55: \"STYLE_SEPARATOR\", 56: \"left_of\", 57: \"right_of\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 3], [9, 4], [9, 1], [9, 2], [9, 1], [9, 4], [9, 3], [9, 6], [9, 1], [9, 1], [9, 1], [9, 1], [9, 4], [9, 4], [9, 1], [9, 2], [9, 2], [9, 1], [10, 3], [10, 3], [11, 3], [12, 3], [32, 1], [32, 1], [32, 1], [32, 1], [52, 1], [52, 1], [13, 1], [13, 1], [13, 3], [13, 3], [30, 1], [30, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setRootDoc($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n          this.$ = [];\n          break;\n        case 5:\n          if ($$[$0] != \"nl\") {\n            $$[$0 - 1].push($$[$0]);\n            this.$ = $$[$0 - 1];\n          }\n          break;\n        case 6:\n        case 7:\n          this.$ = $$[$0];\n          break;\n        case 8:\n          this.$ = \"nl\";\n          break;\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 13:\n          const stateStmt = $$[$0 - 1];\n          stateStmt.description = yy.trimColon($$[$0]);\n          this.$ = stateStmt;\n          break;\n        case 14:\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 2], state2: $$[$0] };\n          break;\n        case 15:\n          const relDescription = yy.trimColon($$[$0]);\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 3], state2: $$[$0 - 1], description: relDescription };\n          break;\n        case 19:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: \"\", doc: $$[$0 - 1] };\n          break;\n        case 20:\n          var id = $$[$0];\n          var description = $$[$0 - 2].trim();\n          if ($$[$0].match(\":\")) {\n            var parts = $$[$0].split(\":\");\n            id = parts[0];\n            description = [description, parts[1]];\n          }\n          this.$ = { stmt: \"state\", id, type: \"default\", description };\n          break;\n        case 21:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: $$[$0 - 5], doc: $$[$0 - 1] };\n          break;\n        case 22:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"fork\" };\n          break;\n        case 23:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"join\" };\n          break;\n        case 24:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"choice\" };\n          break;\n        case 25:\n          this.$ = { stmt: \"state\", id: yy.getDividerId(), type: \"divider\" };\n          break;\n        case 26:\n          this.$ = { stmt: \"state\", id: $$[$0 - 1].trim(), note: { position: $$[$0 - 2].trim(), text: $$[$0].trim() } };\n          break;\n        case 29:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 30:\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 32:\n        case 33:\n          this.$ = { stmt: \"classDef\", id: $$[$0 - 1].trim(), classes: $$[$0].trim() };\n          break;\n        case 34:\n          this.$ = { stmt: \"style\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 35:\n          this.$ = { stmt: \"applyClass\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 36:\n          yy.setDirection(\"TB\");\n          this.$ = { stmt: \"dir\", value: \"TB\" };\n          break;\n        case 37:\n          yy.setDirection(\"BT\");\n          this.$ = { stmt: \"dir\", value: \"BT\" };\n          break;\n        case 38:\n          yy.setDirection(\"RL\");\n          this.$ = { stmt: \"dir\", value: \"RL\" };\n          break;\n        case 39:\n          yy.setDirection(\"LR\");\n          this.$ = { stmt: \"dir\", value: \"LR\" };\n          break;\n        case 42:\n        case 43:\n          this.$ = { stmt: \"state\", id: $$[$0].trim(), type: \"default\", description: \"\" };\n          break;\n        case 44:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n        case 45:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 16, 17, 19, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 5]), { 9: 38, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 7]), o($Vr, [2, 8]), o($Vr, [2, 9]), o($Vr, [2, 10]), o($Vr, [2, 11]), o($Vr, [2, 12], { 14: [1, 39], 15: [1, 40] }), o($Vr, [2, 16]), { 18: [1, 41] }, o($Vr, [2, 18], { 20: [1, 42] }), { 23: [1, 43] }, o($Vr, [2, 22]), o($Vr, [2, 23]), o($Vr, [2, 24]), o($Vr, [2, 25]), { 30: 44, 31: [1, 45], 56: [1, 46], 57: [1, 47] }, o($Vr, [2, 28]), { 34: [1, 48] }, { 36: [1, 49] }, o($Vr, [2, 31]), { 39: [1, 50], 41: [1, 51] }, { 43: [1, 52] }, { 46: [1, 53] }, o($Vs, [2, 42], { 55: [1, 54] }), o($Vs, [2, 43], { 55: [1, 55] }), o($Vr, [2, 36]), o($Vr, [2, 37]), o($Vr, [2, 38]), o($Vr, [2, 39]), o($Vr, [2, 6]), o($Vr, [2, 13]), { 13: 56, 24: $Va, 54: $Vq }, o($Vr, [2, 17]), o($Vt, $V3, { 7: 57 }), { 24: [1, 58] }, { 24: [1, 59] }, { 23: [1, 60] }, { 24: [2, 46] }, { 24: [2, 47] }, o($Vr, [2, 29]), o($Vr, [2, 30]), { 40: [1, 61] }, { 40: [1, 62] }, { 44: [1, 63] }, { 47: [1, 64] }, { 24: [1, 65] }, { 24: [1, 66] }, o($Vr, [2, 14], { 14: [1, 67] }), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 68], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 20], { 20: [1, 69] }), { 31: [1, 70] }, { 24: [1, 71] }, o($Vr, [2, 32]), o($Vr, [2, 33]), o($Vr, [2, 34]), o($Vr, [2, 35]), o($Vs, [2, 44]), o($Vs, [2, 45]), o($Vr, [2, 15]), o($Vr, [2, 19]), o($Vt, $V3, { 7: 72 }), o($Vr, [2, 26]), o($Vr, [2, 27]), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 73], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 21])],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 46: [2, 46], 47: [2, 47] },\n    parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 41;\n            break;\n          case 1:\n            return 48;\n            break;\n          case 2:\n            return 49;\n            break;\n          case 3:\n            return 50;\n            break;\n          case 4:\n            return 51;\n            break;\n          case 5:\n            break;\n          case 6:\n            {\n            }\n            break;\n          case 7:\n            return 5;\n            break;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            break;\n          case 12:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 13:\n            return 18;\n            break;\n          case 14:\n            this.popState();\n            break;\n          case 15:\n            this.begin(\"acc_title\");\n            return 33;\n            break;\n          case 16:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 17:\n            this.begin(\"acc_descr\");\n            return 35;\n            break;\n          case 18:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 19:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 20:\n            this.popState();\n            break;\n          case 21:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 22:\n            this.pushState(\"CLASSDEF\");\n            return 38;\n            break;\n          case 23:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return \"DEFAULT_CLASSDEF_ID\";\n            break;\n          case 24:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return 39;\n            break;\n          case 25:\n            this.popState();\n            return 40;\n            break;\n          case 26:\n            this.pushState(\"CLASS\");\n            return 45;\n            break;\n          case 27:\n            this.popState();\n            this.pushState(\"CLASS_STYLE\");\n            return 46;\n            break;\n          case 28:\n            this.popState();\n            return 47;\n            break;\n          case 29:\n            this.pushState(\"STYLE\");\n            return 42;\n            break;\n          case 30:\n            this.popState();\n            this.pushState(\"STYLEDEF_STYLES\");\n            return 43;\n            break;\n          case 31:\n            this.popState();\n            return 44;\n            break;\n          case 32:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 33:\n            return 18;\n            break;\n          case 34:\n            this.popState();\n            break;\n          case 35:\n            this.pushState(\"STATE\");\n            break;\n          case 36:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 37:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 38:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 39:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 40:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 41:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 42:\n            return 48;\n            break;\n          case 43:\n            return 49;\n            break;\n          case 44:\n            return 50;\n            break;\n          case 45:\n            return 51;\n            break;\n          case 46:\n            this.pushState(\"STATE_STRING\");\n            break;\n          case 47:\n            this.pushState(\"STATE_ID\");\n            return \"AS\";\n            break;\n          case 48:\n            this.popState();\n            return \"ID\";\n            break;\n          case 49:\n            this.popState();\n            break;\n          case 50:\n            return \"STATE_DESCR\";\n            break;\n          case 51:\n            return 19;\n            break;\n          case 52:\n            this.popState();\n            break;\n          case 53:\n            this.popState();\n            this.pushState(\"struct\");\n            return 20;\n            break;\n          case 54:\n            break;\n          case 55:\n            this.popState();\n            return 21;\n            break;\n          case 56:\n            break;\n          case 57:\n            this.begin(\"NOTE\");\n            return 29;\n            break;\n          case 58:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 56;\n            break;\n          case 59:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 57;\n            break;\n          case 60:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE\");\n            break;\n          case 61:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE_ID\");\n            return \"AS\";\n            break;\n          case 62:\n            break;\n          case 63:\n            return \"NOTE_TEXT\";\n            break;\n          case 64:\n            this.popState();\n            return \"ID\";\n            break;\n          case 65:\n            this.popState();\n            this.pushState(\"NOTE_TEXT\");\n            return 24;\n            break;\n          case 66:\n            this.popState();\n            yy_.yytext = yy_.yytext.substr(2).trim();\n            return 31;\n            break;\n          case 67:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 31;\n            break;\n          case 68:\n            return 6;\n            break;\n          case 69:\n            return 6;\n            break;\n          case 70:\n            return 16;\n            break;\n          case 71:\n            return 54;\n            break;\n          case 72:\n            return 24;\n            break;\n          case 73:\n            yy_.yytext = yy_.yytext.trim();\n            return 14;\n            break;\n          case 74:\n            return 15;\n            break;\n          case 75:\n            return 28;\n            break;\n          case 76:\n            return 55;\n            break;\n          case 77:\n            return 5;\n            break;\n          case 78:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:default\\b)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:[\\s]+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:classDef\\s+)/i, /^(?:DEFAULT\\s+)/i, /^(?:\\w+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:class\\s+)/i, /^(?:(\\w+)+((,\\s*\\w+)*))/i, /^(?:[^\\n]*)/i, /^(?:style\\s+)/i, /^(?:[\\w,]+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:state\\s+)/i, /^(?:.*<<fork>>)/i, /^(?:.*<<join>>)/i, /^(?:.*<<choice>>)/i, /^(?:.*\\[\\[fork\\]\\])/i, /^(?:.*\\[\\[join\\]\\])/i, /^(?:.*\\[\\[choice\\]\\])/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:[\"])/i, /^(?:\\s*as\\s+)/i, /^(?:[^\\n\\{]*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n\\s\\{]+)/i, /^(?:\\n)/i, /^(?:\\{)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:\\})/i, /^(?:[\\n])/i, /^(?:note\\s+)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:\")/i, /^(?:\\s*as\\s*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n]*)/i, /^(?:\\s*[^:\\n\\s\\-]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:[\\s\\S]*?end note\\b)/i, /^(?:stateDiagram\\s+)/i, /^(?:stateDiagram-v2\\s+)/i, /^(?:hide empty description\\b)/i, /^(?:\\[\\*\\])/i, /^(?:[^:\\n\\s\\-\\{]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:-->)/i, /^(?:--)/i, /^(?::::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"LINE\": { \"rules\": [9, 10], \"inclusive\": false }, \"struct\": { \"rules\": [9, 10, 22, 26, 29, 35, 42, 43, 44, 45, 54, 55, 56, 57, 71, 72, 73, 74, 75], \"inclusive\": false }, \"FLOATING_NOTE_ID\": { \"rules\": [64], \"inclusive\": false }, \"FLOATING_NOTE\": { \"rules\": [61, 62, 63], \"inclusive\": false }, \"NOTE_TEXT\": { \"rules\": [66, 67], \"inclusive\": false }, \"NOTE_ID\": { \"rules\": [65], \"inclusive\": false }, \"NOTE\": { \"rules\": [58, 59, 60], \"inclusive\": false }, \"STYLEDEF_STYLEOPTS\": { \"rules\": [], \"inclusive\": false }, \"STYLEDEF_STYLES\": { \"rules\": [31], \"inclusive\": false }, \"STYLE_IDS\": { \"rules\": [], \"inclusive\": false }, \"STYLE\": { \"rules\": [30], \"inclusive\": false }, \"CLASS_STYLE\": { \"rules\": [28], \"inclusive\": false }, \"CLASS\": { \"rules\": [27], \"inclusive\": false }, \"CLASSDEFID\": { \"rules\": [25], \"inclusive\": false }, \"CLASSDEF\": { \"rules\": [23, 24], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [20, 21], \"inclusive\": false }, \"acc_descr\": { \"rules\": [18], \"inclusive\": false }, \"acc_title\": { \"rules\": [16], \"inclusive\": false }, \"SCALE\": { \"rules\": [13, 14, 33, 34], \"inclusive\": false }, \"ALIAS\": { \"rules\": [], \"inclusive\": false }, \"STATE_ID\": { \"rules\": [48], \"inclusive\": false }, \"STATE_STRING\": { \"rules\": [49, 50], \"inclusive\": false }, \"FORK_STATE\": { \"rules\": [], \"inclusive\": false }, \"STATE\": { \"rules\": [9, 10, 36, 37, 38, 39, 40, 41, 46, 47, 51, 52, 53], \"inclusive\": false }, \"ID\": { \"rules\": [9, 10], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 15, 17, 19, 22, 26, 29, 32, 35, 53, 57, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar stateDiagram_default = parser;\n\n// src/diagrams/state/stateCommon.ts\nvar DEFAULT_DIAGRAM_DIRECTION = \"LR\";\nvar DEFAULT_NESTED_DOC_DIR = \"TB\";\nvar STMT_STATE = \"state\";\nvar STMT_RELATION = \"relation\";\nvar STMT_CLASSDEF = \"classDef\";\nvar STMT_STYLEDEF = \"style\";\nvar STMT_APPLYCLASS = \"applyClass\";\nvar DEFAULT_STATE_TYPE = \"default\";\nvar DIVIDER_TYPE = \"divider\";\nvar G_EDGE_STYLE = \"fill:none\";\nvar G_EDGE_ARROWHEADSTYLE = \"fill: #333\";\nvar G_EDGE_LABELPOS = \"c\";\nvar G_EDGE_LABELTYPE = \"text\";\nvar G_EDGE_THICKNESS = \"normal\";\nvar SHAPE_STATE = \"rect\";\nvar SHAPE_STATE_WITH_DESC = \"rectWithTitle\";\nvar SHAPE_START = \"stateStart\";\nvar SHAPE_END = \"stateEnd\";\nvar SHAPE_DIVIDER = \"divider\";\nvar SHAPE_GROUP = \"roundedWithTitle\";\nvar SHAPE_NOTE = \"note\";\nvar SHAPE_NOTEGROUP = \"noteGroup\";\nvar CSS_DIAGRAM = \"statediagram\";\nvar CSS_STATE = \"state\";\nvar CSS_DIAGRAM_STATE = `${CSS_DIAGRAM}-${CSS_STATE}`;\nvar CSS_EDGE = \"transition\";\nvar CSS_NOTE = \"note\";\nvar CSS_NOTE_EDGE = \"note-edge\";\nvar CSS_EDGE_NOTE_EDGE = `${CSS_EDGE} ${CSS_NOTE_EDGE}`;\nvar CSS_DIAGRAM_NOTE = `${CSS_DIAGRAM}-${CSS_NOTE}`;\nvar CSS_CLUSTER = \"cluster\";\nvar CSS_DIAGRAM_CLUSTER = `${CSS_DIAGRAM}-${CSS_CLUSTER}`;\nvar CSS_CLUSTER_ALT = \"cluster-alt\";\nvar CSS_DIAGRAM_CLUSTER_ALT = `${CSS_DIAGRAM}-${CSS_CLUSTER_ALT}`;\nvar PARENT = \"parent\";\nvar NOTE = \"note\";\nvar DOMID_STATE = \"state\";\nvar DOMID_TYPE_SPACER = \"----\";\nvar NOTE_ID = `${DOMID_TYPE_SPACER}${NOTE}`;\nvar PARENT_ID = `${DOMID_TYPE_SPACER}${PARENT}`;\n\n// src/diagrams/state/stateRenderer-v3-unified.ts\nvar getDir = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n  let dir = defaultDir;\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === \"dir\") {\n      dir = parsedItemDoc.value;\n    }\n  }\n  return dir;\n}, \"getDir\");\nvar getClasses = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(text, diagramObj) {\n  diagramObj.db.extract(diagramObj.db.getRootDocV2());\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(text, id, _version, diag) {\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"REF0:\");\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Drawing state diagram (v2)\", id);\n  const { securityLevel, state: conf, layout } = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)();\n  diag.db.extract(diag.db.getRootDocV2());\n  const data4Layout = diag.db.getData();\n  const svg = (0,_chunk_5HRBRIJM_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramElement)(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = layout;\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = [\"barb\"];\n  data4Layout.diagramId = id;\n  await (0,_chunk_BO7VGL7K_mjs__WEBPACK_IMPORTED_MODULE_1__.render)(data4Layout, svg);\n  const padding = 8;\n  _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.insertTitle(\n    svg,\n    \"statediagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  (0,_chunk_5HRBRIJM_mjs__WEBPACK_IMPORTED_MODULE_0__.setupViewPortForSVG)(svg, padding, CSS_DIAGRAM, conf?.useMaxWidth ?? true);\n}, \"draw\");\nvar stateRenderer_v3_unified_default = {\n  getClasses,\n  draw,\n  getDir\n};\n\n// src/diagrams/state/dataFetcher.js\nvar nodeDb = /* @__PURE__ */ new Map();\nvar graphItemCount = 0;\nfunction stateDomId(itemId = \"\", counter = 0, type = \"\", typeSpacer = DOMID_TYPE_SPACER) {\n  const typeStr = type !== null && type.length > 0 ? `${typeSpacer}${type}` : \"\";\n  return `${DOMID_STATE}-${itemId}${typeStr}-${counter}`;\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(stateDomId, \"stateDomId\");\nvar setupDoc = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parentParsedItem, doc, diagramStates, nodes2, edges2, altFlag, look, classes2) => {\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.trace(\"items\", doc);\n  doc.forEach((item) => {\n    switch (item.stmt) {\n      case STMT_STATE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes2, edges2, altFlag, look, classes2);\n        break;\n      case DEFAULT_STATE_TYPE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes2, edges2, altFlag, look, classes2);\n        break;\n      case STMT_RELATION:\n        {\n          dataFetcher(\n            parentParsedItem,\n            item.state1,\n            diagramStates,\n            nodes2,\n            edges2,\n            altFlag,\n            look,\n            classes2\n          );\n          dataFetcher(\n            parentParsedItem,\n            item.state2,\n            diagramStates,\n            nodes2,\n            edges2,\n            altFlag,\n            look,\n            classes2\n          );\n          const edgeData = {\n            id: \"edge\" + graphItemCount,\n            start: item.state1.id,\n            end: item.state2.id,\n            arrowhead: \"normal\",\n            arrowTypeEnd: \"arrow_barb\",\n            style: G_EDGE_STYLE,\n            labelStyle: \"\",\n            label: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(item.description, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()),\n            arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n            labelpos: G_EDGE_LABELPOS,\n            labelType: G_EDGE_LABELTYPE,\n            thickness: G_EDGE_THICKNESS,\n            classes: CSS_EDGE,\n            look\n          };\n          edges2.push(edgeData);\n          graphItemCount++;\n        }\n        break;\n    }\n  });\n}, \"setupDoc\");\nvar getDir2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  let dir = defaultDir;\n  if (parsedItem.doc) {\n    for (const parsedItemDoc of parsedItem.doc) {\n      if (parsedItemDoc.stmt === \"dir\") {\n        dir = parsedItemDoc.value;\n      }\n    }\n  }\n  return dir;\n}, \"getDir\");\nfunction insertOrUpdateNode(nodes2, nodeData, classes2) {\n  if (!nodeData.id || nodeData.id === \"</join></fork>\" || nodeData.id === \"</choice>\") {\n    return;\n  }\n  if (nodeData.cssClasses) {\n    if (!Array.isArray(nodeData.cssCompiledStyles)) {\n      nodeData.cssCompiledStyles = [];\n    }\n    nodeData.cssClasses.split(\" \").forEach((cssClass) => {\n      if (classes2.get(cssClass)) {\n        const classDef = classes2.get(cssClass);\n        nodeData.cssCompiledStyles = [...nodeData.cssCompiledStyles, ...classDef.styles];\n      }\n    });\n  }\n  const existingNodeData = nodes2.find((node) => node.id === nodeData.id);\n  if (existingNodeData) {\n    Object.assign(existingNodeData, nodeData);\n  } else {\n    nodes2.push(nodeData);\n  }\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(insertOrUpdateNode, \"insertOrUpdateNode\");\nfunction getClassesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.classes?.join(\" \") ?? \"\";\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getClassesFromDbInfo, \"getClassesFromDbInfo\");\nfunction getStylesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.styles ?? [];\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getStylesFromDbInfo, \"getStylesFromDbInfo\");\nvar dataFetcher = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parent, parsedItem, diagramStates, nodes2, edges2, altFlag, look, classes2) => {\n  const itemId = parsedItem.id;\n  const dbState = diagramStates.get(itemId);\n  const classStr = getClassesFromDbInfo(dbState);\n  const style = getStylesFromDbInfo(dbState);\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"dataFetcher parsedItem\", parsedItem, dbState, style);\n  if (itemId !== \"root\") {\n    let shape = SHAPE_STATE;\n    if (parsedItem.start === true) {\n      shape = SHAPE_START;\n    } else if (parsedItem.start === false) {\n      shape = SHAPE_END;\n    }\n    if (parsedItem.type !== DEFAULT_STATE_TYPE) {\n      shape = parsedItem.type;\n    }\n    if (!nodeDb.get(itemId)) {\n      nodeDb.set(itemId, {\n        id: itemId,\n        shape,\n        description: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(itemId, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()),\n        cssClasses: `${classStr} ${CSS_DIAGRAM_STATE}`,\n        cssStyles: style\n      });\n    }\n    const newNode = nodeDb.get(itemId);\n    if (parsedItem.description) {\n      if (Array.isArray(newNode.description)) {\n        newNode.shape = SHAPE_STATE_WITH_DESC;\n        newNode.description.push(parsedItem.description);\n      } else {\n        if (newNode.description?.length > 0) {\n          newNode.shape = SHAPE_STATE_WITH_DESC;\n          if (newNode.description === itemId) {\n            newNode.description = [parsedItem.description];\n          } else {\n            newNode.description = [newNode.description, parsedItem.description];\n          }\n        } else {\n          newNode.shape = SHAPE_STATE;\n          newNode.description = parsedItem.description;\n        }\n      }\n      newNode.description = _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeTextOrArray(newNode.description, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)());\n    }\n    if (newNode.description?.length === 1 && newNode.shape === SHAPE_STATE_WITH_DESC) {\n      if (newNode.type === \"group\") {\n        newNode.shape = SHAPE_GROUP;\n      } else {\n        newNode.shape = SHAPE_STATE;\n      }\n    }\n    if (!newNode.type && parsedItem.doc) {\n      _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting cluster for XCX\", itemId, getDir2(parsedItem));\n      newNode.type = \"group\";\n      newNode.isGroup = true;\n      newNode.dir = getDir2(parsedItem);\n      newNode.shape = parsedItem.type === DIVIDER_TYPE ? SHAPE_DIVIDER : SHAPE_GROUP;\n      newNode.cssClasses = `${newNode.cssClasses} ${CSS_DIAGRAM_CLUSTER} ${altFlag ? CSS_DIAGRAM_CLUSTER_ALT : \"\"}`;\n    }\n    const nodeData = {\n      labelStyle: \"\",\n      shape: newNode.shape,\n      label: newNode.description,\n      cssClasses: newNode.cssClasses,\n      cssCompiledStyles: [],\n      cssStyles: newNode.cssStyles,\n      id: itemId,\n      dir: newNode.dir,\n      domId: stateDomId(itemId, graphItemCount),\n      type: newNode.type,\n      isGroup: newNode.type === \"group\",\n      padding: 8,\n      rx: 10,\n      ry: 10,\n      look\n    };\n    if (nodeData.shape === SHAPE_DIVIDER) {\n      nodeData.label = \"\";\n    }\n    if (parent && parent.id !== \"root\") {\n      _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.trace(\"Setting node \", itemId, \" to be child of its parent \", parent.id);\n      nodeData.parentId = parent.id;\n    }\n    nodeData.centerLabel = true;\n    if (parsedItem.note) {\n      const noteData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTE,\n        label: parsedItem.note.text,\n        cssClasses: CSS_DIAGRAM_NOTE,\n        // useHtmlLabels: false,\n        cssStyles: [],\n        cssCompilesStyles: [],\n        id: itemId + NOTE_ID + \"-\" + graphItemCount,\n        domId: stateDomId(itemId, graphItemCount, NOTE),\n        type: newNode.type,\n        isGroup: newNode.type === \"group\",\n        padding: (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)().flowchart.padding,\n        look,\n        position: parsedItem.note.position\n      };\n      const parentNodeId = itemId + PARENT_ID;\n      const groupData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTEGROUP,\n        label: parsedItem.note.text,\n        cssClasses: newNode.cssClasses,\n        cssStyles: [],\n        id: itemId + PARENT_ID,\n        domId: stateDomId(itemId, graphItemCount, PARENT),\n        type: \"group\",\n        isGroup: true,\n        padding: 16,\n        //getConfig().flowchart.padding\n        look,\n        position: parsedItem.note.position\n      };\n      graphItemCount++;\n      groupData.id = parentNodeId;\n      noteData.parentId = parentNodeId;\n      insertOrUpdateNode(nodes2, groupData, classes2);\n      insertOrUpdateNode(nodes2, noteData, classes2);\n      insertOrUpdateNode(nodes2, nodeData, classes2);\n      let from = itemId;\n      let to = noteData.id;\n      if (parsedItem.note.position === \"left of\") {\n        from = noteData.id;\n        to = itemId;\n      }\n      edges2.push({\n        id: from + \"-\" + to,\n        start: from,\n        end: to,\n        arrowhead: \"none\",\n        arrowTypeEnd: \"\",\n        style: G_EDGE_STYLE,\n        labelStyle: \"\",\n        classes: CSS_EDGE_NOTE_EDGE,\n        arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n        labelpos: G_EDGE_LABELPOS,\n        labelType: G_EDGE_LABELTYPE,\n        thickness: G_EDGE_THICKNESS,\n        look\n      });\n    } else {\n      insertOrUpdateNode(nodes2, nodeData, classes2);\n    }\n  }\n  if (parsedItem.doc) {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.trace(\"Adding nodes children \");\n    setupDoc(parsedItem, parsedItem.doc, diagramStates, nodes2, edges2, !altFlag, look, classes2);\n  }\n}, \"dataFetcher\");\nvar reset = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => {\n  nodeDb.clear();\n  graphItemCount = 0;\n}, \"reset\");\n\n// src/diagrams/state/stateDb.js\nvar START_NODE = \"[*]\";\nvar START_TYPE = \"start\";\nvar END_NODE = START_NODE;\nvar END_TYPE = \"end\";\nvar COLOR_KEYWORD = \"color\";\nvar FILL_KEYWORD = \"fill\";\nvar BG_FILL = \"bgFill\";\nvar STYLECLASS_SEP = \",\";\nfunction newClassesList() {\n  return /* @__PURE__ */ new Map();\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(newClassesList, \"newClassesList\");\nvar nodes = [];\nvar edges = [];\nvar direction = DEFAULT_DIAGRAM_DIRECTION;\nvar rootDoc = [];\nvar classes = newClassesList();\nvar newDoc = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => {\n  return {\n    /** @type {{ id1: string, id2: string, relationTitle: string }[]} */\n    relations: [],\n    states: /* @__PURE__ */ new Map(),\n    documents: {}\n  };\n}, \"newDoc\");\nvar documents = {\n  root: newDoc()\n};\nvar currentDocument = documents.root;\nvar startEndCount = 0;\nvar dividerCnt = 0;\nvar lineType = {\n  LINE: 0,\n  DOTTED_LINE: 1\n};\nvar relationType = {\n  AGGREGATION: 0,\n  EXTENSION: 1,\n  COMPOSITION: 2,\n  DEPENDENCY: 3\n};\nvar clone = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((o) => JSON.parse(JSON.stringify(o)), \"clone\");\nvar setRootDoc = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((o) => {\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting root doc\", o);\n  rootDoc = o;\n}, \"setRootDoc\");\nvar getRootDoc = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => rootDoc, \"getRootDoc\");\nvar docTranslator = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parent, node, first) => {\n  if (node.stmt === STMT_RELATION) {\n    docTranslator(parent, node.state1, true);\n    docTranslator(parent, node.state2, false);\n  } else {\n    if (node.stmt === STMT_STATE) {\n      if (node.id === \"[*]\") {\n        node.id = first ? parent.id + \"_start\" : parent.id + \"_end\";\n        node.start = first;\n      } else {\n        node.id = node.id.trim();\n      }\n    }\n    if (node.doc) {\n      const doc = [];\n      let currentDoc = [];\n      let i;\n      for (i = 0; i < node.doc.length; i++) {\n        if (node.doc[i].type === DIVIDER_TYPE) {\n          const newNode = clone(node.doc[i]);\n          newNode.doc = clone(currentDoc);\n          doc.push(newNode);\n          currentDoc = [];\n        } else {\n          currentDoc.push(node.doc[i]);\n        }\n      }\n      if (doc.length > 0 && currentDoc.length > 0) {\n        const newNode = {\n          stmt: STMT_STATE,\n          id: (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_2__.generateId)(),\n          type: \"divider\",\n          doc: clone(currentDoc)\n        };\n        doc.push(clone(newNode));\n        node.doc = doc;\n      }\n      node.doc.forEach((docNode) => docTranslator(node, docNode, true));\n    }\n  }\n}, \"docTranslator\");\nvar getRootDocV2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => {\n  docTranslator({ id: \"root\" }, { id: \"root\", doc: rootDoc }, true);\n  return { id: \"root\", doc: rootDoc };\n}, \"getRootDocV2\");\nvar extract = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((_doc) => {\n  let doc;\n  if (_doc.doc) {\n    doc = _doc.doc;\n  } else {\n    doc = _doc;\n  }\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(doc);\n  clear2(true);\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Extract initial document:\", doc);\n  doc.forEach((item) => {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.warn(\"Statement\", item.stmt);\n    switch (item.stmt) {\n      case STMT_STATE:\n        addState(\n          item.id.trim(),\n          item.type,\n          item.doc,\n          item.description,\n          item.note,\n          item.classes,\n          item.styles,\n          item.textStyles\n        );\n        break;\n      case STMT_RELATION:\n        addRelation(item.state1, item.state2, item.description);\n        break;\n      case STMT_CLASSDEF:\n        addStyleClass(item.id.trim(), item.classes);\n        break;\n      case STMT_STYLEDEF:\n        {\n          const ids = item.id.trim().split(\",\");\n          const styles = item.styleClass.split(\",\");\n          ids.forEach((id) => {\n            let foundState = getState(id);\n            if (foundState === void 0) {\n              const trimmedId = id.trim();\n              addState(trimmedId);\n              foundState = getState(trimmedId);\n            }\n            foundState.styles = styles.map((s) => s.replace(/;/g, \"\")?.trim());\n          });\n        }\n        break;\n      case STMT_APPLYCLASS:\n        setCssClass(item.id.trim(), item.styleClass);\n        break;\n    }\n  });\n  const diagramStates = getStates();\n  const config = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)();\n  const look = config.look;\n  reset();\n  dataFetcher(void 0, getRootDocV2(), diagramStates, nodes, edges, true, look, classes);\n  nodes.forEach((node) => {\n    if (Array.isArray(node.label)) {\n      node.description = node.label.slice(1);\n      if (node.isGroup && node.description.length > 0) {\n        throw new Error(\n          \"Group nodes can only have label. Remove the additional description for node [\" + node.id + \"]\"\n        );\n      }\n      node.label = node.label[0];\n    }\n  });\n}, \"extract\");\nvar addState = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(id, type = DEFAULT_STATE_TYPE, doc = null, descr = null, note = null, classes2 = null, styles = null, textStyles = null) {\n  const trimmedId = id?.trim();\n  if (!currentDocument.states.has(trimmedId)) {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Adding state \", trimmedId, descr);\n    currentDocument.states.set(trimmedId, {\n      id: trimmedId,\n      descriptions: [],\n      type,\n      doc,\n      note,\n      classes: [],\n      styles: [],\n      textStyles: []\n    });\n  } else {\n    if (!currentDocument.states.get(trimmedId).doc) {\n      currentDocument.states.get(trimmedId).doc = doc;\n    }\n    if (!currentDocument.states.get(trimmedId).type) {\n      currentDocument.states.get(trimmedId).type = type;\n    }\n  }\n  if (descr) {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting state description\", trimmedId, descr);\n    if (typeof descr === \"string\") {\n      addDescription(trimmedId, descr.trim());\n    }\n    if (typeof descr === \"object\") {\n      descr.forEach((des) => addDescription(trimmedId, des.trim()));\n    }\n  }\n  if (note) {\n    const doc2 = currentDocument.states.get(trimmedId);\n    doc2.note = note;\n    doc2.note.text = _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(doc2.note.text, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)());\n  }\n  if (classes2) {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting state classes\", trimmedId, classes2);\n    const classesList = typeof classes2 === \"string\" ? [classes2] : classes2;\n    classesList.forEach((cssClass) => setCssClass(trimmedId, cssClass.trim()));\n  }\n  if (styles) {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting state styles\", trimmedId, styles);\n    const stylesList = typeof styles === \"string\" ? [styles] : styles;\n    stylesList.forEach((style) => setStyle(trimmedId, style.trim()));\n  }\n  if (textStyles) {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting state styles\", trimmedId, styles);\n    const textStylesList = typeof textStyles === \"string\" ? [textStyles] : textStyles;\n    textStylesList.forEach((textStyle) => setTextStyle(trimmedId, textStyle.trim()));\n  }\n}, \"addState\");\nvar clear2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(saveCommon) {\n  nodes = [];\n  edges = [];\n  documents = {\n    root: newDoc()\n  };\n  currentDocument = documents.root;\n  startEndCount = 0;\n  classes = newClassesList();\n  if (!saveCommon) {\n    (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.clear)();\n  }\n}, \"clear\");\nvar getState = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(id) {\n  return currentDocument.states.get(id);\n}, \"getState\");\nvar getStates = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n  return currentDocument.states;\n}, \"getStates\");\nvar logDocuments = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Documents = \", documents);\n}, \"logDocuments\");\nvar getRelations = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n  return currentDocument.relations;\n}, \"getRelations\");\nfunction startIdIfNeeded(id = \"\") {\n  let fixedId = id;\n  if (id === START_NODE) {\n    startEndCount++;\n    fixedId = `${START_TYPE}${startEndCount}`;\n  }\n  return fixedId;\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(startIdIfNeeded, \"startIdIfNeeded\");\nfunction startTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n  return id === START_NODE ? START_TYPE : type;\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(startTypeIfNeeded, \"startTypeIfNeeded\");\nfunction endIdIfNeeded(id = \"\") {\n  let fixedId = id;\n  if (id === END_NODE) {\n    startEndCount++;\n    fixedId = `${END_TYPE}${startEndCount}`;\n  }\n  return fixedId;\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(endIdIfNeeded, \"endIdIfNeeded\");\nfunction endTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n  return id === END_NODE ? END_TYPE : type;\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(endTypeIfNeeded, \"endTypeIfNeeded\");\nfunction addRelationObjs(item1, item2, relationTitle) {\n  let id1 = startIdIfNeeded(item1.id.trim());\n  let type1 = startTypeIfNeeded(item1.id.trim(), item1.type);\n  let id2 = startIdIfNeeded(item2.id.trim());\n  let type2 = startTypeIfNeeded(item2.id.trim(), item2.type);\n  addState(\n    id1,\n    type1,\n    item1.doc,\n    item1.description,\n    item1.note,\n    item1.classes,\n    item1.styles,\n    item1.textStyles\n  );\n  addState(\n    id2,\n    type2,\n    item2.doc,\n    item2.description,\n    item2.note,\n    item2.classes,\n    item2.styles,\n    item2.textStyles\n  );\n  currentDocument.relations.push({\n    id1,\n    id2,\n    relationTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(relationTitle, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)())\n  });\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(addRelationObjs, \"addRelationObjs\");\nvar addRelation = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(item1, item2, title) {\n  if (typeof item1 === \"object\") {\n    addRelationObjs(item1, item2, title);\n  } else {\n    const id1 = startIdIfNeeded(item1.trim());\n    const type1 = startTypeIfNeeded(item1);\n    const id2 = endIdIfNeeded(item2.trim());\n    const type2 = endTypeIfNeeded(item2);\n    addState(id1, type1);\n    addState(id2, type2);\n    currentDocument.relations.push({\n      id1,\n      id2,\n      title: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(title, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)())\n    });\n  }\n}, \"addRelation\");\nvar addDescription = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(id, descr) {\n  const theState = currentDocument.states.get(id);\n  const _descr = descr.startsWith(\":\") ? descr.replace(\":\", \"\").trim() : descr;\n  theState.descriptions.push(_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(_descr, (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()));\n}, \"addDescription\");\nvar cleanupLabel = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(label) {\n  if (label.substring(0, 1) === \":\") {\n    return label.substr(2).trim();\n  } else {\n    return label.trim();\n  }\n}, \"cleanupLabel\");\nvar getDividerId = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => {\n  dividerCnt++;\n  return \"divider-id-\" + dividerCnt;\n}, \"getDividerId\");\nvar addStyleClass = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(id, styleAttributes = \"\") {\n  if (!classes.has(id)) {\n    classes.set(id, { id, styles: [], textStyles: [] });\n  }\n  const foundClass = classes.get(id);\n  if (styleAttributes !== void 0 && styleAttributes !== null) {\n    styleAttributes.split(STYLECLASS_SEP).forEach((attrib) => {\n      const fixedAttrib = attrib.replace(/([^;]*);/, \"$1\").trim();\n      if (RegExp(COLOR_KEYWORD).exec(attrib)) {\n        const newStyle1 = fixedAttrib.replace(FILL_KEYWORD, BG_FILL);\n        const newStyle2 = newStyle1.replace(COLOR_KEYWORD, FILL_KEYWORD);\n        foundClass.textStyles.push(newStyle2);\n      }\n      foundClass.styles.push(fixedAttrib);\n    });\n  }\n}, \"addStyleClass\");\nvar getClasses2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n  return classes;\n}, \"getClasses\");\nvar setCssClass = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(itemIds, cssClassName) {\n  itemIds.split(\",\").forEach(function(id) {\n    let foundState = getState(id);\n    if (foundState === void 0) {\n      const trimmedId = id.trim();\n      addState(trimmedId);\n      foundState = getState(trimmedId);\n    }\n    foundState.classes.push(cssClassName);\n  });\n}, \"setCssClass\");\nvar setStyle = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(itemId, styleText) {\n  const item = getState(itemId);\n  if (item !== void 0) {\n    item.styles.push(styleText);\n  }\n}, \"setStyle\");\nvar setTextStyle = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(itemId, cssClassName) {\n  const item = getState(itemId);\n  if (item !== void 0) {\n    item.textStyles.push(cssClassName);\n  }\n}, \"setTextStyle\");\nvar getDirection = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => direction, \"getDirection\");\nvar setDirection = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((dir) => {\n  direction = dir;\n}, \"setDirection\");\nvar trimColon = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((str) => str && str[0] === \":\" ? str.substr(1).trim() : str.trim(), \"trimColon\");\nvar getData = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => {\n  const config = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)();\n  return { nodes, edges, other: {}, config, direction: getDir(getRootDocV2()) };\n}, \"getData\");\nvar stateDb_default = {\n  getConfig: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)().state, \"getConfig\"),\n  getData,\n  addState,\n  clear: clear2,\n  getState,\n  getStates,\n  getRelations,\n  getClasses: getClasses2,\n  getDirection,\n  addRelation,\n  getDividerId,\n  setDirection,\n  cleanupLabel,\n  lineType,\n  relationType,\n  logDocuments,\n  getRootDoc,\n  setRootDoc,\n  getRootDocV2,\n  extract,\n  trimColon,\n  getAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccTitle,\n  setAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccTitle,\n  getAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccDescription,\n  setAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccDescription,\n  addStyleClass,\n  setCssClass,\n  addDescription,\n  setDiagramTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.setDiagramTitle,\n  getDiagramTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.getDiagramTitle\n};\n\n// src/diagrams/state/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((options) => `\ndefs #statediagram-barbEnd {\n    fill: ${options.transitionColor};\n    stroke: ${options.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${options.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${options.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${options.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${options.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${options.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${options.noteBorderColor};\n  fill: ${options.noteBkgColor};\n\n  text {\n    fill: ${options.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${options.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${options.edgeLabelBackground};\n  p {\n    background-color: ${options.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${options.edgeLabelBackground};\n    fill: ${options.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${options.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${options.innerEndBackground};\n  stroke: ${options.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${options.compositeBackground || options.background};\n  // stroke: ${options.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${options.stateBkg || options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${options.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${options.compositeTitleBackground};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${options.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${options.stateBorder || options.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${options.compositeBackground || options.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${options.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${options.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${options.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${options.lineColor};\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7U56Z5CX.mjs\n"));

/***/ }),

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-4JROLMXI.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-4JROLMXI.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"diagram\": function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_7U56Z5CX_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-7U56Z5CX.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7U56Z5CX.mjs\");\n/* harmony import */ var _chunk_5HRBRIJM_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-5HRBRIJM.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs\");\n/* harmony import */ var _chunk_BO7VGL7K_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-BO7VGL7K.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-BO7VGL7K.mjs\");\n/* harmony import */ var _chunk_66SQ7PYY_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-66SQ7PYY.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-66SQ7PYY.mjs\");\n/* harmony import */ var _chunk_7NZE2EM7_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-7NZE2EM7.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7NZE2EM7.mjs\");\n/* harmony import */ var _chunk_OPO4IU42_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-OPO4IU42.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-OPO4IU42.mjs\");\n/* harmony import */ var _chunk_3JNJP5BE_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-3JNJP5BE.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3JNJP5BE.mjs\");\n/* harmony import */ var _chunk_3X56UNUX_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-3X56UNUX.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3X56UNUX.mjs\");\n/* harmony import */ var _chunk_6JOS74DS_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-6JOS74DS.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6JOS74DS.mjs\");\n/* harmony import */ var _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./chunk-7DKRZKHE.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7DKRZKHE.mjs\");\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n// src/diagrams/state/stateDiagram-v2.ts\nvar diagram = {\n  parser: _chunk_7U56Z5CX_mjs__WEBPACK_IMPORTED_MODULE_0__.stateDiagram_default,\n  db: _chunk_7U56Z5CX_mjs__WEBPACK_IMPORTED_MODULE_0__.stateDb_default,\n  renderer: _chunk_7U56Z5CX_mjs__WEBPACK_IMPORTED_MODULE_0__.stateRenderer_v3_unified_default,\n  styles: _chunk_7U56Z5CX_mjs__WEBPACK_IMPORTED_MODULE_0__.styles_default,\n  init: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n    _chunk_7U56Z5CX_mjs__WEBPACK_IMPORTED_MODULE_0__.stateDb_default.clear();\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbWVybWFpZC9kaXN0L2NodW5rcy9tZXJtYWlkLmNvcmUvc3RhdGVEaWFncmFtLXYyLTRKUk9MTVhJLm1qcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFLOEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHQTs7QUFFOUI7QUFDQTtBQUNBLFVBQVUscUVBQW9CO0FBQzlCLE1BQU0sZ0VBQWU7QUFDckIsWUFBWSxpRkFBZ0M7QUFDNUMsVUFBVSwrREFBYztBQUN4Qix3QkFBd0IsNERBQU07QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLHNFQUFxQjtBQUN6QixHQUFHO0FBQ0g7QUFHRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWVybWFpZC9kaXN0L2NodW5rcy9tZXJtYWlkLmNvcmUvc3RhdGVEaWFncmFtLXYyLTRKUk9MTVhJLm1qcz8zZWJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIHN0YXRlRGJfZGVmYXVsdCxcbiAgc3RhdGVEaWFncmFtX2RlZmF1bHQsXG4gIHN0YXRlUmVuZGVyZXJfdjNfdW5pZmllZF9kZWZhdWx0LFxuICBzdHlsZXNfZGVmYXVsdFxufSBmcm9tIFwiLi9jaHVuay03VTU2WjVDWC5tanNcIjtcbmltcG9ydCBcIi4vY2h1bmstNUhSQlJJSk0ubWpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLUJPN1ZHTDdLLm1qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay02NlNRN1BZWS5tanNcIjtcbmltcG9ydCBcIi4vY2h1bmstN05aRTJFTTcubWpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLU9QTzRJVTQyLm1qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay0zSk5KUDVCRS5tanNcIjtcbmltcG9ydCBcIi4vY2h1bmstM1g1NlVOVVgubWpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLTZKT1M3NERTLm1qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay03REtSWktIRS5tanNcIjtcbmltcG9ydCB7XG4gIF9fbmFtZVxufSBmcm9tIFwiLi9jaHVuay02REJGRkhJUC5tanNcIjtcblxuLy8gc3JjL2RpYWdyYW1zL3N0YXRlL3N0YXRlRGlhZ3JhbS12Mi50c1xudmFyIGRpYWdyYW0gPSB7XG4gIHBhcnNlcjogc3RhdGVEaWFncmFtX2RlZmF1bHQsXG4gIGRiOiBzdGF0ZURiX2RlZmF1bHQsXG4gIHJlbmRlcmVyOiBzdGF0ZVJlbmRlcmVyX3YzX3VuaWZpZWRfZGVmYXVsdCxcbiAgc3R5bGVzOiBzdHlsZXNfZGVmYXVsdCxcbiAgaW5pdDogLyogQF9fUFVSRV9fICovIF9fbmFtZSgoY25mKSA9PiB7XG4gICAgaWYgKCFjbmYuc3RhdGUpIHtcbiAgICAgIGNuZi5zdGF0ZSA9IHt9O1xuICAgIH1cbiAgICBjbmYuc3RhdGUuYXJyb3dNYXJrZXJBYnNvbHV0ZSA9IGNuZi5hcnJvd01hcmtlckFic29sdXRlO1xuICAgIHN0YXRlRGJfZGVmYXVsdC5jbGVhcigpO1xuICB9LCBcImluaXRcIilcbn07XG5leHBvcnQge1xuICBkaWFncmFtXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-4JROLMXI.mjs\n"));

/***/ })

}]);