/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_mermaid_dist_chunks_mermaid_core_ganttDiagram-NTVNEXSI_mjs"],{

/***/ "./node_modules/dayjs/plugin/advancedFormat.js":
/*!*****************************************************!*\
  !*** ./node_modules/dayjs/plugin/advancedFormat.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";return function(e,t){var r=t.prototype,n=r.format;r.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return n.bind(this)(e);var s=this.$utils(),a=(e||\"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(e){switch(e){case\"Q\":return Math.ceil((t.$M+1)/3);case\"Do\":return r.ordinal(t.$D);case\"gggg\":return t.weekYear();case\"GGGG\":return t.isoWeekYear();case\"wo\":return r.ordinal(t.week(),\"W\");case\"w\":case\"ww\":return s.s(t.week(),\"w\"===e?1:2,\"0\");case\"W\":case\"WW\":return s.s(t.isoWeek(),\"W\"===e?1:2,\"0\");case\"k\":case\"kk\":return s.s(String(0===t.$H?24:t.$H),\"k\"===e?1:2,\"0\");case\"X\":return Math.floor(t.$d.getTime()/1e3);case\"x\":return t.$d.getTime();case\"z\":return\"[\"+t.offsetName()+\"]\";case\"zzz\":return\"[\"+t.offsetName(\"long\")+\"]\";default:return e}}));return n.bind(this)(a)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/plugin/advancedFormat.js\n"));

/***/ }),

/***/ "./node_modules/dayjs/plugin/customParseFormat.js":
/*!********************************************************!*\
  !*** ./node_modules/dayjs/plugin/customParseFormat.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";var e={LTS:\"h:mm:ss A\",LT:\"h:mm A\",L:\"MM/DD/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"},t=/(\\[[^[]*\\])|([-_:/.,()\\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\\d/,r=/\\d\\d/,i=/\\d\\d?/,o=/\\d*[^-_:/,()\\s\\d]+/,s={},a=function(e){return(e=+e)+(e>68?1900:2e3)};var f=function(e){return function(t){this[e]=+t}},h=[/[+-]\\d\\d:?(\\d\\d)?|Z/,function(e){(this.zone||(this.zone={})).offset=function(e){if(!e)return 0;if(\"Z\"===e)return 0;var t=e.match(/([+-]|\\d\\d)/g),n=60*t[1]+(+t[2]||0);return 0===n?0:\"+\"===t[0]?-n:n}(e)}],u=function(e){var t=s[e];return t&&(t.indexOf?t:t.s.concat(t.f))},d=function(e,t){var n,r=s.meridiem;if(r){for(var i=1;i<=24;i+=1)if(e.indexOf(r(i,0,t))>-1){n=i>12;break}}else n=e===(t?\"pm\":\"PM\");return n},c={A:[o,function(e){this.afternoon=d(e,!1)}],a:[o,function(e){this.afternoon=d(e,!0)}],Q:[n,function(e){this.month=3*(e-1)+1}],S:[n,function(e){this.milliseconds=100*+e}],SS:[r,function(e){this.milliseconds=10*+e}],SSS:[/\\d{3}/,function(e){this.milliseconds=+e}],s:[i,f(\"seconds\")],ss:[i,f(\"seconds\")],m:[i,f(\"minutes\")],mm:[i,f(\"minutes\")],H:[i,f(\"hours\")],h:[i,f(\"hours\")],HH:[i,f(\"hours\")],hh:[i,f(\"hours\")],D:[i,f(\"day\")],DD:[r,f(\"day\")],Do:[o,function(e){var t=s.ordinal,n=e.match(/\\d+/);if(this.day=n[0],t)for(var r=1;r<=31;r+=1)t(r).replace(/\\[|\\]/g,\"\")===e&&(this.day=r)}],w:[i,f(\"week\")],ww:[r,f(\"week\")],M:[i,f(\"month\")],MM:[r,f(\"month\")],MMM:[o,function(e){var t=u(\"months\"),n=(u(\"monthsShort\")||t.map((function(e){return e.slice(0,3)}))).indexOf(e)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[o,function(e){var t=u(\"months\").indexOf(e)+1;if(t<1)throw new Error;this.month=t%12||t}],Y:[/[+-]?\\d+/,f(\"year\")],YY:[r,function(e){this.year=a(e)}],YYYY:[/\\d{4}/,f(\"year\")],Z:h,ZZ:h};function l(n){var r,i;r=n,i=s&&s.formats;for(var o=(n=r.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var o=r&&r.toUpperCase();return n||i[r]||e[r]||i[o].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}))).match(t),a=o.length,f=0;f<a;f+=1){var h=o[f],u=c[h],d=u&&u[0],l=u&&u[1];o[f]=l?{regex:d,parser:l}:h.replace(/^\\[|\\]$/g,\"\")}return function(e){for(var t={},n=0,r=0;n<a;n+=1){var i=o[n];if(\"string\"==typeof i)r+=i.length;else{var s=i.regex,f=i.parser,h=e.slice(r),u=s.exec(h)[0];f.call(t,u),e=e.replace(u,\"\")}}return function(e){var t=e.afternoon;if(void 0!==t){var n=e.hours;t?n<12&&(e.hours+=12):12===n&&(e.hours=0),delete e.afternoon}}(t),t}}return function(e,t,n){n.p.customParseFormat=!0,e&&e.parseTwoDigitYear&&(a=e.parseTwoDigitYear);var r=t.prototype,i=r.parse;r.parse=function(e){var t=e.date,r=e.utc,o=e.args;this.$u=r;var a=o[1];if(\"string\"==typeof a){var f=!0===o[2],h=!0===o[3],u=f||h,d=o[2];h&&(d=o[2]),s=this.$locale(),!f&&d&&(s=n.Ls[d]),this.$d=function(e,t,n,r){try{if([\"x\",\"X\"].indexOf(t)>-1)return new Date((\"X\"===t?1e3:1)*e);var i=l(t)(e),o=i.year,s=i.month,a=i.day,f=i.hours,h=i.minutes,u=i.seconds,d=i.milliseconds,c=i.zone,m=i.week,M=new Date,Y=a||(o||s?1:M.getDate()),p=o||M.getFullYear(),v=0;o&&!s||(v=s>0?s-1:M.getMonth());var D,w=f||0,g=h||0,y=u||0,L=d||0;return c?new Date(Date.UTC(p,v,Y,w,g,y,L+60*c.offset*1e3)):n?new Date(Date.UTC(p,v,Y,w,g,y,L)):(D=new Date(p,v,Y,w,g,y,L),m&&(D=r(D).week(m).toDate()),D)}catch(e){return new Date(\"\")}}(t,a,r,n),this.init(),d&&!0!==d&&(this.$L=this.locale(d).$L),u&&t!=this.format(a)&&(this.$d=new Date(\"\")),s={}}else if(a instanceof Array)for(var c=a.length,m=1;m<=c;m+=1){o[1]=a[m-1];var M=n.apply(this,o);if(M.isValid()){this.$d=M.$d,this.$L=M.$L,this.init();break}m===c&&(this.$d=new Date(\"\"))}else i.call(this,e)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dayjs/plugin/customParseFormat.js\n"));

/***/ }),

/***/ "./node_modules/dayjs/plugin/isoWeek.js":
/*!**********************************************!*\
  !*** ./node_modules/dayjs/plugin/isoWeek.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";var e=\"day\";return function(t,i,s){var a=function(t){return t.add(4-t.isoWeekday(),e)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),e);var i,d,n,o,r=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf(\"year\"),o=4-n.isoWeekday(),n.isoWeekday()>4&&(o+=7),n.add(o,e));return r.diff(u,\"week\")+1},d.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var n=d.startOf;d.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return\"isoweek\"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf(\"day\"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf(\"day\"):n.bind(this)(e,t)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvZGF5anMvcGx1Z2luL2lzb1dlZWsuanMuanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxLQUFvRCxvQkFBb0IsQ0FBOEgsQ0FBQyxrQkFBa0IsYUFBYSxZQUFZLHVCQUF1QixrQkFBa0IsaUNBQWlDLGVBQWUseUJBQXlCLHNCQUFzQix1QkFBdUIsK0RBQStELHdKQUF3SiwwQkFBMEIsMEJBQTBCLHNFQUFzRSxnQkFBZ0Isd0JBQXdCLGtDQUFrQyx5S0FBeUsiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2RheWpzL3BsdWdpbi9pc29XZWVrLmpzPzQ0YjQiXSwic291cmNlc0NvbnRlbnQiOlsiIWZ1bmN0aW9uKGUsdCl7XCJvYmplY3RcIj09dHlwZW9mIGV4cG9ydHMmJlwidW5kZWZpbmVkXCIhPXR5cGVvZiBtb2R1bGU/bW9kdWxlLmV4cG9ydHM9dCgpOlwiZnVuY3Rpb25cIj09dHlwZW9mIGRlZmluZSYmZGVmaW5lLmFtZD9kZWZpbmUodCk6KGU9XCJ1bmRlZmluZWRcIiE9dHlwZW9mIGdsb2JhbFRoaXM/Z2xvYmFsVGhpczplfHxzZWxmKS5kYXlqc19wbHVnaW5faXNvV2Vlaz10KCl9KHRoaXMsKGZ1bmN0aW9uKCl7XCJ1c2Ugc3RyaWN0XCI7dmFyIGU9XCJkYXlcIjtyZXR1cm4gZnVuY3Rpb24odCxpLHMpe3ZhciBhPWZ1bmN0aW9uKHQpe3JldHVybiB0LmFkZCg0LXQuaXNvV2Vla2RheSgpLGUpfSxkPWkucHJvdG90eXBlO2QuaXNvV2Vla1llYXI9ZnVuY3Rpb24oKXtyZXR1cm4gYSh0aGlzKS55ZWFyKCl9LGQuaXNvV2Vlaz1mdW5jdGlvbih0KXtpZighdGhpcy4kdXRpbHMoKS51KHQpKXJldHVybiB0aGlzLmFkZCg3Kih0LXRoaXMuaXNvV2VlaygpKSxlKTt2YXIgaSxkLG4sbyxyPWEodGhpcyksdT0oaT10aGlzLmlzb1dlZWtZZWFyKCksZD10aGlzLiR1LG49KGQ/cy51dGM6cykoKS55ZWFyKGkpLnN0YXJ0T2YoXCJ5ZWFyXCIpLG89NC1uLmlzb1dlZWtkYXkoKSxuLmlzb1dlZWtkYXkoKT40JiYobys9Nyksbi5hZGQobyxlKSk7cmV0dXJuIHIuZGlmZih1LFwid2Vla1wiKSsxfSxkLmlzb1dlZWtkYXk9ZnVuY3Rpb24oZSl7cmV0dXJuIHRoaXMuJHV0aWxzKCkudShlKT90aGlzLmRheSgpfHw3OnRoaXMuZGF5KHRoaXMuZGF5KCklNz9lOmUtNyl9O3ZhciBuPWQuc3RhcnRPZjtkLnN0YXJ0T2Y9ZnVuY3Rpb24oZSx0KXt2YXIgaT10aGlzLiR1dGlscygpLHM9ISFpLnUodCl8fHQ7cmV0dXJuXCJpc293ZWVrXCI9PT1pLnAoZSk/cz90aGlzLmRhdGUodGhpcy5kYXRlKCktKHRoaXMuaXNvV2Vla2RheSgpLTEpKS5zdGFydE9mKFwiZGF5XCIpOnRoaXMuZGF0ZSh0aGlzLmRhdGUoKS0xLSh0aGlzLmlzb1dlZWtkYXkoKS0xKSs3KS5lbmRPZihcImRheVwiKTpuLmJpbmQodGhpcykoZSx0KX19fSkpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/dayjs/plugin/isoWeek.js\n"));

/***/ }),

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-NTVNEXSI.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-NTVNEXSI.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"diagram\": function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-7DKRZKHE.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7DKRZKHE.mjs\");\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @braintree/sanitize-url */ \"./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs_plugin_isoWeek_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs/plugin/isoWeek.js */ \"./node_modules/dayjs/plugin/isoWeek.js\");\n/* harmony import */ var dayjs_plugin_customParseFormat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs/plugin/customParseFormat.js */ \"./node_modules/dayjs/plugin/customParseFormat.js\");\n/* harmony import */ var dayjs_plugin_advancedFormat_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs/plugin/advancedFormat.js */ \"./node_modules/dayjs/plugin/advancedFormat.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! d3 */ \"./node_modules/d3/src/index.js\");\n\n\n\n// src/diagrams/gantt/parser/gantt.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 35, 36, 38, 40], $V1 = [1, 26], $V2 = [1, 27], $V3 = [1, 28], $V4 = [1, 29], $V5 = [1, 30], $V6 = [1, 31], $V7 = [1, 32], $V8 = [1, 33], $V9 = [1, 34], $Va = [1, 9], $Vb = [1, 10], $Vc = [1, 11], $Vd = [1, 12], $Ve = [1, 13], $Vf = [1, 14], $Vg = [1, 15], $Vh = [1, 16], $Vi = [1, 19], $Vj = [1, 20], $Vk = [1, 21], $Vl = [1, 22], $Vm = [1, 23], $Vn = [1, 25], $Vo = [1, 35];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"gantt\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NL\": 10, \"weekday\": 11, \"weekday_monday\": 12, \"weekday_tuesday\": 13, \"weekday_wednesday\": 14, \"weekday_thursday\": 15, \"weekday_friday\": 16, \"weekday_saturday\": 17, \"weekday_sunday\": 18, \"weekend\": 19, \"weekend_friday\": 20, \"weekend_saturday\": 21, \"dateFormat\": 22, \"inclusiveEndDates\": 23, \"topAxis\": 24, \"axisFormat\": 25, \"tickInterval\": 26, \"excludes\": 27, \"includes\": 28, \"todayMarker\": 29, \"title\": 30, \"acc_title\": 31, \"acc_title_value\": 32, \"acc_descr\": 33, \"acc_descr_value\": 34, \"acc_descr_multiline_value\": 35, \"section\": 36, \"clickStatement\": 37, \"taskTxt\": 38, \"taskData\": 39, \"click\": 40, \"callbackname\": 41, \"callbackargs\": 42, \"href\": 43, \"clickStatementDebug\": 44, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"gantt\", 6: \"EOF\", 8: \"SPACE\", 10: \"NL\", 12: \"weekday_monday\", 13: \"weekday_tuesday\", 14: \"weekday_wednesday\", 15: \"weekday_thursday\", 16: \"weekday_friday\", 17: \"weekday_saturday\", 18: \"weekday_sunday\", 20: \"weekend_friday\", 21: \"weekend_saturday\", 22: \"dateFormat\", 23: \"inclusiveEndDates\", 24: \"topAxis\", 25: \"axisFormat\", 26: \"tickInterval\", 27: \"excludes\", 28: \"includes\", 29: \"todayMarker\", 30: \"title\", 31: \"acc_title\", 32: \"acc_title_value\", 33: \"acc_descr\", 34: \"acc_descr_value\", 35: \"acc_descr_multiline_value\", 36: \"section\", 38: \"taskTxt\", 39: \"taskData\", 40: \"click\", 41: \"callbackname\", 42: \"callbackargs\", 43: \"href\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [19, 1], [19, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 2], [37, 2], [37, 3], [37, 3], [37, 4], [37, 3], [37, 4], [37, 2], [44, 2], [44, 3], [44, 3], [44, 4], [44, 3], [44, 4], [44, 2]],\n    performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setWeekday(\"monday\");\n          break;\n        case 9:\n          yy.setWeekday(\"tuesday\");\n          break;\n        case 10:\n          yy.setWeekday(\"wednesday\");\n          break;\n        case 11:\n          yy.setWeekday(\"thursday\");\n          break;\n        case 12:\n          yy.setWeekday(\"friday\");\n          break;\n        case 13:\n          yy.setWeekday(\"saturday\");\n          break;\n        case 14:\n          yy.setWeekday(\"sunday\");\n          break;\n        case 15:\n          yy.setWeekend(\"friday\");\n          break;\n        case 16:\n          yy.setWeekend(\"saturday\");\n          break;\n        case 17:\n          yy.setDateFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 18:\n          yy.enableInclusiveEndDates();\n          this.$ = $$[$0].substr(18);\n          break;\n        case 19:\n          yy.TopAxis();\n          this.$ = $$[$0].substr(8);\n          break;\n        case 20:\n          yy.setAxisFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 21:\n          yy.setTickInterval($$[$0].substr(13));\n          this.$ = $$[$0].substr(13);\n          break;\n        case 22:\n          yy.setExcludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 23:\n          yy.setIncludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 24:\n          yy.setTodayMarker($$[$0].substr(12));\n          this.$ = $$[$0].substr(12);\n          break;\n        case 27:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 28:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 29:\n        case 30:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 31:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 33:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n        case 34:\n          this.$ = $$[$0 - 1];\n          yy.setClickEvent($$[$0 - 1], $$[$0], null);\n          break;\n        case 35:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 36:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], null);\n          yy.setLink($$[$0 - 2], $$[$0]);\n          break;\n        case 37:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 2], $$[$0 - 1]);\n          yy.setLink($$[$0 - 3], $$[$0]);\n          break;\n        case 38:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0], null);\n          yy.setLink($$[$0 - 2], $$[$0 - 1]);\n          break;\n        case 39:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 1], $$[$0]);\n          yy.setLink($$[$0 - 3], $$[$0 - 2]);\n          break;\n        case 40:\n          this.$ = $$[$0 - 1];\n          yy.setLink($$[$0 - 1], $$[$0]);\n          break;\n        case 41:\n        case 47:\n          this.$ = $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 42:\n        case 43:\n        case 45:\n          this.$ = $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 44:\n        case 46:\n          this.$ = $$[$0 - 3] + \" \" + $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: 18, 20: $V8, 21: $V9, 22: $Va, 23: $Vb, 24: $Vc, 25: $Vd, 26: $Ve, 27: $Vf, 28: $Vg, 29: $Vh, 30: $Vi, 31: $Vj, 33: $Vk, 35: $Vl, 36: $Vm, 37: 24, 38: $Vn, 40: $Vo }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 36, 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: 18, 20: $V8, 21: $V9, 22: $Va, 23: $Vb, 24: $Vc, 25: $Vd, 26: $Ve, 27: $Vf, 28: $Vg, 29: $Vh, 30: $Vi, 31: $Vj, 33: $Vk, 35: $Vl, 36: $Vm, 37: 24, 38: $Vn, 40: $Vo }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 17]), o($V0, [2, 18]), o($V0, [2, 19]), o($V0, [2, 20]), o($V0, [2, 21]), o($V0, [2, 22]), o($V0, [2, 23]), o($V0, [2, 24]), o($V0, [2, 25]), o($V0, [2, 26]), o($V0, [2, 27]), { 32: [1, 37] }, { 34: [1, 38] }, o($V0, [2, 30]), o($V0, [2, 31]), o($V0, [2, 32]), { 39: [1, 39] }, o($V0, [2, 8]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), o($V0, [2, 15]), o($V0, [2, 16]), { 41: [1, 40], 43: [1, 41] }, o($V0, [2, 4]), o($V0, [2, 28]), o($V0, [2, 29]), o($V0, [2, 33]), o($V0, [2, 34], { 42: [1, 42], 43: [1, 43] }), o($V0, [2, 40], { 41: [1, 44] }), o($V0, [2, 35], { 43: [1, 45] }), o($V0, [2, 36]), o($V0, [2, 38], { 42: [1, 46] }), o($V0, [2, 37]), o($V0, [2, 39])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.begin(\"open_directive\");\n            return \"open_directive\";\n            break;\n          case 1:\n            this.begin(\"acc_title\");\n            return 31;\n            break;\n          case 2:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 3:\n            this.begin(\"acc_descr\");\n            return 33;\n            break;\n          case 4:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 5:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 6:\n            this.popState();\n            break;\n          case 7:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            return 10;\n            break;\n          case 12:\n            break;\n          case 13:\n            break;\n          case 14:\n            this.begin(\"href\");\n            break;\n          case 15:\n            this.popState();\n            break;\n          case 16:\n            return 43;\n            break;\n          case 17:\n            this.begin(\"callbackname\");\n            break;\n          case 18:\n            this.popState();\n            break;\n          case 19:\n            this.popState();\n            this.begin(\"callbackargs\");\n            break;\n          case 20:\n            return 41;\n            break;\n          case 21:\n            this.popState();\n            break;\n          case 22:\n            return 42;\n            break;\n          case 23:\n            this.begin(\"click\");\n            break;\n          case 24:\n            this.popState();\n            break;\n          case 25:\n            return 40;\n            break;\n          case 26:\n            return 4;\n            break;\n          case 27:\n            return 22;\n            break;\n          case 28:\n            return 23;\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 25;\n            break;\n          case 31:\n            return 26;\n            break;\n          case 32:\n            return 28;\n            break;\n          case 33:\n            return 27;\n            break;\n          case 34:\n            return 29;\n            break;\n          case 35:\n            return 12;\n            break;\n          case 36:\n            return 13;\n            break;\n          case 37:\n            return 14;\n            break;\n          case 38:\n            return 15;\n            break;\n          case 39:\n            return 16;\n            break;\n          case 40:\n            return 17;\n            break;\n          case 41:\n            return 18;\n            break;\n          case 42:\n            return 20;\n            break;\n          case 43:\n            return 21;\n            break;\n          case 44:\n            return \"date\";\n            break;\n          case 45:\n            return 30;\n            break;\n          case 46:\n            return \"accDescription\";\n            break;\n          case 47:\n            return 36;\n            break;\n          case 48:\n            return 38;\n            break;\n          case 49:\n            return 39;\n            break;\n          case 50:\n            return \":\";\n            break;\n          case 51:\n            return 6;\n            break;\n          case 52:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%\\{)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:%%(?!\\{)*[^\\n]*)/i, /^(?:[^\\}]%%*[^\\n]*)/i, /^(?:%%*[^\\n]*[\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:%[^\\n]*)/i, /^(?:href[\\s]+[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:call[\\s]+)/i, /^(?:\\([\\s]*\\))/i, /^(?:\\()/i, /^(?:[^(]*)/i, /^(?:\\))/i, /^(?:[^)]*)/i, /^(?:click[\\s]+)/i, /^(?:[\\s\\n])/i, /^(?:[^\\s\\n]*)/i, /^(?:gantt\\b)/i, /^(?:dateFormat\\s[^#\\n;]+)/i, /^(?:inclusiveEndDates\\b)/i, /^(?:topAxis\\b)/i, /^(?:axisFormat\\s[^#\\n;]+)/i, /^(?:tickInterval\\s[^#\\n;]+)/i, /^(?:includes\\s[^#\\n;]+)/i, /^(?:excludes\\s[^#\\n;]+)/i, /^(?:todayMarker\\s[^\\n;]+)/i, /^(?:weekday\\s+monday\\b)/i, /^(?:weekday\\s+tuesday\\b)/i, /^(?:weekday\\s+wednesday\\b)/i, /^(?:weekday\\s+thursday\\b)/i, /^(?:weekday\\s+friday\\b)/i, /^(?:weekday\\s+saturday\\b)/i, /^(?:weekday\\s+sunday\\b)/i, /^(?:weekend\\s+friday\\b)/i, /^(?:weekend\\s+saturday\\b)/i, /^(?:\\d\\d\\d\\d-\\d\\d-\\d\\d\\b)/i, /^(?:title\\s[^\\n]+)/i, /^(?:accDescription\\s[^#\\n;]+)/i, /^(?:section\\s[^\\n]+)/i, /^(?:[^:\\n]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [6, 7], \"inclusive\": false }, \"acc_descr\": { \"rules\": [4], \"inclusive\": false }, \"acc_title\": { \"rules\": [2], \"inclusive\": false }, \"callbackargs\": { \"rules\": [21, 22], \"inclusive\": false }, \"callbackname\": { \"rules\": [18, 19, 20], \"inclusive\": false }, \"href\": { \"rules\": [15, 16], \"inclusive\": false }, \"click\": { \"rules\": [24, 25], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 5, 8, 9, 10, 11, 12, 13, 14, 17, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar gantt_default = parser;\n\n// src/diagrams/gantt/ganttDb.js\n\n\n\n\n\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_isoWeek_js__WEBPACK_IMPORTED_MODULE_4__);\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_customParseFormat_js__WEBPACK_IMPORTED_MODULE_5__);\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_advancedFormat_js__WEBPACK_IMPORTED_MODULE_6__);\nvar WEEKEND_START_DAY = { friday: 5, saturday: 6 };\nvar dateFormat = \"\";\nvar axisFormat = \"\";\nvar tickInterval = void 0;\nvar todayMarker = \"\";\nvar includes = [];\nvar excludes = [];\nvar links = /* @__PURE__ */ new Map();\nvar sections = [];\nvar tasks = [];\nvar currentSection = \"\";\nvar displayMode = \"\";\nvar tags = [\"active\", \"done\", \"crit\", \"milestone\"];\nvar funs = [];\nvar inclusiveEndDates = false;\nvar topAxis = false;\nvar weekday = \"sunday\";\nvar weekend = \"saturday\";\nvar lastOrder = 0;\nvar clear2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  sections = [];\n  tasks = [];\n  currentSection = \"\";\n  funs = [];\n  taskCnt = 0;\n  lastTask = void 0;\n  lastTaskID = void 0;\n  rawTasks = [];\n  dateFormat = \"\";\n  axisFormat = \"\";\n  displayMode = \"\";\n  tickInterval = void 0;\n  todayMarker = \"\";\n  includes = [];\n  excludes = [];\n  inclusiveEndDates = false;\n  topAxis = false;\n  lastOrder = 0;\n  links = /* @__PURE__ */ new Map();\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.clear)();\n  weekday = \"sunday\";\n  weekend = \"saturday\";\n}, \"clear\");\nvar setAxisFormat = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  axisFormat = txt;\n}, \"setAxisFormat\");\nvar getAxisFormat = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return axisFormat;\n}, \"getAxisFormat\");\nvar setTickInterval = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  tickInterval = txt;\n}, \"setTickInterval\");\nvar getTickInterval = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return tickInterval;\n}, \"getTickInterval\");\nvar setTodayMarker = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  todayMarker = txt;\n}, \"setTodayMarker\");\nvar getTodayMarker = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return todayMarker;\n}, \"getTodayMarker\");\nvar setDateFormat = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  dateFormat = txt;\n}, \"setDateFormat\");\nvar enableInclusiveEndDates = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  inclusiveEndDates = true;\n}, \"enableInclusiveEndDates\");\nvar endDatesAreInclusive = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return inclusiveEndDates;\n}, \"endDatesAreInclusive\");\nvar enableTopAxis = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  topAxis = true;\n}, \"enableTopAxis\");\nvar topAxisEnabled = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return topAxis;\n}, \"topAxisEnabled\");\nvar setDisplayMode = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  displayMode = txt;\n}, \"setDisplayMode\");\nvar getDisplayMode = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return displayMode;\n}, \"getDisplayMode\");\nvar getDateFormat = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return dateFormat;\n}, \"getDateFormat\");\nvar setIncludes = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  includes = txt.toLowerCase().split(/[\\s,]+/);\n}, \"setIncludes\");\nvar getIncludes = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return includes;\n}, \"getIncludes\");\nvar setExcludes = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  excludes = txt.toLowerCase().split(/[\\s,]+/);\n}, \"setExcludes\");\nvar getExcludes = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return excludes;\n}, \"getExcludes\");\nvar getLinks = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return links;\n}, \"getLinks\");\nvar addSection = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 10;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks = rawTasks;\n  return tasks;\n}, \"getTasks\");\nvar isInvalidDate = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(date, dateFormat2, excludes2, includes2) {\n  if (includes2.includes(date.format(dateFormat2.trim()))) {\n    return false;\n  }\n  if (excludes2.includes(\"weekends\") && (date.isoWeekday() === WEEKEND_START_DAY[weekend] || date.isoWeekday() === WEEKEND_START_DAY[weekend] + 1)) {\n    return true;\n  }\n  if (excludes2.includes(date.format(\"dddd\").toLowerCase())) {\n    return true;\n  }\n  return excludes2.includes(date.format(dateFormat2.trim()));\n}, \"isInvalidDate\");\nvar setWeekday = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  weekday = txt;\n}, \"setWeekday\");\nvar getWeekday = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return weekday;\n}, \"getWeekday\");\nvar setWeekend = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startDay) {\n  weekend = startDay;\n}, \"setWeekend\");\nvar checkTaskDates = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(task, dateFormat2, excludes2, includes2) {\n  if (!excludes2.length || task.manualEndTime) {\n    return;\n  }\n  let startTime;\n  if (task.startTime instanceof Date) {\n    startTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.startTime);\n  } else {\n    startTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.startTime, dateFormat2, true);\n  }\n  startTime = startTime.add(1, \"d\");\n  let originalEndTime;\n  if (task.endTime instanceof Date) {\n    originalEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.endTime);\n  } else {\n    originalEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.endTime, dateFormat2, true);\n  }\n  const [fixedEndTime, renderEndTime] = fixTaskDates(\n    startTime,\n    originalEndTime,\n    dateFormat2,\n    excludes2,\n    includes2\n  );\n  task.endTime = fixedEndTime.toDate();\n  task.renderEndTime = renderEndTime;\n}, \"checkTaskDates\");\nvar fixTaskDates = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startTime, endTime, dateFormat2, excludes2, includes2) {\n  let invalid = false;\n  let renderEndTime = null;\n  while (startTime <= endTime) {\n    if (!invalid) {\n      renderEndTime = endTime.toDate();\n    }\n    invalid = isInvalidDate(startTime, dateFormat2, excludes2, includes2);\n    if (invalid) {\n      endTime = endTime.add(1, \"d\");\n    }\n    startTime = startTime.add(1, \"d\");\n  }\n  return [endTime, renderEndTime];\n}, \"fixTaskDates\");\nvar getStartDate = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTime, dateFormat2, str) {\n  str = str.trim();\n  const afterRePattern = /^after\\s+(?<ids>[\\d\\w- ]+)/;\n  const afterStatement = afterRePattern.exec(str);\n  if (afterStatement !== null) {\n    let latestTask = null;\n    for (const id of afterStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!latestTask || task.endTime > latestTask.endTime)) {\n        latestTask = task;\n      }\n    }\n    if (latestTask) {\n      return latestTask.endTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let mDate = dayjs__WEBPACK_IMPORTED_MODULE_3__(str, dateFormat2.trim(), true);\n  if (mDate.isValid()) {\n    return mDate.toDate();\n  } else {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"Invalid date:\" + str);\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"With date format:\" + dateFormat2.trim());\n    const d = new Date(str);\n    if (d === void 0 || isNaN(d.getTime()) || // WebKit browsers can mis-parse invalid dates to be ridiculously\n    // huge numbers, e.g. new Date('202304') gets parsed as January 1, 202304.\n    // This can cause virtually infinite loops while rendering, so for the\n    // purposes of Gantt charts we'll just treat any date beyond 10,000 AD/BC as\n    // invalid.\n    d.getFullYear() < -1e4 || d.getFullYear() > 1e4) {\n      throw new Error(\"Invalid date:\" + str);\n    }\n    return d;\n  }\n}, \"getStartDate\");\nvar parseDuration = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(str) {\n  const statement = /^(\\d+(?:\\.\\d+)?)([Mdhmswy]|ms)$/.exec(str.trim());\n  if (statement !== null) {\n    return [Number.parseFloat(statement[1]), statement[2]];\n  }\n  return [NaN, \"ms\"];\n}, \"parseDuration\");\nvar getEndDate = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTime, dateFormat2, str, inclusive = false) {\n  str = str.trim();\n  const untilRePattern = /^until\\s+(?<ids>[\\d\\w- ]+)/;\n  const untilStatement = untilRePattern.exec(str);\n  if (untilStatement !== null) {\n    let earliestTask = null;\n    for (const id of untilStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!earliestTask || task.startTime < earliestTask.startTime)) {\n        earliestTask = task;\n      }\n    }\n    if (earliestTask) {\n      return earliestTask.startTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let parsedDate = dayjs__WEBPACK_IMPORTED_MODULE_3__(str, dateFormat2.trim(), true);\n  if (parsedDate.isValid()) {\n    if (inclusive) {\n      parsedDate = parsedDate.add(1, \"d\");\n    }\n    return parsedDate.toDate();\n  }\n  let endTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(prevTime);\n  const [durationValue, durationUnit] = parseDuration(str);\n  if (!Number.isNaN(durationValue)) {\n    const newEndTime = endTime.add(durationValue, durationUnit);\n    if (newEndTime.isValid()) {\n      endTime = newEndTime;\n    }\n  }\n  return endTime.toDate();\n}, \"getEndDate\");\nvar taskCnt = 0;\nvar parseId = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(idStr) {\n  if (idStr === void 0) {\n    taskCnt = taskCnt + 1;\n    return \"task\" + taskCnt;\n  }\n  return idStr;\n}, \"parseId\");\nvar compileData = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTask, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  let endTimeData = \"\";\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = prevTask.endTime;\n      endTimeData = data[0];\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = getStartDate(void 0, dateFormat, data[0]);\n      endTimeData = data[1];\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = getStartDate(void 0, dateFormat, data[1]);\n      endTimeData = data[2];\n      break;\n    default:\n  }\n  if (endTimeData) {\n    task.endTime = getEndDate(task.startTime, dateFormat, endTimeData, inclusiveEndDates);\n    task.manualEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(endTimeData, \"YYYY-MM-DD\", true).isValid();\n    checkTaskDates(task, dateFormat, excludes, includes);\n  }\n  return task;\n}, \"compileData\");\nvar parseData = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTaskId, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = {\n        type: \"prevTaskEnd\",\n        id: prevTaskId\n      };\n      task.endTime = {\n        data: data[0]\n      };\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[0]\n      };\n      task.endTime = {\n        data: data[1]\n      };\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[1]\n      };\n      task.endTime = {\n        data: data[2]\n      };\n      break;\n    default:\n  }\n  return task;\n}, \"parseData\");\nvar lastTask;\nvar lastTaskID;\nvar rawTasks = [];\nvar taskDb = {};\nvar addTask = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr, data) {\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    processed: false,\n    manualEndTime: false,\n    renderEndTime: null,\n    raw: { data },\n    task: descr,\n    classes: []\n  };\n  const taskInfo = parseData(lastTaskID, data);\n  rawTask.raw.startTime = taskInfo.startTime;\n  rawTask.raw.endTime = taskInfo.endTime;\n  rawTask.id = taskInfo.id;\n  rawTask.prevTaskId = lastTaskID;\n  rawTask.active = taskInfo.active;\n  rawTask.done = taskInfo.done;\n  rawTask.crit = taskInfo.crit;\n  rawTask.milestone = taskInfo.milestone;\n  rawTask.order = lastOrder;\n  lastOrder++;\n  const pos = rawTasks.push(rawTask);\n  lastTaskID = rawTask.id;\n  taskDb[rawTask.id] = pos - 1;\n}, \"addTask\");\nvar findTaskById = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id) {\n  const pos = taskDb[id];\n  return rawTasks[pos];\n}, \"findTaskById\");\nvar addTaskOrg = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr, data) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  const taskInfo = compileData(lastTask, data);\n  newTask.startTime = taskInfo.startTime;\n  newTask.endTime = taskInfo.endTime;\n  newTask.id = taskInfo.id;\n  newTask.active = taskInfo.active;\n  newTask.done = taskInfo.done;\n  newTask.crit = taskInfo.crit;\n  newTask.milestone = taskInfo.milestone;\n  lastTask = newTask;\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  const compileTask = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(pos) {\n    const task = rawTasks[pos];\n    let startTime = \"\";\n    switch (rawTasks[pos].raw.startTime.type) {\n      case \"prevTaskEnd\": {\n        const prevTask = findTaskById(task.prevTaskId);\n        task.startTime = prevTask.endTime;\n        break;\n      }\n      case \"getStartDate\":\n        startTime = getStartDate(void 0, dateFormat, rawTasks[pos].raw.startTime.startData);\n        if (startTime) {\n          rawTasks[pos].startTime = startTime;\n        }\n        break;\n    }\n    if (rawTasks[pos].startTime) {\n      rawTasks[pos].endTime = getEndDate(\n        rawTasks[pos].startTime,\n        dateFormat,\n        rawTasks[pos].raw.endTime.data,\n        inclusiveEndDates\n      );\n      if (rawTasks[pos].endTime) {\n        rawTasks[pos].processed = true;\n        rawTasks[pos].manualEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(\n          rawTasks[pos].raw.endTime.data,\n          \"YYYY-MM-DD\",\n          true\n        ).isValid();\n        checkTaskDates(rawTasks[pos], dateFormat, excludes, includes);\n      }\n    }\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar setLink = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, _linkStr) {\n  let linkStr = _linkStr;\n  if ((0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel !== \"loose\") {\n    linkStr = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_2__.sanitizeUrl)(_linkStr);\n  }\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      pushFun(id, () => {\n        window.open(linkStr, \"_self\");\n      });\n      links.set(id, linkStr);\n    }\n  });\n  setClass(ids, \"clickable\");\n}, \"setLink\");\nvar setClass = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, className) {\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      rawTask.classes.push(className);\n    }\n  });\n}, \"setClass\");\nvar setClickFun = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id, functionName, functionArgs) {\n  if ((0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel !== \"loose\") {\n    return;\n  }\n  if (functionName === void 0) {\n    return;\n  }\n  let argList = [];\n  if (typeof functionArgs === \"string\") {\n    argList = functionArgs.split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/);\n    for (let i = 0; i < argList.length; i++) {\n      let item = argList[i].trim();\n      if (item.startsWith('\"') && item.endsWith('\"')) {\n        item = item.substr(1, item.length - 2);\n      }\n      argList[i] = item;\n    }\n  }\n  if (argList.length === 0) {\n    argList.push(id);\n  }\n  let rawTask = findTaskById(id);\n  if (rawTask !== void 0) {\n    pushFun(id, () => {\n      _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_0__.utils_default.runFunc(functionName, ...argList);\n    });\n  }\n}, \"setClickFun\");\nvar pushFun = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id, callbackFunction) {\n  funs.push(\n    function() {\n      const elem = document.querySelector(`[id=\"${id}\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    },\n    function() {\n      const elem = document.querySelector(`[id=\"${id}-text\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    }\n  );\n}, \"pushFun\");\nvar setClickEvent = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, functionName, functionArgs) {\n  ids.split(\",\").forEach(function(id) {\n    setClickFun(id, functionName, functionArgs);\n  });\n  setClass(ids, \"clickable\");\n}, \"setClickEvent\");\nvar bindFunctions = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(element) {\n  funs.forEach(function(fun) {\n    fun(element);\n  });\n}, \"bindFunctions\");\nvar ganttDb_default = {\n  getConfig: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(() => (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().gantt, \"getConfig\"),\n  clear: clear2,\n  setDateFormat,\n  getDateFormat,\n  enableInclusiveEndDates,\n  endDatesAreInclusive,\n  enableTopAxis,\n  topAxisEnabled,\n  setAxisFormat,\n  getAxisFormat,\n  setTickInterval,\n  getTickInterval,\n  setTodayMarker,\n  getTodayMarker,\n  setAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccTitle,\n  getAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccTitle,\n  setDiagramTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.setDiagramTitle,\n  getDiagramTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getDiagramTitle,\n  setDisplayMode,\n  getDisplayMode,\n  setAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccDescription,\n  getAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  findTaskById,\n  addTaskOrg,\n  setIncludes,\n  getIncludes,\n  setExcludes,\n  getExcludes,\n  setClickEvent,\n  setLink,\n  getLinks,\n  bindFunctions,\n  parseDuration,\n  isInvalidDate,\n  setWeekday,\n  getWeekday,\n  setWeekend\n};\nfunction getTaskTags(data, task, tags2) {\n  let matchFound = true;\n  while (matchFound) {\n    matchFound = false;\n    tags2.forEach(function(t) {\n      const pattern = \"^\\\\s*\" + t + \"\\\\s*$\";\n      const regex = new RegExp(pattern);\n      if (data[0].match(regex)) {\n        task[t] = true;\n        data.shift(1);\n        matchFound = true;\n      }\n    });\n  }\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(getTaskTags, \"getTaskTags\");\n\n// src/diagrams/gantt/ganttRenderer.js\n\n\nvar setConf = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"Something is calling, setConf, remove the call\");\n}, \"setConf\");\nvar mapWeekdayToTimeFunction = {\n  monday: d3__WEBPACK_IMPORTED_MODULE_7__.timeMonday,\n  tuesday: d3__WEBPACK_IMPORTED_MODULE_7__.timeTuesday,\n  wednesday: d3__WEBPACK_IMPORTED_MODULE_7__.timeWednesday,\n  thursday: d3__WEBPACK_IMPORTED_MODULE_7__.timeThursday,\n  friday: d3__WEBPACK_IMPORTED_MODULE_7__.timeFriday,\n  saturday: d3__WEBPACK_IMPORTED_MODULE_7__.timeSaturday,\n  sunday: d3__WEBPACK_IMPORTED_MODULE_7__.timeSunday\n};\nvar getMaxIntersections = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((tasks2, orderOffset) => {\n  let timeline = [...tasks2].map(() => -Infinity);\n  let sorted = [...tasks2].sort((a, b) => a.startTime - b.startTime || a.order - b.order);\n  let maxIntersections = 0;\n  for (const element of sorted) {\n    for (let j = 0; j < timeline.length; j++) {\n      if (element.startTime >= timeline[j]) {\n        timeline[j] = element.endTime;\n        element.order = j + orderOffset;\n        if (j > maxIntersections) {\n          maxIntersections = j;\n        }\n        break;\n      }\n    }\n  }\n  return maxIntersections;\n}, \"getMaxIntersections\");\nvar w;\nvar draw = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(text, id, version, diagObj) {\n  const conf = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().gantt;\n  const securityLevel = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  const elem = doc.getElementById(id);\n  w = elem.parentElement.offsetWidth;\n  if (w === void 0) {\n    w = 1200;\n  }\n  if (conf.useWidth !== void 0) {\n    w = conf.useWidth;\n  }\n  const taskArray = diagObj.db.getTasks();\n  let categories = [];\n  for (const element of taskArray) {\n    categories.push(element.type);\n  }\n  categories = checkUnique(categories);\n  const categoryHeights = {};\n  let h = 2 * conf.topPadding;\n  if (diagObj.db.getDisplayMode() === \"compact\" || conf.displayMode === \"compact\") {\n    const categoryElements = {};\n    for (const element of taskArray) {\n      if (categoryElements[element.section] === void 0) {\n        categoryElements[element.section] = [element];\n      } else {\n        categoryElements[element.section].push(element);\n      }\n    }\n    let intersections = 0;\n    for (const category of Object.keys(categoryElements)) {\n      const categoryHeight = getMaxIntersections(categoryElements[category], intersections) + 1;\n      intersections += categoryHeight;\n      h += categoryHeight * (conf.barHeight + conf.barGap);\n      categoryHeights[category] = categoryHeight;\n    }\n  } else {\n    h += taskArray.length * (conf.barHeight + conf.barGap);\n    for (const category of categories) {\n      categoryHeights[category] = taskArray.filter((task) => task.type === category).length;\n    }\n  }\n  elem.setAttribute(\"viewBox\", \"0 0 \" + w + \" \" + h);\n  const svg = root.select(`[id=\"${id}\"]`);\n  const timeScale = (0,d3__WEBPACK_IMPORTED_MODULE_7__.scaleTime)().domain([\n    (0,d3__WEBPACK_IMPORTED_MODULE_7__.min)(taskArray, function(d) {\n      return d.startTime;\n    }),\n    (0,d3__WEBPACK_IMPORTED_MODULE_7__.max)(taskArray, function(d) {\n      return d.endTime;\n    })\n  ]).rangeRound([0, w - conf.leftPadding - conf.rightPadding]);\n  function taskCompare(a, b) {\n    const taskA = a.startTime;\n    const taskB = b.startTime;\n    let result = 0;\n    if (taskA > taskB) {\n      result = 1;\n    } else if (taskA < taskB) {\n      result = -1;\n    }\n    return result;\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(taskCompare, \"taskCompare\");\n  taskArray.sort(taskCompare);\n  makeGantt(taskArray, w, h);\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.configureSvgSize)(svg, h, w, conf.useMaxWidth);\n  svg.append(\"text\").text(diagObj.db.getDiagramTitle()).attr(\"x\", w / 2).attr(\"y\", conf.titleTopMargin).attr(\"class\", \"titleText\");\n  function makeGantt(tasks2, pageWidth, pageHeight) {\n    const barHeight = conf.barHeight;\n    const gap = barHeight + conf.barGap;\n    const topPadding = conf.topPadding;\n    const leftPadding = conf.leftPadding;\n    const colorScale = (0,d3__WEBPACK_IMPORTED_MODULE_7__.scaleLinear)().domain([0, categories.length]).range([\"#00B9FA\", \"#F95002\"]).interpolate(d3__WEBPACK_IMPORTED_MODULE_7__.interpolateHcl);\n    drawExcludeDays(\n      gap,\n      topPadding,\n      leftPadding,\n      pageWidth,\n      pageHeight,\n      tasks2,\n      diagObj.db.getExcludes(),\n      diagObj.db.getIncludes()\n    );\n    makeGrid(leftPadding, topPadding, pageWidth, pageHeight);\n    drawRects(tasks2, gap, topPadding, leftPadding, barHeight, colorScale, pageWidth, pageHeight);\n    vertLabels(gap, topPadding, leftPadding, barHeight, colorScale);\n    drawToday(leftPadding, topPadding, pageWidth, pageHeight);\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(makeGantt, \"makeGantt\");\n  function drawRects(theArray, theGap, theTopPad, theSidePad, theBarHeight, theColorScale, w2) {\n    const uniqueTaskOrderIds = [...new Set(theArray.map((item) => item.order))];\n    const uniqueTasks = uniqueTaskOrderIds.map((id2) => theArray.find((item) => item.order === id2));\n    svg.append(\"g\").selectAll(\"rect\").data(uniqueTasks).enter().append(\"rect\").attr(\"x\", 0).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad - 2;\n    }).attr(\"width\", function() {\n      return w2 - conf.rightPadding / 2;\n    }).attr(\"height\", theGap).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          return \"section section\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"section section0\";\n    });\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(theArray).enter();\n    const links2 = diagObj.db.getLinks();\n    rectangles.append(\"rect\").attr(\"id\", function(d) {\n      return d.id;\n    }).attr(\"rx\", 3).attr(\"ry\", 3).attr(\"x\", function(d) {\n      if (d.milestone) {\n        return timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      return timeScale(d.startTime) + theSidePad;\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad;\n    }).attr(\"width\", function(d) {\n      if (d.milestone) {\n        return theBarHeight;\n      }\n      return timeScale(d.renderEndTime || d.endTime) - timeScale(d.startTime);\n    }).attr(\"height\", theBarHeight).attr(\"transform-origin\", function(d, i) {\n      i = d.order;\n      return (timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime))).toString() + \"px \" + (i * theGap + theTopPad + 0.5 * theBarHeight).toString() + \"px\";\n    }).attr(\"class\", function(d) {\n      const res = \"task\";\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskClass = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskClass += \" activeCrit\";\n        } else {\n          taskClass = \" active\";\n        }\n      } else if (d.done) {\n        if (d.crit) {\n          taskClass = \" doneCrit\";\n        } else {\n          taskClass = \" done\";\n        }\n      } else {\n        if (d.crit) {\n          taskClass += \" crit\";\n        }\n      }\n      if (taskClass.length === 0) {\n        taskClass = \" task\";\n      }\n      if (d.milestone) {\n        taskClass = \" milestone \" + taskClass;\n      }\n      taskClass += secNum;\n      taskClass += \" \" + classStr;\n      return res + taskClass;\n    });\n    rectangles.append(\"text\").attr(\"id\", function(d) {\n      return d.id + \"-text\";\n    }).text(function(d) {\n      return d.task;\n    }).attr(\"font-size\", conf.fontSize).attr(\"x\", function(d) {\n      let startX = timeScale(d.startTime);\n      let endX = timeScale(d.renderEndTime || d.endTime);\n      if (d.milestone) {\n        startX += 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return startX + theSidePad - 5;\n        } else {\n          return endX + theSidePad + 5;\n        }\n      } else {\n        return (endX - startX) / 2 + startX + theSidePad;\n      }\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + conf.barHeight / 2 + (conf.fontSize / 2 - 2) + theTopPad;\n    }).attr(\"text-height\", theBarHeight).attr(\"class\", function(d) {\n      const startX = timeScale(d.startTime);\n      let endX = timeScale(d.endTime);\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskType = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskType = \"activeCritText\" + secNum;\n        } else {\n          taskType = \"activeText\" + secNum;\n        }\n      }\n      if (d.done) {\n        if (d.crit) {\n          taskType = taskType + \" doneCritText\" + secNum;\n        } else {\n          taskType = taskType + \" doneText\" + secNum;\n        }\n      } else {\n        if (d.crit) {\n          taskType = taskType + \" critText\" + secNum;\n        }\n      }\n      if (d.milestone) {\n        taskType += \" milestoneText\";\n      }\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return classStr + \" taskTextOutsideLeft taskTextOutside\" + secNum + \" \" + taskType;\n        } else {\n          return classStr + \" taskTextOutsideRight taskTextOutside\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n        }\n      } else {\n        return classStr + \" taskText taskText\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n      }\n    });\n    const securityLevel2 = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel;\n    if (securityLevel2 === \"sandbox\") {\n      let sandboxElement2;\n      sandboxElement2 = (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"#i\" + id);\n      const doc2 = sandboxElement2.nodes()[0].contentDocument;\n      rectangles.filter(function(d) {\n        return links2.has(d.id);\n      }).each(function(o) {\n        var taskRect = doc2.querySelector(\"#\" + o.id);\n        var taskText = doc2.querySelector(\"#\" + o.id + \"-text\");\n        const oldParent = taskRect.parentNode;\n        var Link = doc2.createElement(\"a\");\n        Link.setAttribute(\"xlink:href\", links2.get(o.id));\n        Link.setAttribute(\"target\", \"_top\");\n        oldParent.appendChild(Link);\n        Link.appendChild(taskRect);\n        Link.appendChild(taskText);\n      });\n    }\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawRects, \"drawRects\");\n  function drawExcludeDays(theGap, theTopPad, theSidePad, w2, h2, tasks2, excludes2, includes2) {\n    if (excludes2.length === 0 && includes2.length === 0) {\n      return;\n    }\n    let minTime;\n    let maxTime;\n    for (const { startTime, endTime } of tasks2) {\n      if (minTime === void 0 || startTime < minTime) {\n        minTime = startTime;\n      }\n      if (maxTime === void 0 || endTime > maxTime) {\n        maxTime = endTime;\n      }\n    }\n    if (!minTime || !maxTime) {\n      return;\n    }\n    if (dayjs__WEBPACK_IMPORTED_MODULE_3__(maxTime).diff(dayjs__WEBPACK_IMPORTED_MODULE_3__(minTime), \"year\") > 5) {\n      _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.log.warn(\n        \"The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.\"\n      );\n      return;\n    }\n    const dateFormat2 = diagObj.db.getDateFormat();\n    const excludeRanges = [];\n    let range = null;\n    let d = dayjs__WEBPACK_IMPORTED_MODULE_3__(minTime);\n    while (d.valueOf() <= maxTime) {\n      if (diagObj.db.isInvalidDate(d, dateFormat2, excludes2, includes2)) {\n        if (!range) {\n          range = {\n            start: d,\n            end: d\n          };\n        } else {\n          range.end = d;\n        }\n      } else {\n        if (range) {\n          excludeRanges.push(range);\n          range = null;\n        }\n      }\n      d = d.add(1, \"d\");\n    }\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(excludeRanges).enter();\n    rectangles.append(\"rect\").attr(\"id\", function(d2) {\n      return \"exclude-\" + d2.start.format(\"YYYY-MM-DD\");\n    }).attr(\"x\", function(d2) {\n      return timeScale(d2.start) + theSidePad;\n    }).attr(\"y\", conf.gridLineStartPadding).attr(\"width\", function(d2) {\n      const renderEnd = d2.end.add(1, \"day\");\n      return timeScale(renderEnd) - timeScale(d2.start);\n    }).attr(\"height\", h2 - theTopPad - conf.gridLineStartPadding).attr(\"transform-origin\", function(d2, i) {\n      return (timeScale(d2.start) + theSidePad + 0.5 * (timeScale(d2.end) - timeScale(d2.start))).toString() + \"px \" + (i * theGap + 0.5 * h2).toString() + \"px\";\n    }).attr(\"class\", \"exclude-range\");\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawExcludeDays, \"drawExcludeDays\");\n  function makeGrid(theSidePad, theTopPad, w2, h2) {\n    let bottomXAxis = (0,d3__WEBPACK_IMPORTED_MODULE_7__.axisBottom)(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0,d3__WEBPACK_IMPORTED_MODULE_7__.timeFormat)(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n    const reTickInterval = /^([1-9]\\d*)(millisecond|second|minute|hour|day|week|month)$/;\n    const resultTickInterval = reTickInterval.exec(\n      diagObj.db.getTickInterval() || conf.tickInterval\n    );\n    if (resultTickInterval !== null) {\n      const every = resultTickInterval[1];\n      const interval = resultTickInterval[2];\n      const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n      switch (interval) {\n        case \"millisecond\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMillisecond.every(every));\n          break;\n        case \"second\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeSecond.every(every));\n          break;\n        case \"minute\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMinute.every(every));\n          break;\n        case \"hour\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeHour.every(every));\n          break;\n        case \"day\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeDay.every(every));\n          break;\n        case \"week\":\n          bottomXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n          break;\n        case \"month\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMonth.every(every));\n          break;\n      }\n    }\n    svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + (h2 - 50) + \")\").call(bottomXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10).attr(\"dy\", \"1em\");\n    if (diagObj.db.topAxisEnabled() || conf.topAxis) {\n      let topXAxis = (0,d3__WEBPACK_IMPORTED_MODULE_7__.axisTop)(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0,d3__WEBPACK_IMPORTED_MODULE_7__.timeFormat)(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n      if (resultTickInterval !== null) {\n        const every = resultTickInterval[1];\n        const interval = resultTickInterval[2];\n        const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n        switch (interval) {\n          case \"millisecond\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMillisecond.every(every));\n            break;\n          case \"second\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeSecond.every(every));\n            break;\n          case \"minute\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMinute.every(every));\n            break;\n          case \"hour\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeHour.every(every));\n            break;\n          case \"day\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeDay.every(every));\n            break;\n          case \"week\":\n            topXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n            break;\n          case \"month\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMonth.every(every));\n            break;\n        }\n      }\n      svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + theTopPad + \")\").call(topXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10);\n    }\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(makeGrid, \"makeGrid\");\n  function vertLabels(theGap, theTopPad) {\n    let prevGap = 0;\n    const numOccurrences = Object.keys(categoryHeights).map((d) => [d, categoryHeights[d]]);\n    svg.append(\"g\").selectAll(\"text\").data(numOccurrences).enter().append(function(d) {\n      const rows = d[0].split(_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.common_default.lineBreakRegex);\n      const dy = -(rows.length - 1) / 2;\n      const svgLabel = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n      svgLabel.setAttribute(\"dy\", dy + \"em\");\n      for (const [j, row] of rows.entries()) {\n        const tspan = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n        tspan.setAttribute(\"alignment-baseline\", \"central\");\n        tspan.setAttribute(\"x\", \"10\");\n        if (j > 0) {\n          tspan.setAttribute(\"dy\", \"1em\");\n        }\n        tspan.textContent = row;\n        svgLabel.appendChild(tspan);\n      }\n      return svgLabel;\n    }).attr(\"x\", 10).attr(\"y\", function(d, i) {\n      if (i > 0) {\n        for (let j = 0; j < i; j++) {\n          prevGap += numOccurrences[i - 1][1];\n          return d[1] * theGap / 2 + prevGap * theGap + theTopPad;\n        }\n      } else {\n        return d[1] * theGap / 2 + theTopPad;\n      }\n    }).attr(\"font-size\", conf.sectionFontSize).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d[0] === category) {\n          return \"sectionTitle sectionTitle\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"sectionTitle\";\n    });\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(vertLabels, \"vertLabels\");\n  function drawToday(theSidePad, theTopPad, w2, h2) {\n    const todayMarker2 = diagObj.db.getTodayMarker();\n    if (todayMarker2 === \"off\") {\n      return;\n    }\n    const todayG = svg.append(\"g\").attr(\"class\", \"today\");\n    const today = /* @__PURE__ */ new Date();\n    const todayLine = todayG.append(\"line\");\n    todayLine.attr(\"x1\", timeScale(today) + theSidePad).attr(\"x2\", timeScale(today) + theSidePad).attr(\"y1\", conf.titleTopMargin).attr(\"y2\", h2 - conf.titleTopMargin).attr(\"class\", \"today\");\n    if (todayMarker2 !== \"\") {\n      todayLine.attr(\"style\", todayMarker2.replace(/,/g, \";\"));\n    }\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawToday, \"drawToday\");\n  function checkUnique(arr) {\n    const hash = {};\n    const result = [];\n    for (let i = 0, l = arr.length; i < l; ++i) {\n      if (!Object.prototype.hasOwnProperty.call(hash, arr[i])) {\n        hash[arr[i]] = true;\n        result.push(arr[i]);\n      }\n    }\n    return result;\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(checkUnique, \"checkUnique\");\n}, \"draw\");\nvar ganttRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/gantt/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((options) => `\n  .mermaid-main-font {\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n\n  .exclude-range {\n    fill: ${options.excludeBkgColor};\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ${options.sectionBkgColor};\n  }\n\n  .section2 {\n    fill: ${options.sectionBkgColor2};\n  }\n\n  .section1,\n  .section3 {\n    fill: ${options.altSectionBkgColor};\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle1 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle2 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle3 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ${options.gridColor};\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ${options.fontFamily};\n    fill: ${options.textColor};\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ${options.todayLineColor};\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n\n  .taskTextOutsideRight {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: start;\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n\n  .taskTextOutsideLeft {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ${options.taskTextColor};\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ${options.taskBkgColor};\n    stroke: ${options.taskBorderColor};\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ${options.activeTaskBkgColor};\n    stroke: ${options.activeTaskBorderColor};\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ${options.doneTaskBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.critBkgColor};\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.activeTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.titleColor || options.textColor};\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/gantt/ganttDiagram.ts\nvar diagram = {\n  parser: gantt_default,\n  db: ganttDb_default,\n  renderer: ganttRenderer_default,\n  styles: styles_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-NTVNEXSI.mjs\n"));

/***/ })

}]);