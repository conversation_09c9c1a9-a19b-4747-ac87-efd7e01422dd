"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_mermaid_dist_chunks_mermaid_core_flowDiagram-7ASYPVHJ_mjs"],{

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"getDiagramElement\": function() { return /* binding */ getDiagramElement; },\n/* harmony export */   \"setupViewPortForSVG\": function() { return /* binding */ setupViewPortForSVG; }\n/* harmony export */ });\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3 */ \"./node_modules/d3/src/index.js\");\n\n\n// src/rendering-util/insertElementsForSize.js\n\nvar getDiagramElement = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.configureSvgSize)(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbWVybWFpZC9kaXN0L2NodW5rcy9tZXJtYWlkLmNvcmUvY2h1bmstNUhSQlJJSk0ubWpzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFJOEI7O0FBRTlCO0FBQzRCO0FBQzVCLHdDQUF3QywyREFBTTtBQUM5QztBQUNBO0FBQ0EscUJBQXFCLDBDQUFNO0FBQzNCO0FBQ0EsNkNBQTZDLDBDQUFNLG1EQUFtRCwwQ0FBTTtBQUM1RyxrQ0FBa0MsR0FBRztBQUNyQztBQUNBLENBQUM7O0FBRUQ7QUFDQSwwQ0FBMEMsMkRBQU07QUFDaEQ7QUFDQSxVQUFVLHNCQUFzQjtBQUNoQyxFQUFFLHFFQUFnQjtBQUNsQjtBQUNBO0FBQ0EsRUFBRSwwREFBUyx3QkFBd0IsU0FBUyxnQkFBZ0IsUUFBUTtBQUNwRSxDQUFDO0FBQ0QscURBQXFELDJEQUFNO0FBQzNELDRDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsb0NBQW9DLDJEQUFNO0FBQzFDLFlBQVksYUFBYSxFQUFFLGFBQWEsRUFBRSxPQUFPLEVBQUUsT0FBTztBQUMxRCxDQUFDOztBQUtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9jaHVuay01SFJCUklKTS5tanM/ODc2MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBfX25hbWUsXG4gIGNvbmZpZ3VyZVN2Z1NpemUsXG4gIGxvZ1xufSBmcm9tIFwiLi9jaHVuay02REJGRkhJUC5tanNcIjtcblxuLy8gc3JjL3JlbmRlcmluZy11dGlsL2luc2VydEVsZW1lbnRzRm9yU2l6ZS5qc1xuaW1wb3J0IHsgc2VsZWN0IH0gZnJvbSBcImQzXCI7XG52YXIgZ2V0RGlhZ3JhbUVsZW1lbnQgPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKChpZCwgc2VjdXJpdHlMZXZlbCkgPT4ge1xuICBsZXQgc2FuZGJveEVsZW1lbnQ7XG4gIGlmIChzZWN1cml0eUxldmVsID09PSBcInNhbmRib3hcIikge1xuICAgIHNhbmRib3hFbGVtZW50ID0gc2VsZWN0KFwiI2lcIiArIGlkKTtcbiAgfVxuICBjb25zdCByb290ID0gc2VjdXJpdHlMZXZlbCA9PT0gXCJzYW5kYm94XCIgPyBzZWxlY3Qoc2FuZGJveEVsZW1lbnQubm9kZXMoKVswXS5jb250ZW50RG9jdW1lbnQuYm9keSkgOiBzZWxlY3QoXCJib2R5XCIpO1xuICBjb25zdCBzdmcgPSByb290LnNlbGVjdChgW2lkPVwiJHtpZH1cIl1gKTtcbiAgcmV0dXJuIHN2Zztcbn0sIFwiZ2V0RGlhZ3JhbUVsZW1lbnRcIik7XG5cbi8vIHNyYy9yZW5kZXJpbmctdXRpbC9zZXR1cFZpZXdQb3J0Rm9yU1ZHLnRzXG52YXIgc2V0dXBWaWV3UG9ydEZvclNWRyA9IC8qIEBfX1BVUkVfXyAqLyBfX25hbWUoKHN2ZywgcGFkZGluZywgY3NzRGlhZ3JhbSwgdXNlTWF4V2lkdGgpID0+IHtcbiAgc3ZnLmF0dHIoXCJjbGFzc1wiLCBjc3NEaWFncmFtKTtcbiAgY29uc3QgeyB3aWR0aCwgaGVpZ2h0LCB4LCB5IH0gPSBjYWxjdWxhdGVEaW1lbnNpb25zV2l0aFBhZGRpbmcoc3ZnLCBwYWRkaW5nKTtcbiAgY29uZmlndXJlU3ZnU2l6ZShzdmcsIGhlaWdodCwgd2lkdGgsIHVzZU1heFdpZHRoKTtcbiAgY29uc3Qgdmlld0JveCA9IGNyZWF0ZVZpZXdCb3goeCwgeSwgd2lkdGgsIGhlaWdodCwgcGFkZGluZyk7XG4gIHN2Zy5hdHRyKFwidmlld0JveFwiLCB2aWV3Qm94KTtcbiAgbG9nLmRlYnVnKGB2aWV3Qm94IGNvbmZpZ3VyZWQ6ICR7dmlld0JveH0gd2l0aCBwYWRkaW5nOiAke3BhZGRpbmd9YCk7XG59LCBcInNldHVwVmlld1BvcnRGb3JTVkdcIik7XG52YXIgY2FsY3VsYXRlRGltZW5zaW9uc1dpdGhQYWRkaW5nID0gLyogQF9fUFVSRV9fICovIF9fbmFtZSgoc3ZnLCBwYWRkaW5nKSA9PiB7XG4gIGNvbnN0IGJvdW5kcyA9IHN2Zy5ub2RlKCk/LmdldEJCb3goKSB8fCB7IHdpZHRoOiAwLCBoZWlnaHQ6IDAsIHg6IDAsIHk6IDAgfTtcbiAgcmV0dXJuIHtcbiAgICB3aWR0aDogYm91bmRzLndpZHRoICsgcGFkZGluZyAqIDIsXG4gICAgaGVpZ2h0OiBib3VuZHMuaGVpZ2h0ICsgcGFkZGluZyAqIDIsXG4gICAgeDogYm91bmRzLngsXG4gICAgeTogYm91bmRzLnlcbiAgfTtcbn0sIFwiY2FsY3VsYXRlRGltZW5zaW9uc1dpdGhQYWRkaW5nXCIpO1xudmFyIGNyZWF0ZVZpZXdCb3ggPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKCh4LCB5LCB3aWR0aCwgaGVpZ2h0LCBwYWRkaW5nKSA9PiB7XG4gIHJldHVybiBgJHt4IC0gcGFkZGluZ30gJHt5IC0gcGFkZGluZ30gJHt3aWR0aH0gJHtoZWlnaHR9YDtcbn0sIFwiY3JlYXRlVmlld0JveFwiKTtcblxuZXhwb3J0IHtcbiAgZ2V0RGlhZ3JhbUVsZW1lbnQsXG4gIHNldHVwVmlld1BvcnRGb3JTVkdcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs\n"));

/***/ }),

/***/ "./node_modules/mermaid/dist/chunks/mermaid.core/flowDiagram-7ASYPVHJ.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/flowDiagram-7ASYPVHJ.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"diagram\": function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_5HRBRIJM_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-5HRBRIJM.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-5HRBRIJM.mjs\");\n/* harmony import */ var _chunk_S3SWNSAA_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-S3SWNSAA.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-S3SWNSAA.mjs\");\n/* harmony import */ var _chunk_BO7VGL7K_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-BO7VGL7K.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-BO7VGL7K.mjs\");\n/* harmony import */ var _chunk_66SQ7PYY_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-66SQ7PYY.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-66SQ7PYY.mjs\");\n/* harmony import */ var _chunk_7NZE2EM7_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-7NZE2EM7.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7NZE2EM7.mjs\");\n/* harmony import */ var _chunk_OPO4IU42_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-OPO4IU42.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-OPO4IU42.mjs\");\n/* harmony import */ var _chunk_3JNJP5BE_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-3JNJP5BE.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3JNJP5BE.mjs\");\n/* harmony import */ var _chunk_3X56UNUX_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-3X56UNUX.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3X56UNUX.mjs\");\n/* harmony import */ var _chunk_6JOS74DS_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-6JOS74DS.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6JOS74DS.mjs\");\n/* harmony import */ var _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./chunk-7DKRZKHE.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7DKRZKHE.mjs\");\n/* harmony import */ var _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./chunk-6DBFFHIP.mjs */ \"./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6DBFFHIP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! d3 */ \"./node_modules/d3/src/index.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! khroma */ \"./node_modules/khroma/dist/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n// src/diagrams/flowchart/flowDb.ts\n\nvar MERMAID_DOM_ID_PREFIX = \"flowchart-\";\nvar vertexCounter = 0;\nvar config = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getConfig2)();\nvar vertices = /* @__PURE__ */ new Map();\nvar edges = [];\nvar classes = /* @__PURE__ */ new Map();\nvar subGraphs = [];\nvar subGraphLookup = /* @__PURE__ */ new Map();\nvar tooltips = /* @__PURE__ */ new Map();\nvar subCount = 0;\nvar firstGraphFlag = true;\nvar direction;\nvar version;\nvar funs = [];\nvar sanitizeText = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((txt) => _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.common_default.sanitizeText(txt, config), \"sanitizeText\");\nvar lookUpDomId = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(id) {\n  for (const vertex of vertices.values()) {\n    if (vertex.id === id) {\n      return vertex.domId;\n    }\n  }\n  return id;\n}, \"lookUpDomId\");\nvar addVertex = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(id, textObj, type, style, classes2, dir, props = {}, shapeData) {\n  if (!id || id.trim().length === 0) {\n    return;\n  }\n  let txt;\n  let vertex = vertices.get(id);\n  if (vertex === void 0) {\n    vertex = {\n      id,\n      labelType: \"text\",\n      domId: MERMAID_DOM_ID_PREFIX + id + \"-\" + vertexCounter,\n      styles: [],\n      classes: []\n    };\n    vertices.set(id, vertex);\n  }\n  vertexCounter++;\n  if (textObj !== void 0) {\n    config = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getConfig2)();\n    txt = sanitizeText(textObj.text.trim());\n    vertex.labelType = textObj.type;\n    if (txt.startsWith('\"') && txt.endsWith('\"')) {\n      txt = txt.substring(1, txt.length - 1);\n    }\n    vertex.text = txt;\n  } else {\n    if (vertex.text === void 0) {\n      vertex.text = id;\n    }\n  }\n  if (type !== void 0) {\n    vertex.type = type;\n  }\n  if (style !== void 0 && style !== null) {\n    style.forEach(function(s) {\n      vertex.styles.push(s);\n    });\n  }\n  if (classes2 !== void 0 && classes2 !== null) {\n    classes2.forEach(function(s) {\n      vertex.classes.push(s);\n    });\n  }\n  if (dir !== void 0) {\n    vertex.dir = dir;\n  }\n  if (vertex.props === void 0) {\n    vertex.props = props;\n  } else if (props !== void 0) {\n    Object.assign(vertex.props, props);\n  }\n  if (shapeData !== void 0) {\n    let yamlData;\n    if (!shapeData.includes(\"\\n\")) {\n      yamlData = \"{\\n\" + shapeData + \"\\n}\";\n    } else {\n      yamlData = shapeData + \"\\n\";\n    }\n    const doc = (0,_chunk_S3SWNSAA_mjs__WEBPACK_IMPORTED_MODULE_1__.load)(yamlData, { schema: _chunk_S3SWNSAA_mjs__WEBPACK_IMPORTED_MODULE_1__.JSON_SCHEMA });\n    if (doc.shape) {\n      if (doc.shape !== doc.shape.toLowerCase() || doc.shape.includes(\"_\")) {\n        throw new Error(`No such shape: ${doc.shape}. Shape names should be lowercase.`);\n      } else if (!(0,_chunk_7NZE2EM7_mjs__WEBPACK_IMPORTED_MODULE_4__.isValidShape)(doc.shape)) {\n        throw new Error(`No such shape: ${doc.shape}.`);\n      }\n      vertex.type = doc?.shape;\n    }\n    if (doc?.label) {\n      vertex.text = doc?.label;\n    }\n    if (doc?.icon) {\n      vertex.icon = doc?.icon;\n      if (!doc.label?.trim() && vertex.text === id) {\n        vertex.text = \"\";\n      }\n    }\n    if (doc?.form) {\n      vertex.form = doc?.form;\n    }\n    if (doc?.pos) {\n      vertex.pos = doc?.pos;\n    }\n    if (doc?.img) {\n      vertex.img = doc?.img;\n      if (!doc.label?.trim() && vertex.text === id) {\n        vertex.text = \"\";\n      }\n    }\n    if (doc?.constraint) {\n      vertex.constraint = doc.constraint;\n    }\n    if (doc.w) {\n      vertex.assetWidth = Number(doc.w);\n    }\n    if (doc.h) {\n      vertex.assetHeight = Number(doc.h);\n    }\n  }\n}, \"addVertex\");\nvar addSingleLink = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(_start, _end, type) {\n  const start = _start;\n  const end = _end;\n  const edge = { start, end, type: void 0, text: \"\", labelType: \"text\" };\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.info(\"abc78 Got edge...\", edge);\n  const linkTextObj = type.text;\n  if (linkTextObj !== void 0) {\n    edge.text = sanitizeText(linkTextObj.text.trim());\n    if (edge.text.startsWith('\"') && edge.text.endsWith('\"')) {\n      edge.text = edge.text.substring(1, edge.text.length - 1);\n    }\n    edge.labelType = linkTextObj.type;\n  }\n  if (type !== void 0) {\n    edge.type = type.type;\n    edge.stroke = type.stroke;\n    edge.length = type.length > 10 ? 10 : type.length;\n  }\n  if (edges.length < (config.maxEdges ?? 500)) {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.info(\"Pushing edge...\");\n    edges.push(edge);\n  } else {\n    throw new Error(\n      `Edge limit exceeded. ${edges.length} edges found, but the limit is ${config.maxEdges}.\n\nInitialize mermaid with maxEdges set to a higher number to allow more edges.\nYou cannot set this config via configuration inside the diagram as it is a secure config.\nYou have to call mermaid.initialize.`\n    );\n  }\n}, \"addSingleLink\");\nvar addLink = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(_start, _end, type) {\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.info(\"addLink\", _start, _end, type);\n  for (const start of _start) {\n    for (const end of _end) {\n      addSingleLink(start, end, type);\n    }\n  }\n}, \"addLink\");\nvar updateLinkInterpolate = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(positions, interpolate) {\n  positions.forEach(function(pos) {\n    if (pos === \"default\") {\n      edges.defaultInterpolate = interpolate;\n    } else {\n      edges[pos].interpolate = interpolate;\n    }\n  });\n}, \"updateLinkInterpolate\");\nvar updateLink = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(positions, style) {\n  positions.forEach(function(pos) {\n    if (typeof pos === \"number\" && pos >= edges.length) {\n      throw new Error(\n        `The index ${pos} for linkStyle is out of bounds. Valid indices for linkStyle are between 0 and ${edges.length - 1}. (Help: Ensure that the index is within the range of existing edges.)`\n      );\n    }\n    if (pos === \"default\") {\n      edges.defaultStyle = style;\n    } else {\n      edges[pos].style = style;\n      if ((edges[pos]?.style?.length ?? 0) > 0 && !edges[pos]?.style?.some((s) => s?.startsWith(\"fill\"))) {\n        edges[pos]?.style?.push(\"fill:none\");\n      }\n    }\n  });\n}, \"updateLink\");\nvar addClass = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(ids, style) {\n  ids.split(\",\").forEach(function(id) {\n    let classNode = classes.get(id);\n    if (classNode === void 0) {\n      classNode = { id, styles: [], textStyles: [] };\n      classes.set(id, classNode);\n    }\n    if (style !== void 0 && style !== null) {\n      style.forEach(function(s) {\n        if (/color/.exec(s)) {\n          const newStyle = s.replace(\"fill\", \"bgFill\");\n          classNode.textStyles.push(newStyle);\n        }\n        classNode.styles.push(s);\n      });\n    }\n  });\n}, \"addClass\");\nvar setDirection = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(dir) {\n  direction = dir;\n  if (/.*</.exec(direction)) {\n    direction = \"RL\";\n  }\n  if (/.*\\^/.exec(direction)) {\n    direction = \"BT\";\n  }\n  if (/.*>/.exec(direction)) {\n    direction = \"LR\";\n  }\n  if (/.*v/.exec(direction)) {\n    direction = \"TB\";\n  }\n  if (direction === \"TD\") {\n    direction = \"TB\";\n  }\n}, \"setDirection\");\nvar setClass = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(ids, className) {\n  for (const id of ids.split(\",\")) {\n    const vertex = vertices.get(id);\n    if (vertex) {\n      vertex.classes.push(className);\n    }\n    const subGraph = subGraphLookup.get(id);\n    if (subGraph) {\n      subGraph.classes.push(className);\n    }\n  }\n}, \"setClass\");\nvar setTooltip = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(ids, tooltip) {\n  if (tooltip === void 0) {\n    return;\n  }\n  tooltip = sanitizeText(tooltip);\n  for (const id of ids.split(\",\")) {\n    tooltips.set(version === \"gen-1\" ? lookUpDomId(id) : id, tooltip);\n  }\n}, \"setTooltip\");\nvar setClickFun = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(id, functionName, functionArgs) {\n  const domId = lookUpDomId(id);\n  if ((0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getConfig2)().securityLevel !== \"loose\") {\n    return;\n  }\n  if (functionName === void 0) {\n    return;\n  }\n  let argList = [];\n  if (typeof functionArgs === \"string\") {\n    argList = functionArgs.split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/);\n    for (let i = 0; i < argList.length; i++) {\n      let item = argList[i].trim();\n      if (item.startsWith('\"') && item.endsWith('\"')) {\n        item = item.substr(1, item.length - 2);\n      }\n      argList[i] = item;\n    }\n  }\n  if (argList.length === 0) {\n    argList.push(id);\n  }\n  const vertex = vertices.get(id);\n  if (vertex) {\n    vertex.haveCallback = true;\n    funs.push(function() {\n      const elem = document.querySelector(`[id=\"${domId}\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\n          \"click\",\n          function() {\n            _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_9__.utils_default.runFunc(functionName, ...argList);\n          },\n          false\n        );\n      }\n    });\n  }\n}, \"setClickFun\");\nvar setLink = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(ids, linkStr, target) {\n  ids.split(\",\").forEach(function(id) {\n    const vertex = vertices.get(id);\n    if (vertex !== void 0) {\n      vertex.link = _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_9__.utils_default.formatUrl(linkStr, config);\n      vertex.linkTarget = target;\n    }\n  });\n  setClass(ids, \"clickable\");\n}, \"setLink\");\nvar getTooltip = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(id) {\n  return tooltips.get(id);\n}, \"getTooltip\");\nvar setClickEvent = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(ids, functionName, functionArgs) {\n  ids.split(\",\").forEach(function(id) {\n    setClickFun(id, functionName, functionArgs);\n  });\n  setClass(ids, \"clickable\");\n}, \"setClickEvent\");\nvar bindFunctions = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(element) {\n  funs.forEach(function(fun) {\n    fun(element);\n  });\n}, \"bindFunctions\");\nvar getDirection = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n  return direction.trim();\n}, \"getDirection\");\nvar getVertices = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n  return vertices;\n}, \"getVertices\");\nvar getEdges = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n  return edges;\n}, \"getEdges\");\nvar getClasses = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n  return classes;\n}, \"getClasses\");\nvar setupToolTips = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(element) {\n  let tooltipElem = (0,d3__WEBPACK_IMPORTED_MODULE_11__.select)(\".mermaidTooltip\");\n  if ((tooltipElem._groups || tooltipElem)[0][0] === null) {\n    tooltipElem = (0,d3__WEBPACK_IMPORTED_MODULE_11__.select)(\"body\").append(\"div\").attr(\"class\", \"mermaidTooltip\").style(\"opacity\", 0);\n  }\n  const svg = (0,d3__WEBPACK_IMPORTED_MODULE_11__.select)(element).select(\"svg\");\n  const nodes = svg.selectAll(\"g.node\");\n  nodes.on(\"mouseover\", function() {\n    const el = (0,d3__WEBPACK_IMPORTED_MODULE_11__.select)(this);\n    const title = el.attr(\"title\");\n    if (title === null) {\n      return;\n    }\n    const rect = this?.getBoundingClientRect();\n    tooltipElem.transition().duration(200).style(\"opacity\", \".9\");\n    tooltipElem.text(el.attr(\"title\")).style(\"left\", window.scrollX + rect.left + (rect.right - rect.left) / 2 + \"px\").style(\"top\", window.scrollY + rect.bottom + \"px\");\n    tooltipElem.html(tooltipElem.html().replace(/&lt;br\\/&gt;/g, \"<br/>\"));\n    el.classed(\"hover\", true);\n  }).on(\"mouseout\", function() {\n    tooltipElem.transition().duration(500).style(\"opacity\", 0);\n    const el = (0,d3__WEBPACK_IMPORTED_MODULE_11__.select)(this);\n    el.classed(\"hover\", false);\n  });\n}, \"setupToolTips\");\nfuns.push(setupToolTips);\nvar clear2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(ver = \"gen-1\") {\n  vertices = /* @__PURE__ */ new Map();\n  classes = /* @__PURE__ */ new Map();\n  edges = [];\n  funs = [setupToolTips];\n  subGraphs = [];\n  subGraphLookup = /* @__PURE__ */ new Map();\n  subCount = 0;\n  tooltips = /* @__PURE__ */ new Map();\n  firstGraphFlag = true;\n  version = ver;\n  config = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getConfig2)();\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.clear)();\n}, \"clear\");\nvar setGen = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((ver) => {\n  version = ver || \"gen-2\";\n}, \"setGen\");\nvar defaultStyle = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n  return \"fill:#ffa;stroke: #f66; stroke-width: 3px; stroke-dasharray: 5, 5;fill:#ffa;stroke: #666;\";\n}, \"defaultStyle\");\nvar addSubGraph = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(_id, list, _title) {\n  let id = _id.text.trim();\n  let title = _title.text;\n  if (_id === _title && /\\s/.exec(_title.text)) {\n    id = void 0;\n  }\n  function uniq(a) {\n    const prims = { boolean: {}, number: {}, string: {} };\n    const objs = [];\n    let dir2;\n    const nodeList2 = a.filter(function(item) {\n      const type = typeof item;\n      if (item.stmt && item.stmt === \"dir\") {\n        dir2 = item.value;\n        return false;\n      }\n      if (item.trim() === \"\") {\n        return false;\n      }\n      if (type in prims) {\n        return prims[type].hasOwnProperty(item) ? false : prims[type][item] = true;\n      } else {\n        return objs.includes(item) ? false : objs.push(item);\n      }\n    });\n    return { nodeList: nodeList2, dir: dir2 };\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(uniq, \"uniq\");\n  const { nodeList, dir } = uniq(list.flat());\n  if (version === \"gen-1\") {\n    for (let i = 0; i < nodeList.length; i++) {\n      nodeList[i] = lookUpDomId(nodeList[i]);\n    }\n  }\n  id = id ?? \"subGraph\" + subCount;\n  title = title || \"\";\n  title = sanitizeText(title);\n  subCount = subCount + 1;\n  const subGraph = {\n    id,\n    nodes: nodeList,\n    title: title.trim(),\n    classes: [],\n    dir,\n    labelType: _title.type\n  };\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.info(\"Adding\", subGraph.id, subGraph.nodes, subGraph.dir);\n  subGraph.nodes = makeUniq(subGraph, subGraphs).nodes;\n  subGraphs.push(subGraph);\n  subGraphLookup.set(id, subGraph);\n  return id;\n}, \"addSubGraph\");\nvar getPosForId = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(id) {\n  for (const [i, subGraph] of subGraphs.entries()) {\n    if (subGraph.id === id) {\n      return i;\n    }\n  }\n  return -1;\n}, \"getPosForId\");\nvar secCount = -1;\nvar posCrossRef = [];\nvar indexNodes2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(id, pos) {\n  const nodes = subGraphs[pos].nodes;\n  secCount = secCount + 1;\n  if (secCount > 2e3) {\n    return {\n      result: false,\n      count: 0\n    };\n  }\n  posCrossRef[secCount] = pos;\n  if (subGraphs[pos].id === id) {\n    return {\n      result: true,\n      count: 0\n    };\n  }\n  let count = 0;\n  let posCount = 1;\n  while (count < nodes.length) {\n    const childPos = getPosForId(nodes[count]);\n    if (childPos >= 0) {\n      const res = indexNodes2(id, childPos);\n      if (res.result) {\n        return {\n          result: true,\n          count: posCount + res.count\n        };\n      } else {\n        posCount = posCount + res.count;\n      }\n    }\n    count = count + 1;\n  }\n  return {\n    result: false,\n    count: posCount\n  };\n}, \"indexNodes2\");\nvar getDepthFirstPos = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(pos) {\n  return posCrossRef[pos];\n}, \"getDepthFirstPos\");\nvar indexNodes = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n  secCount = -1;\n  if (subGraphs.length > 0) {\n    indexNodes2(\"none\", subGraphs.length - 1);\n  }\n}, \"indexNodes\");\nvar getSubGraphs = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n  return subGraphs;\n}, \"getSubGraphs\");\nvar firstGraph = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(() => {\n  if (firstGraphFlag) {\n    firstGraphFlag = false;\n    return true;\n  }\n  return false;\n}, \"firstGraph\");\nvar destructStartLink = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((_str) => {\n  let str = _str.trim();\n  let type = \"arrow_open\";\n  switch (str[0]) {\n    case \"<\":\n      type = \"arrow_point\";\n      str = str.slice(1);\n      break;\n    case \"x\":\n      type = \"arrow_cross\";\n      str = str.slice(1);\n      break;\n    case \"o\":\n      type = \"arrow_circle\";\n      str = str.slice(1);\n      break;\n  }\n  let stroke = \"normal\";\n  if (str.includes(\"=\")) {\n    stroke = \"thick\";\n  }\n  if (str.includes(\".\")) {\n    stroke = \"dotted\";\n  }\n  return { type, stroke };\n}, \"destructStartLink\");\nvar countChar = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((char, str) => {\n  const length = str.length;\n  let count = 0;\n  for (let i = 0; i < length; ++i) {\n    if (str[i] === char) {\n      ++count;\n    }\n  }\n  return count;\n}, \"countChar\");\nvar destructEndLink = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((_str) => {\n  const str = _str.trim();\n  let line = str.slice(0, -1);\n  let type = \"arrow_open\";\n  switch (str.slice(-1)) {\n    case \"x\":\n      type = \"arrow_cross\";\n      if (str.startsWith(\"x\")) {\n        type = \"double_\" + type;\n        line = line.slice(1);\n      }\n      break;\n    case \">\":\n      type = \"arrow_point\";\n      if (str.startsWith(\"<\")) {\n        type = \"double_\" + type;\n        line = line.slice(1);\n      }\n      break;\n    case \"o\":\n      type = \"arrow_circle\";\n      if (str.startsWith(\"o\")) {\n        type = \"double_\" + type;\n        line = line.slice(1);\n      }\n      break;\n  }\n  let stroke = \"normal\";\n  let length = line.length - 1;\n  if (line.startsWith(\"=\")) {\n    stroke = \"thick\";\n  }\n  if (line.startsWith(\"~\")) {\n    stroke = \"invisible\";\n  }\n  const dots = countChar(\".\", line);\n  if (dots) {\n    stroke = \"dotted\";\n    length = dots;\n  }\n  return { type, stroke, length };\n}, \"destructEndLink\");\nvar destructLink = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((_str, _startStr) => {\n  const info = destructEndLink(_str);\n  let startInfo;\n  if (_startStr) {\n    startInfo = destructStartLink(_startStr);\n    if (startInfo.stroke !== info.stroke) {\n      return { type: \"INVALID\", stroke: \"INVALID\" };\n    }\n    if (startInfo.type === \"arrow_open\") {\n      startInfo.type = info.type;\n    } else {\n      if (startInfo.type !== info.type) {\n        return { type: \"INVALID\", stroke: \"INVALID\" };\n      }\n      startInfo.type = \"double_\" + startInfo.type;\n    }\n    if (startInfo.type === \"double_arrow\") {\n      startInfo.type = \"double_arrow_point\";\n    }\n    startInfo.length = info.length;\n    return startInfo;\n  }\n  return info;\n}, \"destructLink\");\nvar exists = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((allSgs, _id) => {\n  for (const sg of allSgs) {\n    if (sg.nodes.includes(_id)) {\n      return true;\n    }\n  }\n  return false;\n}, \"exists\");\nvar makeUniq = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((sg, allSubgraphs) => {\n  const res = [];\n  sg.nodes.forEach((_id, pos) => {\n    if (!exists(allSubgraphs, _id)) {\n      res.push(sg.nodes[pos]);\n    }\n  });\n  return { nodes: res };\n}, \"makeUniq\");\nvar lex = {\n  firstGraph\n};\nvar getTypeFromVertex = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((vertex) => {\n  if (vertex.img) {\n    return \"imageSquare\";\n  }\n  if (vertex.icon) {\n    if (vertex.form === \"circle\") {\n      return \"iconCircle\";\n    }\n    if (vertex.form === \"square\") {\n      return \"iconSquare\";\n    }\n    if (vertex.form === \"rounded\") {\n      return \"iconRounded\";\n    }\n    return \"icon\";\n  }\n  switch (vertex.type) {\n    case \"square\":\n    case void 0:\n      return \"squareRect\";\n    case \"round\":\n      return \"roundedRect\";\n    case \"ellipse\":\n      return \"ellipse\";\n    default:\n      return vertex.type;\n  }\n}, \"getTypeFromVertex\");\nvar findNode = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((nodes, id) => nodes.find((node) => node.id === id), \"findNode\");\nvar destructEdgeType = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((type) => {\n  let arrowTypeStart = \"none\";\n  let arrowTypeEnd = \"arrow_point\";\n  switch (type) {\n    case \"arrow_point\":\n    case \"arrow_circle\":\n    case \"arrow_cross\":\n      arrowTypeEnd = type;\n      break;\n    case \"double_arrow_point\":\n    case \"double_arrow_circle\":\n    case \"double_arrow_cross\":\n      arrowTypeStart = type.replace(\"double_\", \"\");\n      arrowTypeEnd = arrowTypeStart;\n      break;\n  }\n  return { arrowTypeStart, arrowTypeEnd };\n}, \"destructEdgeType\");\nvar addNodeFromVertex = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((vertex, nodes, parentDB, subGraphDB, config2, look) => {\n  const parentId = parentDB.get(vertex.id);\n  const isGroup = subGraphDB.get(vertex.id) ?? false;\n  const node = findNode(nodes, vertex.id);\n  if (node) {\n    node.cssStyles = vertex.styles;\n    node.cssCompiledStyles = getCompiledStyles(vertex.classes);\n    node.cssClasses = vertex.classes.join(\" \");\n  } else {\n    const baseNode = {\n      id: vertex.id,\n      label: vertex.text,\n      labelStyle: \"\",\n      parentId,\n      padding: config2.flowchart?.padding || 8,\n      cssStyles: vertex.styles,\n      cssCompiledStyles: getCompiledStyles([\"default\", \"node\", ...vertex.classes]),\n      cssClasses: \"default \" + vertex.classes.join(\" \"),\n      dir: vertex.dir,\n      domId: vertex.domId,\n      look,\n      link: vertex.link,\n      linkTarget: vertex.linkTarget,\n      tooltip: getTooltip(vertex.id),\n      icon: vertex.icon,\n      pos: vertex.pos,\n      img: vertex.img,\n      assetWidth: vertex.assetWidth,\n      assetHeight: vertex.assetHeight,\n      constraint: vertex.constraint\n    };\n    if (isGroup) {\n      nodes.push({\n        ...baseNode,\n        isGroup: true,\n        shape: \"rect\"\n      });\n    } else {\n      nodes.push({\n        ...baseNode,\n        isGroup: false,\n        shape: getTypeFromVertex(vertex)\n      });\n    }\n  }\n}, \"addNodeFromVertex\");\nfunction getCompiledStyles(classDefs) {\n  let compiledStyles = [];\n  for (const customClass of classDefs) {\n    const cssClass = classes.get(customClass);\n    if (cssClass?.styles) {\n      compiledStyles = [...compiledStyles, ...cssClass.styles ?? []].map((s) => s.trim());\n    }\n    if (cssClass?.textStyles) {\n      compiledStyles = [...compiledStyles, ...cssClass.textStyles ?? []].map((s) => s.trim());\n    }\n  }\n  return compiledStyles;\n}\n(0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(getCompiledStyles, \"getCompiledStyles\");\nvar getData = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(() => {\n  const config2 = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getConfig2)();\n  const nodes = [];\n  const edges2 = [];\n  const subGraphs2 = getSubGraphs();\n  const parentDB = /* @__PURE__ */ new Map();\n  const subGraphDB = /* @__PURE__ */ new Map();\n  for (let i = subGraphs2.length - 1; i >= 0; i--) {\n    const subGraph = subGraphs2[i];\n    if (subGraph.nodes.length > 0) {\n      subGraphDB.set(subGraph.id, true);\n    }\n    for (const id of subGraph.nodes) {\n      parentDB.set(id, subGraph.id);\n    }\n  }\n  for (let i = subGraphs2.length - 1; i >= 0; i--) {\n    const subGraph = subGraphs2[i];\n    nodes.push({\n      id: subGraph.id,\n      label: subGraph.title,\n      labelStyle: \"\",\n      parentId: parentDB.get(subGraph.id),\n      padding: 8,\n      cssCompiledStyles: getCompiledStyles(subGraph.classes),\n      cssClasses: subGraph.classes.join(\" \"),\n      shape: \"rect\",\n      dir: subGraph.dir,\n      isGroup: true,\n      look: config2.look\n    });\n  }\n  const n = getVertices();\n  n.forEach((vertex) => {\n    addNodeFromVertex(vertex, nodes, parentDB, subGraphDB, config2, config2.look || \"classic\");\n  });\n  const e = getEdges();\n  e.forEach((rawEdge, index) => {\n    const { arrowTypeStart, arrowTypeEnd } = destructEdgeType(rawEdge.type);\n    const styles = [...e.defaultStyle ?? []];\n    if (rawEdge.style) {\n      styles.push(...rawEdge.style);\n    }\n    const edge = {\n      id: (0,_chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_9__.getEdgeId)(rawEdge.start, rawEdge.end, { counter: index, prefix: \"L\" }),\n      start: rawEdge.start,\n      end: rawEdge.end,\n      type: rawEdge.type ?? \"normal\",\n      label: rawEdge.text,\n      labelpos: \"c\",\n      thickness: rawEdge.stroke,\n      minlen: rawEdge.length,\n      classes: rawEdge?.stroke === \"invisible\" ? \"\" : \"edge-thickness-normal edge-pattern-solid flowchart-link\",\n      arrowTypeStart: rawEdge?.stroke === \"invisible\" ? \"none\" : arrowTypeStart,\n      arrowTypeEnd: rawEdge?.stroke === \"invisible\" ? \"none\" : arrowTypeEnd,\n      arrowheadStyle: \"fill: #333\",\n      labelStyle: styles,\n      style: styles,\n      pattern: rawEdge.stroke,\n      look: config2.look\n    };\n    edges2.push(edge);\n  });\n  return { nodes, edges: edges2, other: {}, config: config2 };\n}, \"getData\");\nvar flowDb_default = {\n  defaultConfig: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(() => _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.defaultConfig2.flowchart, \"defaultConfig\"),\n  setAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.setAccTitle,\n  getAccTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getAccTitle,\n  getAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getAccDescription,\n  getData,\n  setAccDescription: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.setAccDescription,\n  addVertex,\n  lookUpDomId,\n  addLink,\n  updateLinkInterpolate,\n  updateLink,\n  addClass,\n  setDirection,\n  setClass,\n  setTooltip,\n  getTooltip,\n  setClickEvent,\n  setLink,\n  bindFunctions,\n  getDirection,\n  getVertices,\n  getEdges,\n  getClasses,\n  clear: clear2,\n  setGen,\n  defaultStyle,\n  addSubGraph,\n  getDepthFirstPos,\n  indexNodes,\n  getSubGraphs,\n  destructLink,\n  lex,\n  exists,\n  makeUniq,\n  setDiagramTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.setDiagramTitle,\n  getDiagramTitle: _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getDiagramTitle\n};\n\n// src/diagrams/flowchart/flowRenderer-v3-unified.ts\n\nvar getClasses2 = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(text, diagramObj) {\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(async function(text, id, _version, diag) {\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.info(\"REF0:\");\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.info(\"Drawing state diagram (v2)\", id);\n  const { securityLevel, flowchart: conf, layout } = (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.getConfig2)();\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_11__.select)(\"#i\" + id);\n  }\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.debug(\"Before getData: \");\n  const data4Layout = diag.db.getData();\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.debug(\"Data: \", data4Layout);\n  const svg = (0,_chunk_5HRBRIJM_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramElement)(id, securityLevel);\n  const direction2 = getDirection();\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = (0,_chunk_BO7VGL7K_mjs__WEBPACK_IMPORTED_MODULE_2__.getRegisteredLayoutAlgorithm)(layout);\n  if (data4Layout.layoutAlgorithm === \"dagre\" && layout === \"elk\") {\n    _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.warn(\n      \"flowchart-elk was moved to an external package in Mermaid v11. Please refer [release notes](https://github.com/mermaid-js/mermaid/releases/tag/v11.0.0) for more details. This diagram will be rendered using `dagre` layout as a fallback.\"\n    );\n  }\n  data4Layout.direction = direction2;\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = [\"point\", \"circle\", \"cross\"];\n  data4Layout.diagramId = id;\n  _chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.log.debug(\"REF1:\", data4Layout);\n  await (0,_chunk_BO7VGL7K_mjs__WEBPACK_IMPORTED_MODULE_2__.render)(data4Layout, svg);\n  const padding = data4Layout.config.flowchart?.diagramPadding ?? 8;\n  _chunk_7DKRZKHE_mjs__WEBPACK_IMPORTED_MODULE_9__.utils_default.insertTitle(\n    svg,\n    \"flowchartTitleText\",\n    conf?.titleTopMargin || 0,\n    diag.db.getDiagramTitle()\n  );\n  (0,_chunk_5HRBRIJM_mjs__WEBPACK_IMPORTED_MODULE_0__.setupViewPortForSVG)(svg, padding, \"flowchart\", conf?.useMaxWidth || false);\n  for (const vertex of data4Layout.nodes) {\n    const node = (0,d3__WEBPACK_IMPORTED_MODULE_11__.select)(`#${id} [id=\"${vertex.id}\"]`);\n    if (!node || !vertex.link) {\n      continue;\n    }\n    const link = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"a\");\n    link.setAttributeNS(\"http://www.w3.org/2000/svg\", \"class\", vertex.cssClasses);\n    link.setAttributeNS(\"http://www.w3.org/2000/svg\", \"rel\", \"noopener\");\n    if (securityLevel === \"sandbox\") {\n      link.setAttributeNS(\"http://www.w3.org/2000/svg\", \"target\", \"_top\");\n    } else if (vertex.linkTarget) {\n      link.setAttributeNS(\"http://www.w3.org/2000/svg\", \"target\", vertex.linkTarget);\n    }\n    const linkNode = node.insert(function() {\n      return link;\n    }, \":first-child\");\n    const shape = node.select(\".label-container\");\n    if (shape) {\n      linkNode.append(function() {\n        return shape.node();\n      });\n    }\n    const label = node.select(\".label\");\n    if (label) {\n      linkNode.append(function() {\n        return label.node();\n      });\n    }\n  }\n}, \"draw\");\nvar flowRenderer_v3_unified_default = {\n  getClasses: getClasses2,\n  draw\n};\n\n// src/diagrams/flowchart/parser/flow.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 4], $V1 = [1, 3], $V2 = [1, 5], $V3 = [1, 8, 9, 10, 11, 27, 34, 36, 38, 44, 60, 83, 84, 85, 86, 87, 88, 101, 104, 105, 108, 110, 113, 114, 115, 120, 121, 122, 123], $V4 = [2, 2], $V5 = [1, 13], $V6 = [1, 14], $V7 = [1, 15], $V8 = [1, 16], $V9 = [1, 23], $Va = [1, 25], $Vb = [1, 26], $Vc = [1, 27], $Vd = [1, 49], $Ve = [1, 48], $Vf = [1, 29], $Vg = [1, 30], $Vh = [1, 31], $Vi = [1, 32], $Vj = [1, 33], $Vk = [1, 44], $Vl = [1, 46], $Vm = [1, 42], $Vn = [1, 47], $Vo = [1, 43], $Vp = [1, 50], $Vq = [1, 45], $Vr = [1, 51], $Vs = [1, 52], $Vt = [1, 34], $Vu = [1, 35], $Vv = [1, 36], $Vw = [1, 37], $Vx = [1, 57], $Vy = [1, 8, 9, 10, 11, 27, 32, 34, 36, 38, 44, 60, 83, 84, 85, 86, 87, 88, 101, 104, 105, 108, 110, 113, 114, 115, 120, 121, 122, 123], $Vz = [1, 61], $VA = [1, 60], $VB = [1, 62], $VC = [8, 9, 11, 75, 77], $VD = [1, 77], $VE = [1, 90], $VF = [1, 95], $VG = [1, 94], $VH = [1, 91], $VI = [1, 87], $VJ = [1, 93], $VK = [1, 89], $VL = [1, 96], $VM = [1, 92], $VN = [1, 97], $VO = [1, 88], $VP = [8, 9, 10, 11, 40, 75, 77], $VQ = [8, 9, 10, 11, 40, 46, 75, 77], $VR = [8, 9, 10, 11, 29, 40, 44, 46, 48, 50, 52, 54, 56, 58, 60, 63, 65, 67, 68, 70, 75, 77, 88, 101, 104, 105, 108, 110, 113, 114, 115], $VS = [8, 9, 11, 44, 60, 75, 77, 88, 101, 104, 105, 108, 110, 113, 114, 115], $VT = [44, 60, 88, 101, 104, 105, 108, 110, 113, 114, 115], $VU = [1, 123], $VV = [1, 122], $VW = [1, 130], $VX = [1, 144], $VY = [1, 145], $VZ = [1, 146], $V_ = [1, 147], $V$ = [1, 132], $V01 = [1, 134], $V11 = [1, 138], $V21 = [1, 139], $V31 = [1, 140], $V41 = [1, 141], $V51 = [1, 142], $V61 = [1, 143], $V71 = [1, 148], $V81 = [1, 149], $V91 = [1, 128], $Va1 = [1, 129], $Vb1 = [1, 136], $Vc1 = [1, 131], $Vd1 = [1, 135], $Ve1 = [1, 133], $Vf1 = [8, 9, 10, 11, 27, 32, 34, 36, 38, 44, 60, 83, 84, 85, 86, 87, 88, 101, 104, 105, 108, 110, 113, 114, 115, 120, 121, 122, 123], $Vg1 = [1, 151], $Vh1 = [1, 153], $Vi1 = [8, 9, 11], $Vj1 = [8, 9, 10, 11, 14, 44, 60, 88, 104, 105, 108, 110, 113, 114, 115], $Vk1 = [1, 173], $Vl1 = [1, 169], $Vm1 = [1, 170], $Vn1 = [1, 174], $Vo1 = [1, 171], $Vp1 = [1, 172], $Vq1 = [77, 115, 118], $Vr1 = [8, 9, 10, 11, 12, 14, 27, 29, 32, 44, 60, 75, 83, 84, 85, 86, 87, 88, 89, 104, 108, 110, 113, 114, 115], $Vs1 = [10, 105], $Vt1 = [31, 49, 51, 53, 55, 57, 62, 64, 66, 67, 69, 71, 115, 116, 117], $Vu1 = [1, 242], $Vv1 = [1, 240], $Vw1 = [1, 244], $Vx1 = [1, 238], $Vy1 = [1, 239], $Vz1 = [1, 241], $VA1 = [1, 243], $VB1 = [1, 245], $VC1 = [1, 263], $VD1 = [8, 9, 11, 105], $VE1 = [8, 9, 10, 11, 60, 83, 104, 105, 108, 109, 110, 111];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"graphConfig\": 4, \"document\": 5, \"line\": 6, \"statement\": 7, \"SEMI\": 8, \"NEWLINE\": 9, \"SPACE\": 10, \"EOF\": 11, \"GRAPH\": 12, \"NODIR\": 13, \"DIR\": 14, \"FirstStmtSeparator\": 15, \"ending\": 16, \"endToken\": 17, \"spaceList\": 18, \"spaceListNewline\": 19, \"vertexStatement\": 20, \"separator\": 21, \"styleStatement\": 22, \"linkStyleStatement\": 23, \"classDefStatement\": 24, \"classStatement\": 25, \"clickStatement\": 26, \"subgraph\": 27, \"textNoTags\": 28, \"SQS\": 29, \"text\": 30, \"SQE\": 31, \"end\": 32, \"direction\": 33, \"acc_title\": 34, \"acc_title_value\": 35, \"acc_descr\": 36, \"acc_descr_value\": 37, \"acc_descr_multiline_value\": 38, \"shapeData\": 39, \"SHAPE_DATA\": 40, \"link\": 41, \"node\": 42, \"styledVertex\": 43, \"AMP\": 44, \"vertex\": 45, \"STYLE_SEPARATOR\": 46, \"idString\": 47, \"DOUBLECIRCLESTART\": 48, \"DOUBLECIRCLEEND\": 49, \"PS\": 50, \"PE\": 51, \"(-\": 52, \"-)\": 53, \"STADIUMSTART\": 54, \"STADIUMEND\": 55, \"SUBROUTINESTART\": 56, \"SUBROUTINEEND\": 57, \"VERTEX_WITH_PROPS_START\": 58, \"NODE_STRING[field]\": 59, \"COLON\": 60, \"NODE_STRING[value]\": 61, \"PIPE\": 62, \"CYLINDERSTART\": 63, \"CYLINDEREND\": 64, \"DIAMOND_START\": 65, \"DIAMOND_STOP\": 66, \"TAGEND\": 67, \"TRAPSTART\": 68, \"TRAPEND\": 69, \"INVTRAPSTART\": 70, \"INVTRAPEND\": 71, \"linkStatement\": 72, \"arrowText\": 73, \"TESTSTR\": 74, \"START_LINK\": 75, \"edgeText\": 76, \"LINK\": 77, \"edgeTextToken\": 78, \"STR\": 79, \"MD_STR\": 80, \"textToken\": 81, \"keywords\": 82, \"STYLE\": 83, \"LINKSTYLE\": 84, \"CLASSDEF\": 85, \"CLASS\": 86, \"CLICK\": 87, \"DOWN\": 88, \"UP\": 89, \"textNoTagsToken\": 90, \"stylesOpt\": 91, \"idString[vertex]\": 92, \"idString[class]\": 93, \"CALLBACKNAME\": 94, \"CALLBACKARGS\": 95, \"HREF\": 96, \"LINK_TARGET\": 97, \"STR[link]\": 98, \"STR[tooltip]\": 99, \"alphaNum\": 100, \"DEFAULT\": 101, \"numList\": 102, \"INTERPOLATE\": 103, \"NUM\": 104, \"COMMA\": 105, \"style\": 106, \"styleComponent\": 107, \"NODE_STRING\": 108, \"UNIT\": 109, \"BRKT\": 110, \"PCT\": 111, \"idStringToken\": 112, \"MINUS\": 113, \"MULT\": 114, \"UNICODE_TEXT\": 115, \"TEXT\": 116, \"TAGSTART\": 117, \"EDGE_TEXT\": 118, \"alphaNumToken\": 119, \"direction_tb\": 120, \"direction_bt\": 121, \"direction_rl\": 122, \"direction_lr\": 123, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 8: \"SEMI\", 9: \"NEWLINE\", 10: \"SPACE\", 11: \"EOF\", 12: \"GRAPH\", 13: \"NODIR\", 14: \"DIR\", 27: \"subgraph\", 29: \"SQS\", 31: \"SQE\", 32: \"end\", 34: \"acc_title\", 35: \"acc_title_value\", 36: \"acc_descr\", 37: \"acc_descr_value\", 38: \"acc_descr_multiline_value\", 40: \"SHAPE_DATA\", 44: \"AMP\", 46: \"STYLE_SEPARATOR\", 48: \"DOUBLECIRCLESTART\", 49: \"DOUBLECIRCLEEND\", 50: \"PS\", 51: \"PE\", 52: \"(-\", 53: \"-)\", 54: \"STADIUMSTART\", 55: \"STADIUMEND\", 56: \"SUBROUTINESTART\", 57: \"SUBROUTINEEND\", 58: \"VERTEX_WITH_PROPS_START\", 59: \"NODE_STRING[field]\", 60: \"COLON\", 61: \"NODE_STRING[value]\", 62: \"PIPE\", 63: \"CYLINDERSTART\", 64: \"CYLINDEREND\", 65: \"DIAMOND_START\", 66: \"DIAMOND_STOP\", 67: \"TAGEND\", 68: \"TRAPSTART\", 69: \"TRAPEND\", 70: \"INVTRAPSTART\", 71: \"INVTRAPEND\", 74: \"TESTSTR\", 75: \"START_LINK\", 77: \"LINK\", 79: \"STR\", 80: \"MD_STR\", 83: \"STYLE\", 84: \"LINKSTYLE\", 85: \"CLASSDEF\", 86: \"CLASS\", 87: \"CLICK\", 88: \"DOWN\", 89: \"UP\", 92: \"idString[vertex]\", 93: \"idString[class]\", 94: \"CALLBACKNAME\", 95: \"CALLBACKARGS\", 96: \"HREF\", 97: \"LINK_TARGET\", 98: \"STR[link]\", 99: \"STR[tooltip]\", 101: \"DEFAULT\", 103: \"INTERPOLATE\", 104: \"NUM\", 105: \"COMMA\", 108: \"NODE_STRING\", 109: \"UNIT\", 110: \"BRKT\", 111: \"PCT\", 113: \"MINUS\", 114: \"MULT\", 115: \"UNICODE_TEXT\", 116: \"TEXT\", 117: \"TAGSTART\", 118: \"EDGE_TEXT\", 120: \"direction_tb\", 121: \"direction_bt\", 122: \"direction_rl\", 123: \"direction_lr\" },\n    productions_: [0, [3, 2], [5, 0], [5, 2], [6, 1], [6, 1], [6, 1], [6, 1], [6, 1], [4, 2], [4, 2], [4, 2], [4, 3], [16, 2], [16, 1], [17, 1], [17, 1], [17, 1], [15, 1], [15, 1], [15, 2], [19, 2], [19, 2], [19, 1], [19, 1], [18, 2], [18, 1], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 9], [7, 6], [7, 4], [7, 1], [7, 2], [7, 2], [7, 1], [21, 1], [21, 1], [21, 1], [39, 2], [39, 1], [20, 4], [20, 3], [20, 4], [20, 2], [20, 2], [20, 1], [42, 1], [42, 6], [42, 5], [43, 1], [43, 3], [45, 4], [45, 4], [45, 6], [45, 4], [45, 4], [45, 4], [45, 8], [45, 4], [45, 4], [45, 4], [45, 6], [45, 4], [45, 4], [45, 4], [45, 4], [45, 4], [45, 1], [41, 2], [41, 3], [41, 3], [41, 1], [41, 3], [76, 1], [76, 2], [76, 1], [76, 1], [72, 1], [73, 3], [30, 1], [30, 2], [30, 1], [30, 1], [82, 1], [82, 1], [82, 1], [82, 1], [82, 1], [82, 1], [82, 1], [82, 1], [82, 1], [82, 1], [82, 1], [28, 1], [28, 2], [28, 1], [28, 1], [24, 5], [25, 5], [26, 2], [26, 4], [26, 3], [26, 5], [26, 3], [26, 5], [26, 5], [26, 7], [26, 2], [26, 4], [26, 2], [26, 4], [26, 4], [26, 6], [22, 5], [23, 5], [23, 5], [23, 9], [23, 9], [23, 7], [23, 7], [102, 1], [102, 3], [91, 1], [91, 3], [106, 1], [106, 2], [107, 1], [107, 1], [107, 1], [107, 1], [107, 1], [107, 1], [107, 1], [107, 1], [112, 1], [112, 1], [112, 1], [112, 1], [112, 1], [112, 1], [112, 1], [112, 1], [112, 1], [112, 1], [112, 1], [81, 1], [81, 1], [81, 1], [81, 1], [90, 1], [90, 1], [90, 1], [90, 1], [90, 1], [90, 1], [90, 1], [90, 1], [90, 1], [90, 1], [90, 1], [78, 1], [78, 1], [119, 1], [119, 1], [119, 1], [119, 1], [119, 1], [119, 1], [119, 1], [119, 1], [119, 1], [119, 1], [119, 1], [47, 1], [47, 2], [100, 1], [100, 2], [33, 1], [33, 1], [33, 1], [33, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          if (!Array.isArray($$[$0]) || $$[$0].length > 0) {\n            $$[$0 - 1].push($$[$0]);\n          }\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 181:\n          this.$ = $$[$0];\n          break;\n        case 11:\n          yy.setDirection(\"TB\");\n          this.$ = \"TB\";\n          break;\n        case 12:\n          yy.setDirection($$[$0 - 1]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 27:\n          this.$ = $$[$0 - 1].nodes;\n          break;\n        case 28:\n        case 29:\n        case 30:\n        case 31:\n        case 32:\n          this.$ = [];\n          break;\n        case 33:\n          this.$ = yy.addSubGraph($$[$0 - 6], $$[$0 - 1], $$[$0 - 4]);\n          break;\n        case 34:\n          this.$ = yy.addSubGraph($$[$0 - 3], $$[$0 - 1], $$[$0 - 3]);\n          break;\n        case 35:\n          this.$ = yy.addSubGraph(void 0, $$[$0 - 1], void 0);\n          break;\n        case 37:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 38:\n        case 39:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 43:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 44:\n          this.$ = $$[$0];\n          break;\n        case 45:\n          yy.addVertex($$[$0 - 1][0], void 0, void 0, void 0, void 0, void 0, void 0, $$[$0]);\n          yy.addLink($$[$0 - 3].stmt, $$[$0 - 1], $$[$0 - 2]);\n          this.$ = { stmt: $$[$0 - 1], nodes: $$[$0 - 1].concat($$[$0 - 3].nodes) };\n          break;\n        case 46:\n          yy.addLink($$[$0 - 2].stmt, $$[$0], $$[$0 - 1]);\n          this.$ = { stmt: $$[$0], nodes: $$[$0].concat($$[$0 - 2].nodes) };\n          break;\n        case 47:\n          yy.addLink($$[$0 - 3].stmt, $$[$0 - 1], $$[$0 - 2]);\n          this.$ = { stmt: $$[$0 - 1], nodes: $$[$0 - 1].concat($$[$0 - 3].nodes) };\n          break;\n        case 48:\n          this.$ = { stmt: $$[$0 - 1], nodes: $$[$0 - 1] };\n          break;\n        case 49:\n          yy.addVertex($$[$0 - 1][0], void 0, void 0, void 0, void 0, void 0, void 0, $$[$0]);\n          this.$ = { stmt: $$[$0 - 1], nodes: $$[$0 - 1], shapeData: $$[$0] };\n          break;\n        case 50:\n          this.$ = { stmt: $$[$0], nodes: $$[$0] };\n          break;\n        case 51:\n          this.$ = [$$[$0]];\n          break;\n        case 52:\n          yy.addVertex($$[$0 - 5][0], void 0, void 0, void 0, void 0, void 0, void 0, $$[$0 - 4]);\n          this.$ = $$[$0 - 5].concat($$[$0]);\n          break;\n        case 53:\n          this.$ = $$[$0 - 4].concat($$[$0]);\n          break;\n        case 54:\n          this.$ = $$[$0];\n          break;\n        case 55:\n          this.$ = $$[$0 - 2];\n          yy.setClass($$[$0 - 2], $$[$0]);\n          break;\n        case 56:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"square\");\n          break;\n        case 57:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"doublecircle\");\n          break;\n        case 58:\n          this.$ = $$[$0 - 5];\n          yy.addVertex($$[$0 - 5], $$[$0 - 2], \"circle\");\n          break;\n        case 59:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"ellipse\");\n          break;\n        case 60:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"stadium\");\n          break;\n        case 61:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"subroutine\");\n          break;\n        case 62:\n          this.$ = $$[$0 - 7];\n          yy.addVertex($$[$0 - 7], $$[$0 - 1], \"rect\", void 0, void 0, void 0, Object.fromEntries([[$$[$0 - 5], $$[$0 - 3]]]));\n          break;\n        case 63:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"cylinder\");\n          break;\n        case 64:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"round\");\n          break;\n        case 65:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"diamond\");\n          break;\n        case 66:\n          this.$ = $$[$0 - 5];\n          yy.addVertex($$[$0 - 5], $$[$0 - 2], \"hexagon\");\n          break;\n        case 67:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"odd\");\n          break;\n        case 68:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"trapezoid\");\n          break;\n        case 69:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"inv_trapezoid\");\n          break;\n        case 70:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"lean_right\");\n          break;\n        case 71:\n          this.$ = $$[$0 - 3];\n          yy.addVertex($$[$0 - 3], $$[$0 - 1], \"lean_left\");\n          break;\n        case 72:\n          this.$ = $$[$0];\n          yy.addVertex($$[$0]);\n          break;\n        case 73:\n          $$[$0 - 1].text = $$[$0];\n          this.$ = $$[$0 - 1];\n          break;\n        case 74:\n        case 75:\n          $$[$0 - 2].text = $$[$0 - 1];\n          this.$ = $$[$0 - 2];\n          break;\n        case 76:\n          this.$ = $$[$0];\n          break;\n        case 77:\n          var inf = yy.destructLink($$[$0], $$[$0 - 2]);\n          this.$ = { \"type\": inf.type, \"stroke\": inf.stroke, \"length\": inf.length, \"text\": $$[$0 - 1] };\n          break;\n        case 78:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 79:\n          this.$ = { text: $$[$0 - 1].text + \"\" + $$[$0], type: $$[$0 - 1].type };\n          break;\n        case 80:\n          this.$ = { text: $$[$0], type: \"string\" };\n          break;\n        case 81:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 82:\n          var inf = yy.destructLink($$[$0]);\n          this.$ = { \"type\": inf.type, \"stroke\": inf.stroke, \"length\": inf.length };\n          break;\n        case 83:\n          this.$ = $$[$0 - 1];\n          break;\n        case 84:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 85:\n          this.$ = { text: $$[$0 - 1].text + \"\" + $$[$0], type: $$[$0 - 1].type };\n          break;\n        case 86:\n          this.$ = { text: $$[$0], type: \"string\" };\n          break;\n        case 87:\n        case 102:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 99:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 100:\n          this.$ = { text: $$[$0 - 1].text + \"\" + $$[$0], type: $$[$0 - 1].type };\n          break;\n        case 101:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 103:\n          this.$ = $$[$0 - 4];\n          yy.addClass($$[$0 - 2], $$[$0]);\n          break;\n        case 104:\n          this.$ = $$[$0 - 4];\n          yy.setClass($$[$0 - 2], $$[$0]);\n          break;\n        case 105:\n        case 113:\n          this.$ = $$[$0 - 1];\n          yy.setClickEvent($$[$0 - 1], $$[$0]);\n          break;\n        case 106:\n        case 114:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 2]);\n          yy.setTooltip($$[$0 - 3], $$[$0]);\n          break;\n        case 107:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 108:\n          this.$ = $$[$0 - 4];\n          yy.setClickEvent($$[$0 - 4], $$[$0 - 3], $$[$0 - 2]);\n          yy.setTooltip($$[$0 - 4], $$[$0]);\n          break;\n        case 109:\n          this.$ = $$[$0 - 2];\n          yy.setLink($$[$0 - 2], $$[$0]);\n          break;\n        case 110:\n          this.$ = $$[$0 - 4];\n          yy.setLink($$[$0 - 4], $$[$0 - 2]);\n          yy.setTooltip($$[$0 - 4], $$[$0]);\n          break;\n        case 111:\n          this.$ = $$[$0 - 4];\n          yy.setLink($$[$0 - 4], $$[$0 - 2], $$[$0]);\n          break;\n        case 112:\n          this.$ = $$[$0 - 6];\n          yy.setLink($$[$0 - 6], $$[$0 - 4], $$[$0]);\n          yy.setTooltip($$[$0 - 6], $$[$0 - 2]);\n          break;\n        case 115:\n          this.$ = $$[$0 - 1];\n          yy.setLink($$[$0 - 1], $$[$0]);\n          break;\n        case 116:\n          this.$ = $$[$0 - 3];\n          yy.setLink($$[$0 - 3], $$[$0 - 2]);\n          yy.setTooltip($$[$0 - 3], $$[$0]);\n          break;\n        case 117:\n          this.$ = $$[$0 - 3];\n          yy.setLink($$[$0 - 3], $$[$0 - 2], $$[$0]);\n          break;\n        case 118:\n          this.$ = $$[$0 - 5];\n          yy.setLink($$[$0 - 5], $$[$0 - 4], $$[$0]);\n          yy.setTooltip($$[$0 - 5], $$[$0 - 2]);\n          break;\n        case 119:\n          this.$ = $$[$0 - 4];\n          yy.addVertex($$[$0 - 2], void 0, void 0, $$[$0]);\n          break;\n        case 120:\n          this.$ = $$[$0 - 4];\n          yy.updateLink([$$[$0 - 2]], $$[$0]);\n          break;\n        case 121:\n          this.$ = $$[$0 - 4];\n          yy.updateLink($$[$0 - 2], $$[$0]);\n          break;\n        case 122:\n          this.$ = $$[$0 - 8];\n          yy.updateLinkInterpolate([$$[$0 - 6]], $$[$0 - 2]);\n          yy.updateLink([$$[$0 - 6]], $$[$0]);\n          break;\n        case 123:\n          this.$ = $$[$0 - 8];\n          yy.updateLinkInterpolate($$[$0 - 6], $$[$0 - 2]);\n          yy.updateLink($$[$0 - 6], $$[$0]);\n          break;\n        case 124:\n          this.$ = $$[$0 - 6];\n          yy.updateLinkInterpolate([$$[$0 - 4]], $$[$0]);\n          break;\n        case 125:\n          this.$ = $$[$0 - 6];\n          yy.updateLinkInterpolate($$[$0 - 4], $$[$0]);\n          break;\n        case 126:\n        case 128:\n          this.$ = [$$[$0]];\n          break;\n        case 127:\n        case 129:\n          $$[$0 - 2].push($$[$0]);\n          this.$ = $$[$0 - 2];\n          break;\n        case 131:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 179:\n          this.$ = $$[$0];\n          break;\n        case 180:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n        case 182:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n        case 183:\n          this.$ = { stmt: \"dir\", value: \"TB\" };\n          break;\n        case 184:\n          this.$ = { stmt: \"dir\", value: \"BT\" };\n          break;\n        case 185:\n          this.$ = { stmt: \"dir\", value: \"RL\" };\n          break;\n        case 186:\n          this.$ = { stmt: \"dir\", value: \"LR\" };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 9: $V0, 10: $V1, 12: $V2 }, { 1: [3] }, o($V3, $V4, { 5: 6 }), { 4: 7, 9: $V0, 10: $V1, 12: $V2 }, { 4: 8, 9: $V0, 10: $V1, 12: $V2 }, { 13: [1, 9], 14: [1, 10] }, { 1: [2, 1], 6: 11, 7: 12, 8: $V5, 9: $V6, 10: $V7, 11: $V8, 20: 17, 22: 18, 23: 19, 24: 20, 25: 21, 26: 22, 27: $V9, 33: 24, 34: $Va, 36: $Vb, 38: $Vc, 42: 28, 43: 38, 44: $Vd, 45: 39, 47: 40, 60: $Ve, 83: $Vf, 84: $Vg, 85: $Vh, 86: $Vi, 87: $Vj, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs, 120: $Vt, 121: $Vu, 122: $Vv, 123: $Vw }, o($V3, [2, 9]), o($V3, [2, 10]), o($V3, [2, 11]), { 8: [1, 54], 9: [1, 55], 10: $Vx, 15: 53, 18: 56 }, o($Vy, [2, 3]), o($Vy, [2, 4]), o($Vy, [2, 5]), o($Vy, [2, 6]), o($Vy, [2, 7]), o($Vy, [2, 8]), { 8: $Vz, 9: $VA, 11: $VB, 21: 58, 41: 59, 72: 63, 75: [1, 64], 77: [1, 65] }, { 8: $Vz, 9: $VA, 11: $VB, 21: 66 }, { 8: $Vz, 9: $VA, 11: $VB, 21: 67 }, { 8: $Vz, 9: $VA, 11: $VB, 21: 68 }, { 8: $Vz, 9: $VA, 11: $VB, 21: 69 }, { 8: $Vz, 9: $VA, 11: $VB, 21: 70 }, { 8: $Vz, 9: $VA, 10: [1, 71], 11: $VB, 21: 72 }, o($Vy, [2, 36]), { 35: [1, 73] }, { 37: [1, 74] }, o($Vy, [2, 39]), o($VC, [2, 50], { 18: 75, 39: 76, 10: $Vx, 40: $VD }), { 10: [1, 78] }, { 10: [1, 79] }, { 10: [1, 80] }, { 10: [1, 81] }, { 14: $VE, 44: $VF, 60: $VG, 79: [1, 85], 88: $VH, 94: [1, 82], 96: [1, 83], 100: 84, 104: $VI, 105: $VJ, 108: $VK, 110: $VL, 113: $VM, 114: $VN, 115: $VO, 119: 86 }, o($Vy, [2, 183]), o($Vy, [2, 184]), o($Vy, [2, 185]), o($Vy, [2, 186]), o($VP, [2, 51]), o($VP, [2, 54], { 46: [1, 98] }), o($VQ, [2, 72], { 112: 111, 29: [1, 99], 44: $Vd, 48: [1, 100], 50: [1, 101], 52: [1, 102], 54: [1, 103], 56: [1, 104], 58: [1, 105], 60: $Ve, 63: [1, 106], 65: [1, 107], 67: [1, 108], 68: [1, 109], 70: [1, 110], 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 113: $Vq, 114: $Vr, 115: $Vs }), o($VR, [2, 179]), o($VR, [2, 140]), o($VR, [2, 141]), o($VR, [2, 142]), o($VR, [2, 143]), o($VR, [2, 144]), o($VR, [2, 145]), o($VR, [2, 146]), o($VR, [2, 147]), o($VR, [2, 148]), o($VR, [2, 149]), o($VR, [2, 150]), o($V3, [2, 12]), o($V3, [2, 18]), o($V3, [2, 19]), { 9: [1, 112] }, o($VS, [2, 26], { 18: 113, 10: $Vx }), o($Vy, [2, 27]), { 42: 114, 43: 38, 44: $Vd, 45: 39, 47: 40, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs }, o($Vy, [2, 40]), o($Vy, [2, 41]), o($Vy, [2, 42]), o($VT, [2, 76], { 73: 115, 62: [1, 117], 74: [1, 116] }), { 76: 118, 78: 119, 79: [1, 120], 80: [1, 121], 115: $VU, 118: $VV }, o([44, 60, 62, 74, 88, 101, 104, 105, 108, 110, 113, 114, 115], [2, 82]), o($Vy, [2, 28]), o($Vy, [2, 29]), o($Vy, [2, 30]), o($Vy, [2, 31]), o($Vy, [2, 32]), { 10: $VW, 12: $VX, 14: $VY, 27: $VZ, 28: 124, 32: $V_, 44: $V$, 60: $V01, 75: $V11, 79: [1, 126], 80: [1, 127], 82: 137, 83: $V21, 84: $V31, 85: $V41, 86: $V51, 87: $V61, 88: $V71, 89: $V81, 90: 125, 104: $V91, 108: $Va1, 110: $Vb1, 113: $Vc1, 114: $Vd1, 115: $Ve1 }, o($Vf1, $V4, { 5: 150 }), o($Vy, [2, 37]), o($Vy, [2, 38]), o($VC, [2, 48], { 44: $Vg1 }), o($VC, [2, 49], { 18: 152, 10: $Vx, 40: $Vh1 }), o($VP, [2, 44]), { 44: $Vd, 47: 154, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs }, { 101: [1, 155], 102: 156, 104: [1, 157] }, { 44: $Vd, 47: 158, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs }, { 44: $Vd, 47: 159, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs }, o($Vi1, [2, 105], { 10: [1, 160], 95: [1, 161] }), { 79: [1, 162] }, o($Vi1, [2, 113], { 119: 164, 10: [1, 163], 14: $VE, 44: $VF, 60: $VG, 88: $VH, 104: $VI, 105: $VJ, 108: $VK, 110: $VL, 113: $VM, 114: $VN, 115: $VO }), o($Vi1, [2, 115], { 10: [1, 165] }), o($Vj1, [2, 181]), o($Vj1, [2, 168]), o($Vj1, [2, 169]), o($Vj1, [2, 170]), o($Vj1, [2, 171]), o($Vj1, [2, 172]), o($Vj1, [2, 173]), o($Vj1, [2, 174]), o($Vj1, [2, 175]), o($Vj1, [2, 176]), o($Vj1, [2, 177]), o($Vj1, [2, 178]), { 44: $Vd, 47: 166, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs }, { 30: 167, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 175, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 177, 50: [1, 176], 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 178, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 179, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 180, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 108: [1, 181] }, { 30: 182, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 183, 65: [1, 184], 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 185, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 186, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 187, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, o($VR, [2, 180]), o($V3, [2, 20]), o($VS, [2, 25]), o($VC, [2, 46], { 39: 188, 18: 189, 10: $Vx, 40: $VD }), o($VT, [2, 73], { 10: [1, 190] }), { 10: [1, 191] }, { 30: 192, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 77: [1, 193], 78: 194, 115: $VU, 118: $VV }, o($Vq1, [2, 78]), o($Vq1, [2, 80]), o($Vq1, [2, 81]), o($Vq1, [2, 166]), o($Vq1, [2, 167]), { 8: $Vz, 9: $VA, 10: $VW, 11: $VB, 12: $VX, 14: $VY, 21: 196, 27: $VZ, 29: [1, 195], 32: $V_, 44: $V$, 60: $V01, 75: $V11, 82: 137, 83: $V21, 84: $V31, 85: $V41, 86: $V51, 87: $V61, 88: $V71, 89: $V81, 90: 197, 104: $V91, 108: $Va1, 110: $Vb1, 113: $Vc1, 114: $Vd1, 115: $Ve1 }, o($Vr1, [2, 99]), o($Vr1, [2, 101]), o($Vr1, [2, 102]), o($Vr1, [2, 155]), o($Vr1, [2, 156]), o($Vr1, [2, 157]), o($Vr1, [2, 158]), o($Vr1, [2, 159]), o($Vr1, [2, 160]), o($Vr1, [2, 161]), o($Vr1, [2, 162]), o($Vr1, [2, 163]), o($Vr1, [2, 164]), o($Vr1, [2, 165]), o($Vr1, [2, 88]), o($Vr1, [2, 89]), o($Vr1, [2, 90]), o($Vr1, [2, 91]), o($Vr1, [2, 92]), o($Vr1, [2, 93]), o($Vr1, [2, 94]), o($Vr1, [2, 95]), o($Vr1, [2, 96]), o($Vr1, [2, 97]), o($Vr1, [2, 98]), { 6: 11, 7: 12, 8: $V5, 9: $V6, 10: $V7, 11: $V8, 20: 17, 22: 18, 23: 19, 24: 20, 25: 21, 26: 22, 27: $V9, 32: [1, 198], 33: 24, 34: $Va, 36: $Vb, 38: $Vc, 42: 28, 43: 38, 44: $Vd, 45: 39, 47: 40, 60: $Ve, 83: $Vf, 84: $Vg, 85: $Vh, 86: $Vi, 87: $Vj, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs, 120: $Vt, 121: $Vu, 122: $Vv, 123: $Vw }, { 10: $Vx, 18: 199 }, { 44: [1, 200] }, o($VP, [2, 43]), { 10: [1, 201], 44: $Vd, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 111, 113: $Vq, 114: $Vr, 115: $Vs }, { 10: [1, 202] }, { 10: [1, 203], 105: [1, 204] }, o($Vs1, [2, 126]), { 10: [1, 205], 44: $Vd, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 111, 113: $Vq, 114: $Vr, 115: $Vs }, { 10: [1, 206], 44: $Vd, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 111, 113: $Vq, 114: $Vr, 115: $Vs }, { 79: [1, 207] }, o($Vi1, [2, 107], { 10: [1, 208] }), o($Vi1, [2, 109], { 10: [1, 209] }), { 79: [1, 210] }, o($Vj1, [2, 182]), { 79: [1, 211], 97: [1, 212] }, o($VP, [2, 55], { 112: 111, 44: $Vd, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 113: $Vq, 114: $Vr, 115: $Vs }), { 31: [1, 213], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, o($Vt1, [2, 84]), o($Vt1, [2, 86]), o($Vt1, [2, 87]), o($Vt1, [2, 151]), o($Vt1, [2, 152]), o($Vt1, [2, 153]), o($Vt1, [2, 154]), { 49: [1, 215], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 216, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 51: [1, 217], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 53: [1, 218], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 55: [1, 219], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 57: [1, 220], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 60: [1, 221] }, { 64: [1, 222], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 66: [1, 223], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 30: 224, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 31: [1, 225], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 67: $Vk1, 69: [1, 226], 71: [1, 227], 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 67: $Vk1, 69: [1, 229], 71: [1, 228], 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, o($VC, [2, 45], { 18: 152, 10: $Vx, 40: $Vh1 }), o($VC, [2, 47], { 44: $Vg1 }), o($VT, [2, 75]), o($VT, [2, 74]), { 62: [1, 230], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, o($VT, [2, 77]), o($Vq1, [2, 79]), { 30: 231, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, o($Vf1, $V4, { 5: 232 }), o($Vr1, [2, 100]), o($Vy, [2, 35]), { 43: 233, 44: $Vd, 45: 39, 47: 40, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs }, { 10: $Vx, 18: 234 }, { 10: $Vu1, 60: $Vv1, 83: $Vw1, 91: 235, 104: $Vx1, 106: 236, 107: 237, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }, { 10: $Vu1, 60: $Vv1, 83: $Vw1, 91: 246, 103: [1, 247], 104: $Vx1, 106: 236, 107: 237, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }, { 10: $Vu1, 60: $Vv1, 83: $Vw1, 91: 248, 103: [1, 249], 104: $Vx1, 106: 236, 107: 237, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }, { 104: [1, 250] }, { 10: $Vu1, 60: $Vv1, 83: $Vw1, 91: 251, 104: $Vx1, 106: 236, 107: 237, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }, { 44: $Vd, 47: 252, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs }, o($Vi1, [2, 106]), { 79: [1, 253] }, { 79: [1, 254], 97: [1, 255] }, o($Vi1, [2, 114]), o($Vi1, [2, 116], { 10: [1, 256] }), o($Vi1, [2, 117]), o($VQ, [2, 56]), o($Vt1, [2, 85]), o($VQ, [2, 57]), { 51: [1, 257], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, o($VQ, [2, 64]), o($VQ, [2, 59]), o($VQ, [2, 60]), o($VQ, [2, 61]), { 108: [1, 258] }, o($VQ, [2, 63]), o($VQ, [2, 65]), { 66: [1, 259], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, o($VQ, [2, 67]), o($VQ, [2, 68]), o($VQ, [2, 70]), o($VQ, [2, 69]), o($VQ, [2, 71]), o([10, 44, 60, 88, 101, 104, 105, 108, 110, 113, 114, 115], [2, 83]), { 31: [1, 260], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 6: 11, 7: 12, 8: $V5, 9: $V6, 10: $V7, 11: $V8, 20: 17, 22: 18, 23: 19, 24: 20, 25: 21, 26: 22, 27: $V9, 32: [1, 261], 33: 24, 34: $Va, 36: $Vb, 38: $Vc, 42: 28, 43: 38, 44: $Vd, 45: 39, 47: 40, 60: $Ve, 83: $Vf, 84: $Vg, 85: $Vh, 86: $Vi, 87: $Vj, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs, 120: $Vt, 121: $Vu, 122: $Vv, 123: $Vw }, o($VP, [2, 53]), { 43: 262, 44: $Vd, 45: 39, 47: 40, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs }, o($Vi1, [2, 119], { 105: $VC1 }), o($VD1, [2, 128], { 107: 264, 10: $Vu1, 60: $Vv1, 83: $Vw1, 104: $Vx1, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }), o($VE1, [2, 130]), o($VE1, [2, 132]), o($VE1, [2, 133]), o($VE1, [2, 134]), o($VE1, [2, 135]), o($VE1, [2, 136]), o($VE1, [2, 137]), o($VE1, [2, 138]), o($VE1, [2, 139]), o($Vi1, [2, 120], { 105: $VC1 }), { 10: [1, 265] }, o($Vi1, [2, 121], { 105: $VC1 }), { 10: [1, 266] }, o($Vs1, [2, 127]), o($Vi1, [2, 103], { 105: $VC1 }), o($Vi1, [2, 104], { 112: 111, 44: $Vd, 60: $Ve, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 113: $Vq, 114: $Vr, 115: $Vs }), o($Vi1, [2, 108]), o($Vi1, [2, 110], { 10: [1, 267] }), o($Vi1, [2, 111]), { 97: [1, 268] }, { 51: [1, 269] }, { 62: [1, 270] }, { 66: [1, 271] }, { 8: $Vz, 9: $VA, 11: $VB, 21: 272 }, o($Vy, [2, 34]), o($VP, [2, 52]), { 10: $Vu1, 60: $Vv1, 83: $Vw1, 104: $Vx1, 106: 273, 107: 237, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }, o($VE1, [2, 131]), { 14: $VE, 44: $VF, 60: $VG, 88: $VH, 100: 274, 104: $VI, 105: $VJ, 108: $VK, 110: $VL, 113: $VM, 114: $VN, 115: $VO, 119: 86 }, { 14: $VE, 44: $VF, 60: $VG, 88: $VH, 100: 275, 104: $VI, 105: $VJ, 108: $VK, 110: $VL, 113: $VM, 114: $VN, 115: $VO, 119: 86 }, { 97: [1, 276] }, o($Vi1, [2, 118]), o($VQ, [2, 58]), { 30: 277, 67: $Vk1, 79: $Vl1, 80: $Vm1, 81: 168, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, o($VQ, [2, 66]), o($Vf1, $V4, { 5: 278 }), o($VD1, [2, 129], { 107: 264, 10: $Vu1, 60: $Vv1, 83: $Vw1, 104: $Vx1, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }), o($Vi1, [2, 124], { 119: 164, 10: [1, 279], 14: $VE, 44: $VF, 60: $VG, 88: $VH, 104: $VI, 105: $VJ, 108: $VK, 110: $VL, 113: $VM, 114: $VN, 115: $VO }), o($Vi1, [2, 125], { 119: 164, 10: [1, 280], 14: $VE, 44: $VF, 60: $VG, 88: $VH, 104: $VI, 105: $VJ, 108: $VK, 110: $VL, 113: $VM, 114: $VN, 115: $VO }), o($Vi1, [2, 112]), { 31: [1, 281], 67: $Vk1, 81: 214, 115: $Vn1, 116: $Vo1, 117: $Vp1 }, { 6: 11, 7: 12, 8: $V5, 9: $V6, 10: $V7, 11: $V8, 20: 17, 22: 18, 23: 19, 24: 20, 25: 21, 26: 22, 27: $V9, 32: [1, 282], 33: 24, 34: $Va, 36: $Vb, 38: $Vc, 42: 28, 43: 38, 44: $Vd, 45: 39, 47: 40, 60: $Ve, 83: $Vf, 84: $Vg, 85: $Vh, 86: $Vi, 87: $Vj, 88: $Vk, 101: $Vl, 104: $Vm, 105: $Vn, 108: $Vo, 110: $Vp, 112: 41, 113: $Vq, 114: $Vr, 115: $Vs, 120: $Vt, 121: $Vu, 122: $Vv, 123: $Vw }, { 10: $Vu1, 60: $Vv1, 83: $Vw1, 91: 283, 104: $Vx1, 106: 236, 107: 237, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }, { 10: $Vu1, 60: $Vv1, 83: $Vw1, 91: 284, 104: $Vx1, 106: 236, 107: 237, 108: $Vy1, 109: $Vz1, 110: $VA1, 111: $VB1 }, o($VQ, [2, 62]), o($Vy, [2, 33]), o($Vi1, [2, 122], { 105: $VC1 }), o($Vi1, [2, 123], { 105: $VC1 })],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(popStack, \"popStack\");\n      function lex2() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(lex2, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex2();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function lex2() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.begin(\"acc_title\");\n            return 34;\n            break;\n          case 1:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 2:\n            this.begin(\"acc_descr\");\n            return 36;\n            break;\n          case 3:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 4:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 5:\n            this.popState();\n            break;\n          case 6:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 7:\n            this.pushState(\"shapeData\");\n            yy_.yytext = \"\";\n            return 40;\n            break;\n          case 8:\n            this.pushState(\"shapeDataStr\");\n            return 40;\n            break;\n          case 9:\n            this.popState();\n            return 40;\n            break;\n          case 10:\n            const re = /\\n\\s*/g;\n            yy_.yytext = yy_.yytext.replace(re, \"<br/>\");\n            return 40;\n            break;\n          case 11:\n            return 40;\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            this.begin(\"callbackname\");\n            break;\n          case 14:\n            this.popState();\n            break;\n          case 15:\n            this.popState();\n            this.begin(\"callbackargs\");\n            break;\n          case 16:\n            return 94;\n            break;\n          case 17:\n            this.popState();\n            break;\n          case 18:\n            return 95;\n            break;\n          case 19:\n            return \"MD_STR\";\n            break;\n          case 20:\n            this.popState();\n            break;\n          case 21:\n            this.begin(\"md_string\");\n            break;\n          case 22:\n            return \"STR\";\n            break;\n          case 23:\n            this.popState();\n            break;\n          case 24:\n            this.pushState(\"string\");\n            break;\n          case 25:\n            return 83;\n            break;\n          case 26:\n            return 101;\n            break;\n          case 27:\n            return 84;\n            break;\n          case 28:\n            return 103;\n            break;\n          case 29:\n            return 85;\n            break;\n          case 30:\n            return 86;\n            break;\n          case 31:\n            return 96;\n            break;\n          case 32:\n            this.begin(\"click\");\n            break;\n          case 33:\n            this.popState();\n            break;\n          case 34:\n            return 87;\n            break;\n          case 35:\n            if (yy.lex.firstGraph()) {\n              this.begin(\"dir\");\n            }\n            return 12;\n            break;\n          case 36:\n            if (yy.lex.firstGraph()) {\n              this.begin(\"dir\");\n            }\n            return 12;\n            break;\n          case 37:\n            if (yy.lex.firstGraph()) {\n              this.begin(\"dir\");\n            }\n            return 12;\n            break;\n          case 38:\n            return 27;\n            break;\n          case 39:\n            return 32;\n            break;\n          case 40:\n            return 97;\n            break;\n          case 41:\n            return 97;\n            break;\n          case 42:\n            return 97;\n            break;\n          case 43:\n            return 97;\n            break;\n          case 44:\n            this.popState();\n            return 13;\n            break;\n          case 45:\n            this.popState();\n            return 14;\n            break;\n          case 46:\n            this.popState();\n            return 14;\n            break;\n          case 47:\n            this.popState();\n            return 14;\n            break;\n          case 48:\n            this.popState();\n            return 14;\n            break;\n          case 49:\n            this.popState();\n            return 14;\n            break;\n          case 50:\n            this.popState();\n            return 14;\n            break;\n          case 51:\n            this.popState();\n            return 14;\n            break;\n          case 52:\n            this.popState();\n            return 14;\n            break;\n          case 53:\n            this.popState();\n            return 14;\n            break;\n          case 54:\n            this.popState();\n            return 14;\n            break;\n          case 55:\n            return 120;\n            break;\n          case 56:\n            return 121;\n            break;\n          case 57:\n            return 122;\n            break;\n          case 58:\n            return 123;\n            break;\n          case 59:\n            return 104;\n            break;\n          case 60:\n            return 110;\n            break;\n          case 61:\n            return 46;\n            break;\n          case 62:\n            return 60;\n            break;\n          case 63:\n            return 44;\n            break;\n          case 64:\n            return 8;\n            break;\n          case 65:\n            return 105;\n            break;\n          case 66:\n            return 114;\n            break;\n          case 67:\n            this.popState();\n            return 77;\n            break;\n          case 68:\n            this.pushState(\"edgeText\");\n            return 75;\n            break;\n          case 69:\n            return 118;\n            break;\n          case 70:\n            this.popState();\n            return 77;\n            break;\n          case 71:\n            this.pushState(\"thickEdgeText\");\n            return 75;\n            break;\n          case 72:\n            return 118;\n            break;\n          case 73:\n            this.popState();\n            return 77;\n            break;\n          case 74:\n            this.pushState(\"dottedEdgeText\");\n            return 75;\n            break;\n          case 75:\n            return 118;\n            break;\n          case 76:\n            return 77;\n            break;\n          case 77:\n            this.popState();\n            return 53;\n            break;\n          case 78:\n            return \"TEXT\";\n            break;\n          case 79:\n            this.pushState(\"ellipseText\");\n            return 52;\n            break;\n          case 80:\n            this.popState();\n            return 55;\n            break;\n          case 81:\n            this.pushState(\"text\");\n            return 54;\n            break;\n          case 82:\n            this.popState();\n            return 57;\n            break;\n          case 83:\n            this.pushState(\"text\");\n            return 56;\n            break;\n          case 84:\n            return 58;\n            break;\n          case 85:\n            this.pushState(\"text\");\n            return 67;\n            break;\n          case 86:\n            this.popState();\n            return 64;\n            break;\n          case 87:\n            this.pushState(\"text\");\n            return 63;\n            break;\n          case 88:\n            this.popState();\n            return 49;\n            break;\n          case 89:\n            this.pushState(\"text\");\n            return 48;\n            break;\n          case 90:\n            this.popState();\n            return 69;\n            break;\n          case 91:\n            this.popState();\n            return 71;\n            break;\n          case 92:\n            return 116;\n            break;\n          case 93:\n            this.pushState(\"trapText\");\n            return 68;\n            break;\n          case 94:\n            this.pushState(\"trapText\");\n            return 70;\n            break;\n          case 95:\n            return 117;\n            break;\n          case 96:\n            return 67;\n            break;\n          case 97:\n            return 89;\n            break;\n          case 98:\n            return \"SEP\";\n            break;\n          case 99:\n            return 88;\n            break;\n          case 100:\n            return 114;\n            break;\n          case 101:\n            return 110;\n            break;\n          case 102:\n            return 44;\n            break;\n          case 103:\n            return 108;\n            break;\n          case 104:\n            return 113;\n            break;\n          case 105:\n            return 115;\n            break;\n          case 106:\n            this.popState();\n            return 62;\n            break;\n          case 107:\n            this.pushState(\"text\");\n            return 62;\n            break;\n          case 108:\n            this.popState();\n            return 51;\n            break;\n          case 109:\n            this.pushState(\"text\");\n            return 50;\n            break;\n          case 110:\n            this.popState();\n            return 31;\n            break;\n          case 111:\n            this.pushState(\"text\");\n            return 29;\n            break;\n          case 112:\n            this.popState();\n            return 66;\n            break;\n          case 113:\n            this.pushState(\"text\");\n            return 65;\n            break;\n          case 114:\n            return \"TEXT\";\n            break;\n          case 115:\n            return \"QUOTE\";\n            break;\n          case 116:\n            return 9;\n            break;\n          case 117:\n            return 10;\n            break;\n          case 118:\n            return 11;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:@\\{)/, /^(?:[\"])/, /^(?:[\"])/, /^(?:[^\\\"]+)/, /^(?:[^}^\"]+)/, /^(?:\\})/, /^(?:call[\\s]+)/, /^(?:\\([\\s]*\\))/, /^(?:\\()/, /^(?:[^(]*)/, /^(?:\\))/, /^(?:[^)]*)/, /^(?:[^`\"]+)/, /^(?:[`][\"])/, /^(?:[\"][`])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:[\"])/, /^(?:style\\b)/, /^(?:default\\b)/, /^(?:linkStyle\\b)/, /^(?:interpolate\\b)/, /^(?:classDef\\b)/, /^(?:class\\b)/, /^(?:href[\\s])/, /^(?:click[\\s]+)/, /^(?:[\\s\\n])/, /^(?:[^\\s\\n]*)/, /^(?:flowchart-elk\\b)/, /^(?:graph\\b)/, /^(?:flowchart\\b)/, /^(?:subgraph\\b)/, /^(?:end\\b\\s*)/, /^(?:_self\\b)/, /^(?:_blank\\b)/, /^(?:_parent\\b)/, /^(?:_top\\b)/, /^(?:(\\r?\\n)*\\s*\\n)/, /^(?:\\s*LR\\b)/, /^(?:\\s*RL\\b)/, /^(?:\\s*TB\\b)/, /^(?:\\s*BT\\b)/, /^(?:\\s*TD\\b)/, /^(?:\\s*BR\\b)/, /^(?:\\s*<)/, /^(?:\\s*>)/, /^(?:\\s*\\^)/, /^(?:\\s*v\\b)/, /^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:[0-9]+)/, /^(?:#)/, /^(?::::)/, /^(?::)/, /^(?:&)/, /^(?:;)/, /^(?:,)/, /^(?:\\*)/, /^(?:\\s*[xo<]?--+[-xo>]\\s*)/, /^(?:\\s*[xo<]?--\\s*)/, /^(?:[^-]|-(?!-)+)/, /^(?:\\s*[xo<]?==+[=xo>]\\s*)/, /^(?:\\s*[xo<]?==\\s*)/, /^(?:[^=]|=(?!))/, /^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/, /^(?:\\s*[xo<]?-\\.\\s*)/, /^(?:[^\\.]|\\.(?!))/, /^(?:\\s*~~[\\~]+\\s*)/, /^(?:[-/\\)][\\)])/, /^(?:[^\\(\\)\\[\\]\\{\\}]|!\\)+)/, /^(?:\\(-)/, /^(?:\\]\\))/, /^(?:\\(\\[)/, /^(?:\\]\\])/, /^(?:\\[\\[)/, /^(?:\\[\\|)/, /^(?:>)/, /^(?:\\)\\])/, /^(?:\\[\\()/, /^(?:\\)\\)\\))/, /^(?:\\(\\(\\()/, /^(?:[\\\\(?=\\])][\\]])/, /^(?:\\/(?=\\])\\])/, /^(?:\\/(?!\\])|\\\\(?!\\])|[^\\\\\\[\\]\\(\\)\\{\\}\\/]+)/, /^(?:\\[\\/)/, /^(?:\\[\\\\)/, /^(?:<)/, /^(?:>)/, /^(?:\\^)/, /^(?:\\\\\\|)/, /^(?:v\\b)/, /^(?:\\*)/, /^(?:#)/, /^(?:&)/, /^(?:([A-Za-z0-9!\"\\#$%&'*+\\.`?\\\\_\\/]|-(?=[^\\>\\-\\.])|(?!))+)/, /^(?:-)/, /^(?:[\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6]|[\\u00F8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377]|[\\u037A-\\u037D\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5]|[\\u03F7-\\u0481\\u048A-\\u0527\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA]|[\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE]|[\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA]|[\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0]|[\\u08A2-\\u08AC\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0977]|[\\u0979-\\u097F\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2]|[\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A]|[\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39]|[\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8]|[\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C]|[\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C]|[\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99]|[\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0]|[\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C33\\u0C35-\\u0C39\\u0C3D]|[\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3]|[\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10]|[\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1]|[\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81]|[\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3]|[\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6]|[\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A]|[\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081]|[\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D]|[\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0]|[\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310]|[\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C]|[\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u1700-\\u170C\\u170E-\\u1711]|[\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7]|[\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191C]|[\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16]|[\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF]|[\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC]|[\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D]|[\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D]|[\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3]|[\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F]|[\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128]|[\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184]|[\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3]|[\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6]|[\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE]|[\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C]|[\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D]|[\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC]|[\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B]|[\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788]|[\\uA78B-\\uA78E\\uA790-\\uA793\\uA7A0-\\uA7AA\\uA7F8-\\uA801\\uA803-\\uA805]|[\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB]|[\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uAA00-\\uAA28]|[\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA80-\\uAAAF\\uAAB1\\uAAB5]|[\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4]|[\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E]|[\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D]|[\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36]|[\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D]|[\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC]|[\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF]|[\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC])/, /^(?:\\|)/, /^(?:\\|)/, /^(?:\\))/, /^(?:\\()/, /^(?:\\])/, /^(?:\\[)/, /^(?:(\\}))/, /^(?:\\{)/, /^(?:[^\\[\\]\\(\\)\\{\\}\\|\\\"]+)/, /^(?:\")/, /^(?:(\\r?\\n)+)/, /^(?:\\s)/, /^(?:$)/],\n      conditions: { \"shapeDataEndBracket\": { \"rules\": [21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"shapeDataStr\": { \"rules\": [9, 10, 21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"shapeData\": { \"rules\": [8, 11, 12, 21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"callbackargs\": { \"rules\": [17, 18, 21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"callbackname\": { \"rules\": [14, 15, 16, 21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"href\": { \"rules\": [21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"click\": { \"rules\": [21, 24, 33, 34, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"dottedEdgeText\": { \"rules\": [21, 24, 73, 75, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"thickEdgeText\": { \"rules\": [21, 24, 70, 72, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"edgeText\": { \"rules\": [21, 24, 67, 69, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"trapText\": { \"rules\": [21, 24, 76, 79, 81, 83, 87, 89, 90, 91, 92, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"ellipseText\": { \"rules\": [21, 24, 76, 77, 78, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"text\": { \"rules\": [21, 24, 76, 79, 80, 81, 82, 83, 86, 87, 88, 89, 93, 94, 106, 107, 108, 109, 110, 111, 112, 113, 114], \"inclusive\": false }, \"vertex\": { \"rules\": [21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"dir\": { \"rules\": [21, 24, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [5, 6, 21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"acc_descr\": { \"rules\": [3, 21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"acc_title\": { \"rules\": [1, 21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"md_string\": { \"rules\": [19, 20, 21, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"string\": { \"rules\": [21, 22, 23, 24, 76, 79, 81, 83, 87, 89, 93, 94, 107, 109, 111, 113], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 2, 4, 7, 13, 21, 24, 25, 26, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 41, 42, 43, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 70, 71, 73, 74, 76, 79, 81, 83, 84, 85, 87, 89, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 107, 109, 111, 113, 115, 116, 117, 118], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar flow_default = parser;\n\n// src/diagrams/flowchart/styles.ts\n\nvar fade = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((color, opacity) => {\n  const channel2 = khroma__WEBPACK_IMPORTED_MODULE_12__.channel;\n  const r = channel2(color, \"r\");\n  const g = channel2(color, \"g\");\n  const b = channel2(color, \"b\");\n  return khroma__WEBPACK_IMPORTED_MODULE_12__.rgba(r, g, b, opacity);\n}, \"fade\");\nvar getStyles = /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((options) => `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .cluster-label text {\n    fill: ${options.titleColor};\n  }\n  .cluster-label span {\n    color: ${options.titleColor};\n  }\n  .cluster-label span p {\n    background-color: transparent;\n  }\n\n  .label text,span {\n    fill: ${options.nodeTextColor || options.textColor};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n  .rough-node .label text , .node .label text, .image-shape .label, .icon-shape .label {\n    text-anchor: middle;\n  }\n  // .flowchart-label .text-outer-tspan {\n  //   text-anchor: middle;\n  // }\n  // .flowchart-label .text-inner-tspan {\n  //   text-anchor: start;\n  // }\n\n  .node .katex path {\n    fill: #000;\n    stroke: #000;\n    stroke-width: 1px;\n  }\n\n  .rough-node .label,.node .label, .image-shape .label, .icon-shape .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n\n  .root .anchor path {\n    fill: ${options.lineColor} !important;\n    stroke-width: 0;\n    stroke: ${options.lineColor};\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 2.0px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    p {\n      background-color: ${options.edgeLabelBackground};\n    }\n    rect {\n      opacity: 0.5;\n      background-color: ${options.edgeLabelBackground};\n      fill: ${options.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n\n  /* For html labels only */\n  .labelBkg {\n    background-color: ${fade(options.edgeLabelBackground, 0.5)};\n    // background-color:\n  }\n\n  .cluster rect {\n    fill: ${options.clusterBkg};\n    stroke: ${options.clusterBorder};\n    stroke-width: 1px;\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  .cluster span {\n    color: ${options.titleColor};\n  }\n  /* .cluster div {\n    color: ${options.titleColor};\n  } */\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .flowchartTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n\n  rect.text {\n    fill: none;\n    stroke-width: 0;\n  }\n\n  .icon-shape, .image-shape {\n    background-color: ${options.edgeLabelBackground};\n    p {\n      background-color: ${options.edgeLabelBackground};\n      padding: 2px;\n    }\n    rect {\n      opacity: 0.5;\n      background-color: ${options.edgeLabelBackground};\n      fill: ${options.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/flowchart/flowDiagram.ts\nvar diagram = {\n  parser: flow_default,\n  db: flowDb_default,\n  renderer: flowRenderer_v3_unified_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((cnf) => {\n    if (!cnf.flowchart) {\n      cnf.flowchart = {};\n    }\n    if (cnf.layout) {\n      (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.setConfig2)({ layout: cnf.layout });\n    }\n    cnf.flowchart.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n    (0,_chunk_6DBFFHIP_mjs__WEBPACK_IMPORTED_MODULE_10__.setConfig2)({ flowchart: { arrowMarkerAbsolute: cnf.arrowMarkerAbsolute } });\n    flowDb_default.clear();\n    flowDb_default.setGen(\"gen-2\");\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/mermaid/dist/chunks/mermaid.core/flowDiagram-7ASYPVHJ.mjs\n"));

/***/ })

}]);