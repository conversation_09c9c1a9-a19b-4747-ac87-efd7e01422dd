"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nvar _htmlContext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\nexports[\"default\"] = Document;\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache() {\n    if (typeof WeakMap !== \"function\") return null;\n    var cache = new WeakMap();\n    _getRequireWildcardCache = function() {\n        return cache;\n    };\n    return cache;\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache();\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\n/** Set of pages that have triggered a large data warning on production mode. */ const largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var ref, ref1;\n            return el == null ? void 0 : (ref = el.props) == null ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref1.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var ref;\n    const { assetPrefix , buildManifest , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) == null ? void 0 : ref.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix , scriptLoader , crossOrigin , nextScriptWorkers  } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet  } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var ref, ref2;\n            return hasComponentProps(child) && (child == null ? void 0 : (ref = child.props) == null ? void 0 : (ref2 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref2.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy , src , children: scriptChildren , dangerouslySetInnerHTML , ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, srcProps, scriptProps, {\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            }));\n        }));\n    } catch (err) {\n        if ((0, _isError).default(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin , nonce , ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nfunction getFontLoaderLinks(fontLoaderManifest, dangerousAsPath, assetPrefix = \"\") {\n    if (!fontLoaderManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = fontLoaderManifest.pages[\"/_app\"];\n    const pageFontsEntry = fontLoaderManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = [\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ];\n    // If no font files should preload but there's an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ _react.default.createElement(\"link\", {\n            \"data-next-font\": fontLoaderManifest.pagesUsingSizeAdjust ? \"size-adjust\" : \"\",\n            rel: \"preconnect\",\n            href: \"/\",\n            crossOrigin: \"anonymous\"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: fontFile,\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${encodeURI(fontFile)}`,\n                as: \"font\",\n                type: `font/${ext}`,\n                crossOrigin: \"anonymous\",\n                \"data-next-font\": fontFile.includes(\"-s\") ? \"size-adjust\" : \"\"\n            });\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts  } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , devOnlyCacheBusterQueryString , crossOrigin  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , scriptLoader , crossOrigin  } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader  } = this.context;\n        const { nonce , crossOrigin  } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy , children , dangerouslySetInnerHTML , src , ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            }));\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var ref5, ref3;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (ref5 = c.props) == null ? void 0 : ref5.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>{\n                var ref, ref4;\n                return c == null ? void 0 : (ref = c.props) == null ? void 0 : (ref4 = ref.href) == null ? void 0 : ref4.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (ref3 = c.props) == null ? void 0 : ref3.children) {\n                const newProps = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            }\n            return c;\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , optimizeCss , optimizeFonts , assetPrefix , fontLoaderManifest  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    cssPreloads.push(c);\n                } else {\n                    c && otherHeadElements.push(c);\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var ref;\n                const isReactHelmet = child == null ? void 0 : (ref = child.props) == null ? void 0 : ref[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var ref6;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (ref6 = child.props) == null ? void 0 : ref6.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const fontLoaderLinks = getFontLoaderLinks(fontLoaderManifest, dangerousAsPath, assetPrefix);\n        return /*#__PURE__*/ _react.default.createElement(\"head\", Object.assign({}, getHeadHTMLProps(this.props)), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }), fontLoaderLinks.preconnect, fontLoaderLinks.preload,  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nexports.Head = Head;\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var ref10, ref7, ref8, ref9;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (ref10 = children.find((child)=>child.type === Head)) == null ? void 0 : (ref7 = ref10.props) == null ? void 0 : ref7.children;\n    const bodyChildren = (ref8 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (ref9 = ref8.props) == null ? void 0 : ref9.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var ref;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((ref = child.type) == null ? void 0 : ref.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__ , largePageDataBytes  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n                return (0, _htmlescape).htmlEscapeJsonString(data);\n            }\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                if (false) {}\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape).htmlEscapeJsonString(data);\n        } catch (err) {\n            if ((0, _isError).default(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nexports.NextScript = NextScript;\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale , scriptLoader , __NEXT_DATA__  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", Object.assign({}, props, {\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    }));\n}\nfunction Main() {\n    const { docComponentsRendered  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./node_modules/next/dist/pages/_document.js"));
module.exports = __webpack_exports__;

})();