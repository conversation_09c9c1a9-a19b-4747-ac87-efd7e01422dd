/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./node_modules/next/dist/client/head-manager.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/head-manager.js ***!
  \*******************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = initHeadManager;\nexports.isEqualNode = isEqualNode;\nexports.DOMAttributeNames = void 0;\nfunction initHeadManager() {\n    return {\n        mountedInstances: new Set(),\n        updateHead: (head)=>{\n            const tags = {};\n            head.forEach((h)=>{\n                if (// it won't be inlined. In this case revert to the original behavior\n                h.type === \"link\" && h.props[\"data-optimized-fonts\"]) {\n                    if (document.querySelector(`style[data-href=\"${h.props[\"data-href\"]}\"]`)) {\n                        return;\n                    } else {\n                        h.props.href = h.props[\"data-href\"];\n                        h.props[\"data-href\"] = undefined;\n                    }\n                }\n                const components = tags[h.type] || [];\n                components.push(h);\n                tags[h.type] = components;\n            });\n            const titleComponent = tags.title ? tags.title[0] : null;\n            let title = \"\";\n            if (titleComponent) {\n                const { children  } = titleComponent.props;\n                title = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            if (title !== document.title) document.title = title;\n            [\n                \"meta\",\n                \"base\",\n                \"link\",\n                \"style\",\n                \"script\"\n            ].forEach((type)=>{\n                updateElements(type, tags[type] || []);\n            });\n        }\n    };\n}\nconst DOMAttributeNames = {\n    acceptCharset: \"accept-charset\",\n    className: \"class\",\n    htmlFor: \"for\",\n    httpEquiv: \"http-equiv\",\n    noModule: \"noModule\"\n};\nexports.DOMAttributeNames = DOMAttributeNames;\nfunction reactElementToDOM({ type , props  }) {\n    const el = document.createElement(type);\n    for(const p in props){\n        if (!props.hasOwnProperty(p)) continue;\n        if (p === \"children\" || p === \"dangerouslySetInnerHTML\") continue;\n        // we don't render undefined props to the DOM\n        if (props[p] === undefined) continue;\n        const attr = DOMAttributeNames[p] || p.toLowerCase();\n        if (type === \"script\" && (attr === \"async\" || attr === \"defer\" || attr === \"noModule\")) {\n            el[attr] = !!props[p];\n        } else {\n            el.setAttribute(attr, props[p]);\n        }\n    }\n    const { children , dangerouslySetInnerHTML  } = props;\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || \"\";\n    } else if (children) {\n        el.textContent = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n    }\n    return el;\n}\nfunction isEqualNode(oldTag, newTag) {\n    if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n        const nonce = newTag.getAttribute(\"nonce\");\n        // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n        // be stripped if there is no content security policy response header that includes a nonce.\n        if (nonce && !oldTag.getAttribute(\"nonce\")) {\n            const cloneTag = newTag.cloneNode(true);\n            cloneTag.setAttribute(\"nonce\", \"\");\n            cloneTag.nonce = nonce;\n            return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag);\n        }\n    }\n    return oldTag.isEqualNode(newTag);\n}\nfunction updateElements(type, components) {\n    const headEl = document.getElementsByTagName(\"head\")[0];\n    const headCountEl = headEl.querySelector(\"meta[name=next-head-count]\");\n    if (true) {\n        if (!headCountEl) {\n            console.error(\"Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing\");\n            return;\n        }\n    }\n    const headCount = Number(headCountEl.content);\n    const oldTags = [];\n    for(let i = 0, j = headCountEl.previousElementSibling; i < headCount; i++, j = (j == null ? void 0 : j.previousElementSibling) || null){\n        var ref;\n        if ((j == null ? void 0 : (ref = j.tagName) == null ? void 0 : ref.toLowerCase()) === type) {\n            oldTags.push(j);\n        }\n    }\n    const newTags = components.map(reactElementToDOM).filter((newTag)=>{\n        for(let k = 0, len = oldTags.length; k < len; k++){\n            const oldTag = oldTags[k];\n            if (isEqualNode(oldTag, newTag)) {\n                oldTags.splice(k, 1);\n                return false;\n            }\n        }\n        return true;\n    });\n    oldTags.forEach((t)=>{\n        var ref;\n        return (ref = t.parentNode) == null ? void 0 : ref.removeChild(t);\n    });\n    newTags.forEach((t)=>headEl.insertBefore(t, headCountEl));\n    headCountEl.content = (headCount - oldTags.length + newTags.length).toString();\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head-manager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/head-manager.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.cancelIdleCallback = exports.requestIdleCallback = void 0;\nconst requestIdleCallback = typeof self !== \"undefined\" && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return self.setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nexports.requestIdleCallback = requestIdleCallback;\nconst cancelIdleCallback = typeof self !== \"undefined\" && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nexports.cancelIdleCallback = cancelIdleCallback;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/request-idle-callback.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\n\"use client\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.handleClientScriptLoad = handleClientScriptLoad;\nexports.initScriptLoader = initScriptLoader;\nexports[\"default\"] = void 0;\nvar _extends = (__webpack_require__(/*! @swc/helpers/lib/_extends.js */ \"./node_modules/next/node_modules/@swc/helpers/lib/_extends.js\")[\"default\"]);\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/next/node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _interop_require_wildcard = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_wildcard.js */ \"./node_modules/next/node_modules/@swc/helpers/lib/_interop_require_wildcard.js\")[\"default\"]);\nvar _object_without_properties_loose = (__webpack_require__(/*! @swc/helpers/lib/_object_without_properties_loose.js */ \"./node_modules/next/node_modules/@swc/helpers/lib/_object_without_properties_loose.js\")[\"default\"]);\nvar _reactDom = _interop_require_default(__webpack_require__(/*! react-dom */ \"react-dom\"));\nvar _react = _interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nvar _headManagerContext = __webpack_require__(/*! ../shared/lib/head-manager-context */ \"./head-manager-context\");\nvar _headManager = __webpack_require__(/*! ./head-manager */ \"./node_modules/next/dist/client/head-manager.js\");\nvar _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst ScriptCache = new Map();\nconst LoadCache = new Set();\nconst ignoreProps = [\n    \"onLoad\",\n    \"onReady\",\n    \"dangerouslySetInnerHTML\",\n    \"children\",\n    \"onError\",\n    \"strategy\"\n];\nconst loadScript = (props)=>{\n    const { src , id , onLoad =()=>{} , onReady =null , dangerouslySetInnerHTML , children =\"\" , strategy =\"afterInteractive\" , onError  } = props;\n    const cacheKey = id || src;\n    // Script has already loaded\n    if (cacheKey && LoadCache.has(cacheKey)) {\n        return;\n    }\n    // Contents of this script are already loading/loaded\n    if (ScriptCache.has(src)) {\n        LoadCache.add(cacheKey);\n        // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n        // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n        ScriptCache.get(src).then(onLoad, onError);\n        return;\n    }\n    /** Execute after the script first loaded */ const afterLoad = ()=>{\n        // Run onReady for the first time after load event\n        if (onReady) {\n            onReady();\n        }\n        // add cacheKey to LoadCache when load successfully\n        LoadCache.add(cacheKey);\n    };\n    const el = document.createElement(\"script\");\n    const loadPromise = new Promise((resolve, reject)=>{\n        el.addEventListener(\"load\", function(e) {\n            resolve();\n            if (onLoad) {\n                onLoad.call(this, e);\n            }\n            afterLoad();\n        });\n        el.addEventListener(\"error\", function(e) {\n            reject(e);\n        });\n    }).catch(function(e) {\n        if (onError) {\n            onError(e);\n        }\n    });\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || \"\";\n        afterLoad();\n    } else if (children) {\n        el.textContent = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n        afterLoad();\n    } else if (src) {\n        el.src = src;\n        // do not add cacheKey into LoadCache for remote script here\n        // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n        ScriptCache.set(src, loadPromise);\n    }\n    for (const [k, value] of Object.entries(props)){\n        if (value === undefined || ignoreProps.includes(k)) {\n            continue;\n        }\n        const attr = _headManager.DOMAttributeNames[k] || k.toLowerCase();\n        el.setAttribute(attr, value);\n    }\n    if (strategy === \"worker\") {\n        el.setAttribute(\"type\", \"text/partytown\");\n    }\n    el.setAttribute(\"data-nscript\", strategy);\n    document.body.appendChild(el);\n};\nfunction handleClientScriptLoad(props) {\n    const { strategy =\"afterInteractive\"  } = props;\n    if (strategy === \"lazyOnload\") {\n        window.addEventListener(\"load\", ()=>{\n            (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props));\n        });\n    } else {\n        loadScript(props);\n    }\n}\nfunction loadLazyScript(props) {\n    if (document.readyState === \"complete\") {\n        (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props));\n    } else {\n        window.addEventListener(\"load\", ()=>{\n            (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props));\n        });\n    }\n}\nfunction addBeforeInteractiveToCache() {\n    const scripts = [\n        ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n        ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]')\n    ];\n    scripts.forEach((script)=>{\n        const cacheKey = script.id || script.getAttribute(\"src\");\n        LoadCache.add(cacheKey);\n    });\n}\nfunction initScriptLoader(scriptLoaderItems) {\n    scriptLoaderItems.forEach(handleClientScriptLoad);\n    addBeforeInteractiveToCache();\n}\nfunction Script(props) {\n    const { id , src =\"\" , onLoad =()=>{} , onReady =null , strategy =\"afterInteractive\" , onError  } = props, restProps = _object_without_properties_loose(props, [\n        \"id\",\n        \"src\",\n        \"onLoad\",\n        \"onReady\",\n        \"strategy\",\n        \"onError\"\n    ]);\n    // Context is available only during SSR\n    const { updateScripts , scripts , getIsSsr , appDir , nonce  } = (0, _react).useContext(_headManagerContext.HeadManagerContext);\n    /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */ const hasOnReadyEffectCalled = (0, _react).useRef(false);\n    (0, _react).useEffect(()=>{\n        const cacheKey = id || src;\n        if (!hasOnReadyEffectCalled.current) {\n            // Run onReady if script has loaded before but component is re-mounted\n            if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n                onReady();\n            }\n            hasOnReadyEffectCalled.current = true;\n        }\n    }, [\n        onReady,\n        id,\n        src\n    ]);\n    const hasLoadScriptEffectCalled = (0, _react).useRef(false);\n    (0, _react).useEffect(()=>{\n        if (!hasLoadScriptEffectCalled.current) {\n            if (strategy === \"afterInteractive\") {\n                loadScript(props);\n            } else if (strategy === \"lazyOnload\") {\n                loadLazyScript(props);\n            }\n            hasLoadScriptEffectCalled.current = true;\n        }\n    }, [\n        props,\n        strategy\n    ]);\n    if (strategy === \"beforeInteractive\" || strategy === \"worker\") {\n        if (updateScripts) {\n            scripts[strategy] = (scripts[strategy] || []).concat([\n                _extends({\n                    id,\n                    src,\n                    onLoad,\n                    onReady,\n                    onError\n                }, restProps)\n            ]);\n            updateScripts(scripts);\n        } else if (getIsSsr && getIsSsr()) {\n            // Script has already loaded during SSR\n            LoadCache.add(id || src);\n        } else if (getIsSsr && !getIsSsr()) {\n            loadScript(props);\n        }\n    }\n    // For the app directory, we need React Float to preload these scripts.\n    if (appDir) {\n        // Before interactive scripts need to be loaded by Next.js' runtime instead\n        // of native <script> tags, because they no longer have `defer`.\n        if (strategy === \"beforeInteractive\") {\n            if (!src) {\n                // For inlined scripts, we put the content in `children`.\n                if (restProps.dangerouslySetInnerHTML) {\n                    restProps.children = restProps.dangerouslySetInnerHTML.__html;\n                    delete restProps.dangerouslySetInnerHTML;\n                }\n                return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                            0,\n                            _extends({}, restProps)\n                        ])})`\n                    }\n                });\n            }\n            // @ts-ignore\n            _reactDom.default.preload(src, restProps.integrity ? {\n                as: \"script\",\n                integrity: restProps.integrity\n            } : {\n                as: \"script\"\n            });\n            return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                nonce: nonce,\n                dangerouslySetInnerHTML: {\n                    __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                        src\n                    ])})`\n                }\n            });\n        } else if (strategy === \"afterInteractive\") {\n            if (src) {\n                // @ts-ignore\n                _reactDom.default.preload(src, restProps.integrity ? {\n                    as: \"script\",\n                    integrity: restProps.integrity\n                } : {\n                    as: \"script\"\n                });\n            }\n        }\n    }\n    return null;\n}\nObject.defineProperty(Script, \"__nextScript\", {\n    value: true\n});\nvar _default = Script;\nexports[\"default\"] = _default;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=script.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/script.js\n");

/***/ }),

/***/ "./src/contexts/AuthContext.js":
/*!*************************************!*\
  !*** ./src/contexts/AuthContext.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"AuthProvider\": () => (/* binding */ AuthProvider),\n/* harmony export */   \"useAuth\": () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst AuthProvider = ({ children  })=>{\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoginModalOpen, setIsLoginModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const openLoginModal = ()=>setIsLoginModalOpen(true);\n    const closeLoginModal = ()=>setIsLoginModalOpen(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const storedLoginStatus = localStorage.getItem(\"isLoggedIn\");\n        setIsLoggedIn(storedLoginStatus === \"true\");\n        const storedUser = localStorage.getItem(\"user\");\n        setUser(!!storedUser && storedUser !== \"undefined\" ? JSON.parse(storedUser) : null);\n    }, []);\n    const logon = (user)=>{\n        localStorage.setItem(\"isLoggedIn\", \"true\");\n        localStorage.setItem(\"user\", JSON.stringify(user));\n        setUser(user);\n        setIsLoggedIn(true);\n        setIsLoginModalOpen(false);\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"user\");\n        setUser(null);\n        setIsLoggedIn(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isLoggedIn,\n            logon,\n            logout,\n            isLoginModalOpen,\n            openLoginModal,\n            closeLoginModal,\n            user\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/wise-card/src/contexts/AuthContext.js\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/AuthContext.js\n");

/***/ }),

/***/ "./src/lib/gtag.js":
/*!*************************!*\
  !*** ./src/lib/gtag.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"GA_MEASUREMENT_ID\": () => (/* binding */ GA_MEASUREMENT_ID),\n/* harmony export */   \"event\": () => (/* binding */ event),\n/* harmony export */   \"pageview\": () => (/* binding */ pageview)\n/* harmony export */ });\n// Google Analytics 测量 ID\nconst GA_MEASUREMENT_ID = \"G-RYTCZEQK0W\" // 替换为你的 GA 测量 ID\n;\n// 页面访问追踪\nconst pageview = (url)=>{\n    window.gtag(\"config\", GA_MEASUREMENT_ID, {\n        page_path: url\n    });\n};\n// 事件追踪\nconst event = ({ action , category , label , value  })=>{\n    window.gtag(\"event\", action, {\n        event_category: category,\n        event_label: label,\n        value: value\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2d0YWcuanMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEseUJBQXlCO0FBQ2xCLE1BQU1BLG9CQUFvQixlQUFlLGlCQUFpQjtDQUFsQjtBQUUvQyxTQUFTO0FBQ0YsTUFBTUMsV0FBVyxDQUFDQyxNQUFRO0lBQy9CQyxPQUFPQyxJQUFJLENBQUMsVUFBVUosbUJBQW1CO1FBQ3ZDSyxXQUFXSDtJQUNiO0FBQ0YsRUFBQztBQUVELE9BQU87QUFDQSxNQUFNSSxRQUFRLENBQUMsRUFBRUMsT0FBTSxFQUFFQyxTQUFRLEVBQUVDLE1BQUssRUFBRUMsTUFBSyxFQUFFLEdBQUs7SUFDM0RQLE9BQU9DLElBQUksQ0FBQyxTQUFTRyxRQUFRO1FBQzNCSSxnQkFBZ0JIO1FBQ2hCSSxhQUFhSDtRQUNiQyxPQUFPQTtJQUNUO0FBQ0YsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dpc2UtY2FyZHMvLi9zcmMvbGliL2d0YWcuanM/ZWQzNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHb29nbGUgQW5hbHl0aWNzIOa1i+mHjyBJRFxuZXhwb3J0IGNvbnN0IEdBX01FQVNVUkVNRU5UX0lEID0gJ0ctUllUQ1pFUUswVycgLy8g5pu/5o2i5Li65L2g55qEIEdBIOa1i+mHjyBJRFxuXG4vLyDpobXpnaLorr/pl67ov73ouKpcbmV4cG9ydCBjb25zdCBwYWdldmlldyA9ICh1cmwpID0+IHtcbiAgd2luZG93Lmd0YWcoJ2NvbmZpZycsIEdBX01FQVNVUkVNRU5UX0lELCB7XG4gICAgcGFnZV9wYXRoOiB1cmwsXG4gIH0pXG59XG5cbi8vIOS6i+S7tui/vei4qlxuZXhwb3J0IGNvbnN0IGV2ZW50ID0gKHsgYWN0aW9uLCBjYXRlZ29yeSwgbGFiZWwsIHZhbHVlIH0pID0+IHtcbiAgd2luZG93Lmd0YWcoJ2V2ZW50JywgYWN0aW9uLCB7XG4gICAgZXZlbnRfY2F0ZWdvcnk6IGNhdGVnb3J5LFxuICAgIGV2ZW50X2xhYmVsOiBsYWJlbCxcbiAgICB2YWx1ZTogdmFsdWUsXG4gIH0pXG59ICJdLCJuYW1lcyI6WyJHQV9NRUFTVVJFTUVOVF9JRCIsInBhZ2V2aWV3IiwidXJsIiwid2luZG93IiwiZ3RhZyIsInBhZ2VfcGF0aCIsImV2ZW50IiwiYWN0aW9uIiwiY2F0ZWdvcnkiLCJsYWJlbCIsInZhbHVlIiwiZXZlbnRfY2F0ZWdvcnkiLCJldmVudF9sYWJlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/lib/gtag.js\n");

/***/ }),

/***/ "./src/pages/_app.js":
/*!***************************!*\
  !*** ./src/pages/_app.js ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./src/contexts/AuthContext.js\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _styles_markdown_styles_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../styles/markdown-styles.css */ \"./src/styles/markdown-styles.css\");\n/* harmony import */ var _styles_markdown_styles_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_markdown_styles_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_CardGenerator_InputForm_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/CardGenerator/InputForm.css */ \"./src/components/CardGenerator/InputForm.css\");\n/* harmony import */ var _components_CardGenerator_InputForm_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_components_CardGenerator_InputForm_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _styles_node_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../styles/node.css */ \"./src/styles/node.css\");\n/* harmony import */ var _styles_node_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_styles_node_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _styles_simple_floatingedges_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../styles/simple-floatingedges.css */ \"./src/styles/simple-floatingedges.css\");\n/* harmony import */ var _styles_simple_floatingedges_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_styles_simple_floatingedges_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/config */ \"next/config\");\n/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _lib_gtag__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../lib/gtag */ \"./src/lib/gtag.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction MyApp({ Component , pageProps  }) {\n    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"common\");\n    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_10___default()().publicRuntimeConfig;\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    // 使用 pageProps 中的自定义 meta 数据，如果没有则使用默认值\n    const metaTitle = pageProps.metaTitle || t(\"meta.title\");\n    const metaDescription = pageProps.metaDescription || t(\"meta.description\");\n    const metaImage = pageProps.metaImage || `https://www.funblocks.net/assets/img/portfolio/fullsize/ai_insights.png`;\n    const metaUrl = pageProps.metaUrl || `https://www.funblocks.net${basePath}`;\n    (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(()=>{\n        // 监听路由变化，发送页面访问数据\n        const handleRouteChange = (url)=>{\n            _lib_gtag__WEBPACK_IMPORTED_MODULE_14__.pageview(url);\n        };\n        router.events.on(\"routeChangeComplete\", handleRouteChange);\n        return ()=>{\n            router.events.off(\"routeChangeComplete\", handleRouteChange);\n        };\n    }, [\n        router.events\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_13___default()), {\n                strategy: \"afterInteractive\",\n                src: `https://www.googletagmanager.com/gtag/js?id=${_lib_gtag__WEBPACK_IMPORTED_MODULE_14__.GA_MEASUREMENT_ID}`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_13___default()), {\n                id: \"gtag-init\",\n                strategy: \"afterInteractive\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', '${_lib_gtag__WEBPACK_IMPORTED_MODULE_14__.GA_MEASUREMENT_ID}', {\n              page_path: window.location.pathname,\n            });\n          `\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_9___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: `${basePath}/icon.png`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: t(\"meta.keywords\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: metaTitle\n                    }, \"title\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: metaDescription\n                    }, \"description\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: metaTitle\n                    }, \"og:title\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: metaDescription\n                    }, \"og:description\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: metaImage\n                    }, \"og:image\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:url\",\n                        content: metaUrl\n                    }, \"og:url\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, \"twitter:card\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: metaTitle\n                    }, \"twitter:title\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: metaDescription\n                    }, \"twitter:description\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: metaImage\n                    }, \"twitter:image\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: metaUrl\n                    }, \"canonical\", false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ChakraProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                        ...pageProps\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/wise-card/src/pages/_app.js\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n// 添加 getInitialProps 以支持服务端渲染\n// MyApp.getInitialProps = async ({ Component, ctx }) => {\n//   let pageProps = {}\n//   if (Component.getInitialProps) {\n//     pageProps = await Component.getInitialProps(ctx)\n//   }\n//   return { pageProps }\n// }\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.appWithTranslation)(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.js\n");

/***/ }),

/***/ "./src/components/CardGenerator/InputForm.css":
/*!****************************************************!*\
  !*** ./src/components/CardGenerator/InputForm.css ***!
  \****************************************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/markdown-styles.css":
/*!****************************************!*\
  !*** ./src/styles/markdown-styles.css ***!
  \****************************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/node.css":
/*!*****************************!*\
  !*** ./src/styles/node.css ***!
  \*****************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/simple-floatingedges.css":
/*!*********************************************!*\
  !*** ./src/styles/simple-floatingedges.css ***!
  \*********************************************/
/***/ (() => {



/***/ }),

/***/ "./node_modules/next/node_modules/@swc/helpers/lib/_extends.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next/node_modules/@swc/helpers/lib/_extends.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _extends;\n    }\n}));\nfunction extends_() {\n    extends_ = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return extends_.apply(this, arguments);\n}\nfunction _extends() {\n    return extends_.apply(this, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9fZXh0ZW5kcy5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDJDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQSx1QkFBdUIsc0JBQXNCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93aXNlLWNhcmRzLy4vbm9kZV9tb2R1bGVzL25leHQvbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9saWIvX2V4dGVuZHMuanM/Zjc1MyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImRlZmF1bHRcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9leHRlbmRzO1xuICAgIH1cbn0pO1xuZnVuY3Rpb24gZXh0ZW5kc18oKSB7XG4gICAgZXh0ZW5kc18gPSBPYmplY3QuYXNzaWduIHx8IGZ1bmN0aW9uKHRhcmdldCkge1xuICAgICAgICBmb3IodmFyIGkgPSAxOyBpIDwgYXJndW1lbnRzLmxlbmd0aDsgaSsrKXtcbiAgICAgICAgICAgIHZhciBzb3VyY2UgPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICBmb3IodmFyIGtleSBpbiBzb3VyY2Upe1xuICAgICAgICAgICAgICAgIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoc291cmNlLCBrZXkpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRhcmdldFtrZXldID0gc291cmNlW2tleV07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0YXJnZXQ7XG4gICAgfTtcbiAgICByZXR1cm4gZXh0ZW5kc18uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn1cbmZ1bmN0aW9uIF9leHRlbmRzKCkge1xuICAgIHJldHVybiBleHRlbmRzXy5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/node_modules/@swc/helpers/lib/_extends.js\n");

/***/ }),

/***/ "./node_modules/next/node_modules/@swc/helpers/lib/_interop_require_default.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/node_modules/@swc/helpers/lib/_interop_require_default.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _interopRequireDefault;\n    }\n}));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9faW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQuanMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRiwyQ0FBMEM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2lzZS1jYXJkcy8uL25vZGVfbW9kdWxlcy9uZXh0L25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvbGliL19pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5qcz9iYTE5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiZGVmYXVsdFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX2ludGVyb3BSZXF1aXJlRGVmYXVsdDtcbiAgICB9XG59KTtcbmZ1bmN0aW9uIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQob2JqKSB7XG4gICAgcmV0dXJuIG9iaiAmJiBvYmouX19lc01vZHVsZSA/IG9iaiA6IHtcbiAgICAgICAgZGVmYXVsdDogb2JqXG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/node_modules/@swc/helpers/lib/_interop_require_default.js\n");

/***/ }),

/***/ "./node_modules/next/node_modules/@swc/helpers/lib/_interop_require_wildcard.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/node_modules/@swc/helpers/lib/_interop_require_wildcard.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _interopRequireWildcard;\n    }\n}));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/node_modules/@swc/helpers/lib/_interop_require_wildcard.js\n");

/***/ }),

/***/ "./node_modules/next/node_modules/@swc/helpers/lib/_object_without_properties_loose.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/node_modules/@swc/helpers/lib/_object_without_properties_loose.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _objectWithoutPropertiesLoose;\n    }\n}));\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9fb2JqZWN0X3dpdGhvdXRfcHJvcGVydGllc19sb29zZS5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDJDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHVCQUF1QjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93aXNlLWNhcmRzLy4vbm9kZV9tb2R1bGVzL25leHQvbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9saWIvX29iamVjdF93aXRob3V0X3Byb3BlcnRpZXNfbG9vc2UuanM/OTc4MCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImRlZmF1bHRcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlO1xuICAgIH1cbn0pO1xuZnVuY3Rpb24gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2Uoc291cmNlLCBleGNsdWRlZCkge1xuICAgIGlmIChzb3VyY2UgPT0gbnVsbCkgcmV0dXJuIHt9O1xuICAgIHZhciB0YXJnZXQgPSB7fTtcbiAgICB2YXIgc291cmNlS2V5cyA9IE9iamVjdC5rZXlzKHNvdXJjZSk7XG4gICAgdmFyIGtleSwgaTtcbiAgICBmb3IoaSA9IDA7IGkgPCBzb3VyY2VLZXlzLmxlbmd0aDsgaSsrKXtcbiAgICAgICAga2V5ID0gc291cmNlS2V5c1tpXTtcbiAgICAgICAgaWYgKGV4Y2x1ZGVkLmluZGV4T2Yoa2V5KSA+PSAwKSBjb250aW51ZTtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICB9XG4gICAgcmV0dXJuIHRhcmdldDtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/node_modules/@swc/helpers/lib/_object_without_properties_loose.js\n");

/***/ }),

/***/ "./node_modules/next/script.js":
/*!*************************************!*\
  !*** ./node_modules/next/script.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/client/script */ \"./node_modules/next/dist/client/script.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9zY3JpcHQuanMuanMiLCJtYXBwaW5ncyI6IkFBQUEsNkdBQWdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2lzZS1jYXJkcy8uL25vZGVfbW9kdWxlcy9uZXh0L3NjcmlwdC5qcz9lNGJhIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2NsaWVudC9zY3JpcHQnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/script.js\n");

/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next");

/***/ }),

/***/ "next/config":
/*!******************************!*\
  !*** external "next/config" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/config");

/***/ }),

/***/ "./head-manager-context":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/head-manager-context.js" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head-manager-context.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "next/router":
/*!******************************!*\
  !*** external "next/router" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/router");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@chakra-ui/react":
/*!***********************************!*\
  !*** external "@chakra-ui/react" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@chakra-ui/react");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./src/pages/_app.js"));
module.exports = __webpack_exports__;

})();