{"name": "wise-cards", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "postbuild": "next-sitemap", "lint": "next lint"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.5.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.3.1", "@mui/styled-engine": "^6.3.1", "@react-oauth/google": "^0.12.1", "@styled-icons/bootstrap": "^10.47.0", "@styled-icons/entypo": "^10.46.0", "@styled-icons/fluentui-system-regular": "^10.47.0", "@styled-icons/material": "^10.47.0", "@xyflow/react": "^12.3.6", "framer-motion": "^10.0.1", "html-to-image": "^1.11.11", "i18next": "^23.16.0", "immutability-helper": "^3.1.1", "mermaid": "^11.4.0", "next": "13.2.3", "next-i18next": "^15.3.1", "qrcode.react": "^4.0.1", "react": "18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "18.2.0", "react-google-recaptcha": "^3.1.0", "react-i18next": "^15.0.3", "react-icons": "^5.3.0", "react-intersection-observer": "^9.13.1", "react-markdown": "^9.0.1", "react-share": "^5.1.0", "react-use": "^17.6.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "seedrandom": "^3.0.5", "styled-components": "^6.1.13", "url-regex": "^5.0.0", "uuid": "^10.0.0"}, "devDependencies": {"next-sitemap": "^4.2.3"}}