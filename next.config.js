const { i18n } = require('./next-i18next.config')

/** @type {import('next').NextConfig} */
const nextConfig = () => ({
  reactStrictMode: false,
  basePath: process.env.BASE_PATH || '/aitools',
  assetPrefix: '/aitools',
  i18n,
  env: {
    NEXT_PUBLIC_API_URL:
      process.env.NODE_ENV === 'production'
        ? 'https://www.funblocks.net/service'
        :
        'http://localhost:50058',
    PRICING_URL:
      process.env.NODE_ENV === 'production'
        ? 'https://app.funblocks.net/#/aiplans'
        :
        'http://localhost:3001/#/aiplans',
  },
  images: {
    domains: [
      'www.google.com',
      'www.gstatic.com',
      'www.recaptcha.net'
    ],
  },
  // Add publicRuntimeConfig
  publicRuntimeConfig: {
    basePath: '/aitools',
    basePathGraphics: '/aitools/graphics',
    basePathMindmap:'/aitools/mindmap'
  },
})

module.exports = nextConfig
