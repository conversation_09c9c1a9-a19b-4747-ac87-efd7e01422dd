// module.exports = {
//     siteUrl: 'https://www.funblocks.net/aitools',
//     generateRobotsTxt: true, // 可选，同时生成robots.txt
//     changefreq: 'daily',     // 可选，更新频率
//     priority: 0.7,           // 可选，优先级
//     sitemapSize: 5000,       // 可选，限制每个sitemap的URL数量
//   }

// const { fetchShowcases } = require("./src/utils/data");


module.exports = {
    siteUrl: 'https://www.funblocks.net/aitools',
    generateRobotsTxt: true,
    // 静态页面列表
    exclude: ['/admin/*', '/api/*'], // 排除不需要索引的路径

    // 自定义动态路由的转换函数
    transform: async (config, path) => {
        // 返回默认配置用于静态页面
        return {
            loc: path,
            changefreq: path.includes('/aitools/collections/') ? 'weekly' : 'monthly', // 博客文章更新频率更高
            priority: path === '/aitools' ? 1.0 : path.includes('/collections/') ? 0.8 : 0.5, // 首页优先级最高，其次是博客文章
            lastmod: new Date().toISOString(),
        };
    },

    // 添加额外的动态生成的URL（通过API获取的博客文章）
    additionalPaths: async (config) => {
        const result = ['/collections/Movie', '/collections/Mindmap', '/collections/Reading'];

        // 获取博客文章（可以从数据库、API或本地文件获取）
        // 这里需要你实现一个获取所有博客文章的函数
        //   const posts = await fetch('https://www.funblocks.net/aitools/collections/Mindmap')
        //     .then(res => res.json())
        //     .catch(() => []);\

        // const collections = await fetchShowcases(APP_TYPE.mindmap, 'book', 500);
        let response = await fetch(`https://www.funblocks.net/service/ai/showcase_artifacts?app=Mindmap&mode=book&pageNum=0&pageSize=500`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let data = await response.json();

        if (data?.data?.length) {
            for (const artifact of data.data) {
                result.push({
                    loc: `/share/${artifact.app}/${artifact._id}/${artifact.title?.replaceAll(' ', '-')}`,
                    changefreq: 'monthly',
                    priority: 0.5,
                    lastmod: new Date(artifact.updatedAt || Date.now()).toISOString(),
                });
            }
        }

        response = await fetch(`https://www.funblocks.net/service/ai/showcase_artifacts?app=Mindmap&mode=movie&pageNum=0&pageSize=500`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        data = await response.json();

        if (data?.data?.length) {
            for (const artifact of data.data) {
                result.push({
                    loc: `/share/${artifact.app}/${artifact._id}/${artifact.title?.replaceAll(' ', '-')}`,
                    changefreq: 'monthly',
                    priority: 0.5,
                    lastmod: new Date(artifact.updatedAt || Date.now()).toISOString(),
                });
            }
        }

        // 为每篇文章生成URL配置


        return result;
    },
};